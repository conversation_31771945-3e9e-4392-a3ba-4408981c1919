"""
Logging configuration and utilities for AI Crypto Trading Bot
"""

import os
import sys
import logging
import logging.config
from pathlib import Path
from typing import Optional
from loguru import logger
from .config import settings


class InterceptHandler(logging.Handler):
    """Intercept standard logging and redirect to loguru"""
    
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


def setup_logging():
    """Setup logging configuration"""
    
    # Create logs directory if it doesn't exist
    log_dir = Path(settings.log_file_path).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Remove default loguru handler
    logger.remove()
    
    # Add console handler
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True,
    )
    
    # Add file handler
    logger.add(
        settings.log_file_path,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation=settings.log_rotation,
        retention=settings.log_retention,
        compression="zip",
        serialize=settings.log_format == "json",
    )
    
    # Add error file handler
    error_log_path = str(Path(settings.log_file_path).with_suffix('.error.log'))
    logger.add(
        error_log_path,
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="1 day",
        retention="30 days",
        compression="zip",
    )
    
    # Intercept standard logging
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    # Set specific loggers
    for logger_name in ["uvicorn", "uvicorn.access", "fastapi", "sqlalchemy"]:
        logging.getLogger(logger_name).handlers = [InterceptHandler()]
    
    return logger


class TradingLogger:
    """Specialized logger for trading operations"""
    
    def __init__(self, name: str = "trading"):
        self.logger = logger.bind(component=name)
    
    def trade_executed(self, symbol: str, side: str, quantity: float, price: float, order_id: str):
        """Log trade execution"""
        self.logger.info(
            "Trade executed",
            extra={
                "event": "trade_executed",
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "price": price,
                "order_id": order_id
            }
        )
    
    def trade_failed(self, symbol: str, side: str, reason: str, error: Optional[str] = None):
        """Log trade failure"""
        self.logger.error(
            "Trade failed",
            extra={
                "event": "trade_failed",
                "symbol": symbol,
                "side": side,
                "reason": reason,
                "error": error
            }
        )
    
    def signal_generated(self, symbol: str, signal: str, confidence: float, indicators: dict):
        """Log trading signal generation"""
        self.logger.info(
            "Trading signal generated",
            extra={
                "event": "signal_generated",
                "symbol": symbol,
                "signal": signal,
                "confidence": confidence,
                "indicators": indicators
            }
        )
    
    def risk_check(self, symbol: str, action: str, risk_level: float, passed: bool):
        """Log risk management checks"""
        level = "info" if passed else "warning"
        getattr(self.logger, level)(
            "Risk check performed",
            extra={
                "event": "risk_check",
                "symbol": symbol,
                "action": action,
                "risk_level": risk_level,
                "passed": passed
            }
        )
    
    def model_prediction(self, symbol: str, prediction: float, confidence: float, model_name: str):
        """Log AI model predictions"""
        self.logger.info(
            "AI model prediction",
            extra={
                "event": "model_prediction",
                "symbol": symbol,
                "prediction": prediction,
                "confidence": confidence,
                "model_name": model_name
            }
        )
    
    def portfolio_update(self, total_value: float, pnl: float, positions: int):
        """Log portfolio updates"""
        self.logger.info(
            "Portfolio updated",
            extra={
                "event": "portfolio_update",
                "total_value": total_value,
                "pnl": pnl,
                "positions": positions
            }
        )
    
    def error(self, message: str, error: Exception, context: Optional[dict] = None):
        """Log errors with context"""
        self.logger.error(
            message,
            extra={
                "event": "error",
                "error_type": type(error).__name__,
                "error_message": str(error),
                "context": context or {}
            }
        )


class PerformanceLogger:
    """Logger for performance metrics"""
    
    def __init__(self):
        self.logger = logger.bind(component="performance")
    
    def api_call(self, endpoint: str, duration: float, status: str):
        """Log API call performance"""
        self.logger.info(
            "API call completed",
            extra={
                "event": "api_call",
                "endpoint": endpoint,
                "duration": duration,
                "status": status
            }
        )
    
    def model_training(self, model_name: str, duration: float, accuracy: float):
        """Log model training performance"""
        self.logger.info(
            "Model training completed",
            extra={
                "event": "model_training",
                "model_name": model_name,
                "duration": duration,
                "accuracy": accuracy
            }
        )
    
    def data_processing(self, operation: str, records: int, duration: float):
        """Log data processing performance"""
        self.logger.info(
            "Data processing completed",
            extra={
                "event": "data_processing",
                "operation": operation,
                "records": records,
                "duration": duration
            }
        )


# Initialize loggers
setup_logging()
trading_logger = TradingLogger()
performance_logger = PerformanceLogger()

# Export main logger and specialized loggers
__all__ = ["logger", "trading_logger", "performance_logger", "setup_logging"]
