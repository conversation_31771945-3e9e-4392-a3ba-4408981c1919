#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
المضارب النهائي وصياد الفرص
Ultimate Scalper & Opportunity Hunter

نظام مضاربة قوي وذكي:
- تحليل شامل لجميع العملات
- مضاربة ذكية متقدمة
- صيد الفرص التلقائي
- إدارة مخاطر ذكية
"""

import requests
import json
import time
import hashlib
import hmac
import base64
import threading
from datetime import datetime
from collections import defaultdict

# KuCoin API Configuration
API_KEY = "686a4e782301b10001e7457c"
SECRET_KEY = "61718954-dc69-4b89-b21c-dff5b80fff15"
PASSPHRASE = "Eslam*17*3*1999"
BASE_URL = "https://api.kucoin.com"

class UltimateScalper:
    """المضارب النهائي"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 5
        self.price_history = defaultdict(list)
        self.opportunities = []
        self.hunting = False
        print("🚀 Ultimate Scalper initialized")
    
    def _sign_request(self, timestamp, method, endpoint, body=""):
        """توقيع الطلب"""
        message = timestamp + method.upper() + endpoint + body
        signature = base64.b64encode(
            hmac.new(SECRET_KEY.encode(), message.encode(), hashlib.sha256).digest()
        ).decode()
        
        passphrase = base64.b64encode(
            hmac.new(SECRET_KEY.encode(), PASSPHRASE.encode(), hashlib.sha256).digest()
        ).decode()
        
        return {
            "KC-API-KEY": API_KEY,
            "KC-API-SIGN": signature,
            "KC-API-TIMESTAMP": timestamp,
            "KC-API-PASSPHRASE": passphrase,
            "KC-API-KEY-VERSION": "2",
            "Content-Type": "application/json"
        }
    
    def _api_call(self, method, endpoint, data=None, signed=False):
        """استدعاء API"""
        url = BASE_URL + endpoint
        headers = {}
        
        if signed:
            timestamp = str(int(time.time() * 1000))
            body = json.dumps(data) if data else ""
            headers = self._sign_request(timestamp, method, endpoint, body)
            data = body if body else None
        
        try:
            response = self.session.request(method, url, data=data, headers=headers)
            result = response.json()
            
            if response.status_code == 200 and result.get("code") == "200000":
                return result.get("data")
            else:
                return None
        except:
            return None
    
    def get_price(self, symbol):
        """الحصول على السعر"""
        ticker = self._api_call("GET", f"/api/v1/market/stats?symbol={symbol}")
        return float(ticker["last"]) if ticker else None
    
    def get_balance(self, currency="USDT"):
        """الحصول على الرصيد"""
        accounts = self._api_call("GET", "/api/v1/accounts", signed=True)
        if accounts:
            for account in accounts:
                if account["currency"] == currency and account["type"] == "trade":
                    return float(account["available"])
        return 0.0
    
    def buy_market(self, symbol, amount):
        """شراء بسعر السوق"""
        data = {
            "clientOid": f"ultimate_buy_{int(time.time())}",
            "symbol": symbol,
            "side": "buy",
            "type": "market",
            "funds": str(amount)
        }
        
        result = self._api_call("POST", "/api/v1/orders", data=data, signed=True)
        return result.get('orderId') if result else None
    
    def sell_market(self, symbol, amount):
        """بيع بسعر السوق"""
        data = {
            "clientOid": f"ultimate_sell_{int(time.time())}",
            "symbol": symbol,
            "side": "sell",
            "type": "market",
            "size": str(amount)
        }
        
        result = self._api_call("POST", "/api/v1/orders", data=data, signed=True)
        return result.get('orderId') if result else None
    
    def analyze_coin(self, symbol, ticker_data):
        """تحليل ذكي للعملة"""
        try:
            current_price = float(ticker_data['last'])
            change_rate = float(ticker_data['changeRate']) * 100
            volume_24h = float(ticker_data['vol'])
            high_24h = float(ticker_data['high'])
            low_24h = float(ticker_data['low'])
            
            score = 0
            signals = []
            
            # 1. Price Movement Analysis (40 points max)
            if abs(change_rate) > 10:
                score += 40
                signals.append(f"Extreme move: {change_rate:+.1f}%")
            elif abs(change_rate) > 5:
                score += 25
                signals.append(f"Strong move: {change_rate:+.1f}%")
            elif abs(change_rate) > 2:
                score += 15
                signals.append(f"Good move: {change_rate:+.1f}%")
            
            # 2. Volume Analysis (30 points max)
            if volume_24h > 1000000:  # High volume
                score += 30
                signals.append("Very high volume")
            elif volume_24h > 100000:
                score += 20
                signals.append("High volume")
            elif volume_24h > 10000:
                score += 10
                signals.append("Good volume")
            
            # 3. Price Range Analysis (20 points max)
            if high_24h > 0 and low_24h > 0:
                price_range = ((high_24h - low_24h) / low_24h) * 100
                if price_range > 15:
                    score += 20
                    signals.append(f"Wide range: {price_range:.1f}%")
                elif price_range > 8:
                    score += 15
                    signals.append(f"Good range: {price_range:.1f}%")
                elif price_range > 4:
                    score += 10
                    signals.append(f"Decent range: {price_range:.1f}%")
            
            # 4. Price Position (10 points max)
            if high_24h > 0 and low_24h > 0:
                position = ((current_price - low_24h) / (high_24h - low_24h)) * 100
                if position > 80:
                    signals.append("Near high")
                    score += 5
                elif position < 20:
                    signals.append("Near low")
                    score += 5
                else:
                    signals.append("Mid-range")
                    score += 10
            
            # Determine signal type
            if score >= 70:
                signal_type = "STRONG_BUY" if change_rate > 0 else "STRONG_SELL"
            elif score >= 50:
                signal_type = "BUY" if change_rate > 0 else "SELL"
            elif score >= 30:
                signal_type = "WEAK_BUY" if change_rate > 0 else "WEAK_SELL"
            else:
                signal_type = "HOLD"
            
            # Risk level
            if abs(change_rate) > 8 or score >= 80:
                risk_level = "HIGH"
            elif abs(change_rate) > 4 or score >= 50:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"
            
            return {
                'symbol': symbol,
                'signal_type': signal_type,
                'score': score,
                'confidence': min(95, score),
                'current_price': current_price,
                'change_24h': change_rate,
                'volume_24h': volume_24h,
                'risk_level': risk_level,
                'signals': signals,
                'reasoning': " | ".join(signals[:2]),
                'timestamp': datetime.now().strftime("%H:%M:%S")
            }
            
        except Exception as e:
            return None
    
    def scan_all_markets(self):
        """مسح جميع الأسواق"""
        print("🔍 Scanning ALL markets...")
        
        # Get all tickers
        all_tickers = self._api_call("GET", "/api/v1/market/allTickers")
        
        if not all_tickers or "ticker" not in all_tickers:
            print("❌ Failed to get market data")
            return []
        
        opportunities = []
        total_coins = 0
        
        for ticker in all_tickers["ticker"]:
            symbol = ticker["symbol"]
            
            # Focus on USDT pairs only
            if not symbol.endswith("-USDT"):
                continue
            
            # Skip stablecoins
            base_currency = symbol.replace("-USDT", "")
            if base_currency in ["USDT", "USDC", "BUSD", "DAI", "TUSD", "FDUSD"]:
                continue
            
            total_coins += 1
            
            # Analyze coin
            analysis = self.analyze_coin(symbol, ticker)
            if analysis and analysis['score'] >= 20:  # Minimum threshold
                opportunities.append(analysis)
        
        # Sort by score
        opportunities.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"\n📊 MARKET SCAN RESULTS ({total_coins} coins scanned):")
        print("=" * 110)
        print(f"{'#':<3} {'Symbol':<15} {'Signal':<12} {'Score':<5} {'Price':<12} {'Change':<8} {'Volume':<10} {'Risk':<6} {'Reasoning'}")
        print("=" * 110)
        
        for i, opp in enumerate(opportunities[:25]):  # Top 25
            volume_k = opp['volume_24h'] / 1000
            print(f"{i+1:<3} {opp['symbol']:<15} {opp['signal_type']:<12} "
                  f"{opp['score']:<5} ${opp['current_price']:<11.6f} "
                  f"{opp['change_24h']:+7.2f}% {volume_k:>8.0f}K {opp['risk_level']:<6} {opp['reasoning'][:35]}")
        
        self.opportunities = opportunities
        return opportunities

    def ultimate_scalp(self, symbol, amount, analysis=None):
        """مضاربة نهائية متقدمة"""
        print(f"🚀 Ultimate scalping {symbol} with ${amount}")

        if analysis:
            print(f"📊 Analysis: {analysis['signal_type']} (Score: {analysis['score']}) - {analysis['reasoning']}")

        # Get current price
        current_price = self.get_price(symbol)
        if not current_price:
            print("❌ Cannot get current price")
            return False

        # Check balance
        usdt_balance = self.get_balance("USDT")
        if usdt_balance < amount:
            print(f"❌ Insufficient USDT: ${usdt_balance:.2f}")
            return False

        # Dynamic targets based on analysis
        if analysis:
            if analysis['score'] >= 80:
                profit_target = 0.012  # 1.2% for very high confidence
                stop_loss = 0.005      # 0.5% stop loss
                timeout = 900          # 15 minutes
            elif analysis['score'] >= 60:
                profit_target = 0.008  # 0.8% for high confidence
                stop_loss = 0.004      # 0.4% stop loss
                timeout = 600          # 10 minutes
            elif analysis['score'] >= 40:
                profit_target = 0.006  # 0.6% for medium confidence
                stop_loss = 0.003      # 0.3% stop loss
                timeout = 450          # 7.5 minutes
            else:
                profit_target = 0.004  # 0.4% for low confidence
                stop_loss = 0.002      # 0.2% stop loss
                timeout = 300          # 5 minutes
        else:
            profit_target = 0.005
            stop_loss = 0.0025
            timeout = 300

        print(f"💰 Entry: ${current_price:.6f} | Target: {profit_target*100:.1f}% | Stop: {stop_loss*100:.1f}%")

        # Execute buy order
        buy_order = self.buy_market(symbol, amount)
        if not buy_order:
            print("❌ Buy order failed")
            return False

        print(f"✅ Position opened! Order: {buy_order}")

        # Wait for order to fill
        time.sleep(3)

        # Calculate crypto amount
        crypto_amount = amount / current_price

        # Set targets
        target_price = current_price * (1 + profit_target)
        stop_price = current_price * (1 - stop_loss)

        print(f"🎯 Target: ${target_price:.6f} | Stop: ${stop_price:.6f}")

        # Advanced exit strategy with trailing stop
        start_time = time.time()
        best_price = current_price
        trailing_stop = stop_price
        check_count = 0

        while time.time() - start_time < timeout:
            new_price = self.get_price(symbol)
            if not new_price:
                time.sleep(2)
                continue

            check_count += 1

            # Update best price and trailing stop
            if new_price > best_price:
                best_price = new_price
                # Dynamic trailing stop
                trail_distance = stop_loss * (1 + (new_price - current_price) / current_price)
                new_trailing = best_price * (1 - trail_distance)
                if new_trailing > trailing_stop:
                    trailing_stop = new_trailing

            # Progress indicator
            progress = (time.time() - start_time) / timeout * 100
            profit_pct = ((new_price - current_price) / current_price) * 100

            print(f"📊 ${new_price:.6f} | P&L: {profit_pct:+.2f}% | Best: ${best_price:.6f} | Trail: ${trailing_stop:.6f} | {progress:.0f}%", end="\r")

            # Check exit conditions
            if new_price >= target_price:
                print(f"\n🎉 TARGET ACHIEVED! Selling at ${new_price:.6f}")
                sell_order = self.sell_market(symbol, crypto_amount * 0.995)  # Account for fees
                if sell_order:
                    profit = (new_price - current_price) * crypto_amount
                    print(f"💰 Profit: ${profit:.3f} | ROI: {(profit/amount)*100:.2f}% | Order: {sell_order}")
                    return True
                break

            elif new_price <= trailing_stop:
                print(f"\n🛑 TRAILING STOP! Selling at ${new_price:.6f}")
                sell_order = self.sell_market(symbol, crypto_amount * 0.995)
                if sell_order:
                    result = (new_price - current_price) * crypto_amount
                    roi = (result/amount)*100
                    if result > 0:
                        print(f"💰 Small profit: ${result:.3f} | ROI: {roi:.2f}%")
                    else:
                        print(f"💸 Loss: ${abs(result):.3f} | ROI: {roi:.2f}%")
                    print(f"📋 Order: {sell_order}")
                    return result > 0
                break

            # Check every 3 seconds for first minute, then every 5 seconds
            sleep_time = 3 if time.time() - start_time < 60 else 5
            time.sleep(sleep_time)

        # Timeout exit
        print(f"\n⏰ Timeout reached! Selling at market...")
        final_price = self.get_price(symbol)
        sell_order = self.sell_market(symbol, crypto_amount * 0.995)

        if sell_order and final_price:
            result = (final_price - current_price) * crypto_amount
            roi = (result/amount)*100
            print(f"📋 Final result: ${result:.3f} | ROI: {roi:.2f}% | Order: {sell_order}")
            return result > 0

        return False

    def auto_hunt_and_scalp(self, amount=10):
        """صيد وتداول تلقائي"""
        print(f"🎯 Starting auto hunt & scalp with ${amount}...")

        self.hunting = True
        successful_scalps = 0
        total_scalps = 0

        while self.hunting:
            try:
                # Scan markets
                opportunities = self.scan_all_markets()

                # Filter high-score opportunities
                top_opportunities = [opp for opp in opportunities if opp['score'] >= 60]

                if top_opportunities:
                    print(f"\n🔥 Found {len(top_opportunities)} high-score opportunities!")

                    # Take top 3 opportunities
                    for opp in top_opportunities[:3]:
                        if not self.hunting:
                            break

                        print(f"\n🚀 Auto-scalping {opp['symbol']} (Score: {opp['score']})...")

                        success = self.ultimate_scalp(opp['symbol'], amount, opp)
                        total_scalps += 1

                        if success:
                            successful_scalps += 1
                            print(f"✅ Successful scalp #{successful_scalps}!")
                        else:
                            print(f"❌ Scalp failed")

                        # Wait between scalps
                        if self.hunting:
                            time.sleep(30)

                    # Show stats
                    if total_scalps > 0:
                        success_rate = (successful_scalps / total_scalps) * 100
                        print(f"\n📊 Auto-scalp stats: {successful_scalps}/{total_scalps} successful ({success_rate:.1f}%)")

                else:
                    print("⏳ No high-score opportunities found. Waiting...")

                # Wait before next scan
                if self.hunting:
                    time.sleep(60)  # Wait 1 minute

            except KeyboardInterrupt:
                self.hunting = False
                break
            except Exception as e:
                print(f"❌ Auto-hunt error: {e}")
                time.sleep(30)

        print(f"🛑 Auto-hunt stopped. Final stats: {successful_scalps}/{total_scalps} successful")

    def stop_hunting(self):
        """إيقاف الصيد"""
        self.hunting = False
        print("🛑 Hunting stopped")

def main():
    """الدالة الرئيسية"""
    scalper = UltimateScalper()

    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🚀 المضارب النهائي وصياد الفرص                      ║
    ║           Ultimate Scalper & Opportunity Hunter              ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝

    الأوامر النهائية:
    1. price <SYMBOL>           - سعر العملة
    2. balance <CURRENCY>       - الرصيد
    3. scan                     - مسح جميع الأسواق
    4. scalp <SYMBOL> <AMOUNT>  - مضاربة نهائية
    5. hunt <AMOUNT>            - صيد وتداول تلقائي
    6. stop                     - إيقاف الصيد
    7. top                      - أفضل الفرص الحالية
    8. quick <AMOUNT>           - مضاربة سريعة على أفضل فرصة
    9. quit                     - خروج

    أمثلة قوية:
    scan                 - مسح شامل لجميع العملات
    scalp BTC-USDT 20    - مضاربة نهائية على البيتكوين
    hunt 15              - صيد وتداول تلقائي بـ $15
    quick 10             - مضاربة سريعة على أفضل فرصة
    """)

    while True:
        try:
            command = input("\n🚀 Ultimate Command: ").strip().split()

            if not command:
                continue

            cmd = command[0].lower()

            if cmd == "quit":
                scalper.stop_hunting()
                print("👋 Ultimate Scalper stopped!")
                break

            elif cmd == "price" and len(command) == 2:
                symbol = command[1].upper()
                price = scalper.get_price(symbol)
                if price:
                    print(f"💰 {symbol}: ${price}")

            elif cmd == "balance" and len(command) == 2:
                currency = command[1].upper()
                balance = scalper.get_balance(currency)
                print(f"💰 {currency}: {balance}")

            elif cmd == "scan":
                scalper.scan_all_markets()

            elif cmd == "scalp":
                symbol = command[1].upper() if len(command) > 1 else "BTC-USDT"
                amount = float(command[2]) if len(command) > 2 else 10

                # Get analysis first
                ticker = scalper._api_call("GET", f"/api/v1/market/stats?symbol={symbol}")
                analysis = None
                if ticker:
                    analysis = scalper.analyze_coin(symbol, ticker)

                scalper.ultimate_scalp(symbol, amount, analysis)

            elif cmd == "hunt" and len(command) == 2:
                amount = float(command[1])
                hunting_thread = threading.Thread(target=scalper.auto_hunt_and_scalp, args=(amount,), daemon=True)
                hunting_thread.start()
                print(f"🎯 Auto-hunting started with ${amount} per scalp")

            elif cmd == "stop":
                scalper.stop_hunting()

            elif cmd == "top":
                if scalper.opportunities:
                    print(f"\n🔥 TOP OPPORTUNITIES:")
                    for i, opp in enumerate(scalper.opportunities[:10]):
                        print(f"{i+1:2d}. {opp['symbol']:<15} {opp['signal_type']:<12} Score: {opp['score']:<3} "
                              f"${opp['current_price']:<10.6f} {opp['change_24h']:+6.2f}%")
                else:
                    print("❌ No opportunities found. Run 'scan' first.")

            elif cmd == "quick" and len(command) == 2:
                amount = float(command[1])

                # Get fresh opportunities
                opportunities = scalper.scan_all_markets()

                if opportunities and opportunities[0]['score'] >= 50:
                    best_opp = opportunities[0]
                    print(f"🚀 Quick scalping best opportunity: {best_opp['symbol']} (Score: {best_opp['score']})")
                    scalper.ultimate_scalp(best_opp['symbol'], amount, best_opp)
                else:
                    print("❌ No good opportunities found for quick scalp")

            else:
                print("❌ Invalid command. Type 'quit' to exit.")

        except KeyboardInterrupt:
            scalper.stop_hunting()
            print("\n👋 Ultimate Scalper stopped!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
