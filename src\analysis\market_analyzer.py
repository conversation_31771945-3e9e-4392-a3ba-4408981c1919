"""
Market Analyzer for AI Crypto Trading Bot
Comprehensive market analysis combining technical indicators and patterns
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio

from .technical_indicators import TechnicalIndicators, IndicatorResult
from .pattern_recognition import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PatternResult
from ..core.logger import logger, trading_logger


@dataclass
class MarketSignal:
    """Comprehensive market signal"""
    symbol: str
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    strength: float  # 0-1
    confidence: float  # 0-1
    timestamp: datetime
    
    # Signal components
    technical_signals: Dict[str, IndicatorResult]
    pattern_signals: List[PatternResult]
    trend_analysis: Dict
    support_resistance: Dict
    
    # Price targets
    entry_price: float
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    
    # Additional info
    reasoning: str = ""
    risk_level: str = "MEDIUM"  # LOW, MEDIUM, HIGH
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type,
            'strength': self.strength,
            'confidence': self.confidence,
            'timestamp': self.timestamp.isoformat(),
            'technical_signals': {k: v.to_dict() for k, v in self.technical_signals.items()},
            'pattern_signals': [p.to_dict() for p in self.pattern_signals],
            'trend_analysis': self.trend_analysis,
            'support_resistance': self.support_resistance,
            'entry_price': self.entry_price,
            'target_price': self.target_price,
            'stop_loss': self.stop_loss,
            'reasoning': self.reasoning,
            'risk_level': self.risk_level
        }


class MarketAnalyzer:
    """
    Comprehensive market analyzer combining technical analysis and pattern recognition
    """
    
    def __init__(self):
        self.technical_indicators = TechnicalIndicators()
        self.pattern_recognition = PatternRecognition()
        self.analysis_cache = {}
        
        # Signal weights for different indicators
        self.indicator_weights = {
            'RSI': 0.20,
            'MACD': 0.25,
            'Bollinger_Bands': 0.20,
            'Stochastic': 0.15,
            'Patterns': 0.20
        }
        
        logger.info("Market Analyzer initialized")
    
    async def analyze_symbol(self, symbol: str, df: pd.DataFrame, 
                           timeframe: str = '1h') -> MarketSignal:
        """
        Comprehensive analysis of a single symbol
        """
        try:
            logger.info(f"Analyzing {symbol} on {timeframe} timeframe")
            
            # Technical indicators analysis
            technical_signals = self.technical_indicators.analyze_all_indicators(df)
            
            # Pattern recognition analysis
            pattern_analysis = self.pattern_recognition.analyze_all_patterns(df)
            
            # Trend analysis
            trend_analysis = self.technical_indicators.get_trend_strength(df)
            
            # Support and resistance
            support_resistance = self.pattern_recognition.detect_support_resistance(df)
            
            # Generate comprehensive signal
            signal = self._generate_comprehensive_signal(
                symbol=symbol,
                df=df,
                technical_signals=technical_signals,
                pattern_analysis=pattern_analysis,
                trend_analysis=trend_analysis,
                support_resistance=support_resistance
            )
            
            # Log the signal
            trading_logger.signal_generated(
                symbol=symbol,
                signal=signal.signal_type,
                confidence=signal.confidence,
                indicators={
                    'technical_count': len(technical_signals),
                    'pattern_count': len(pattern_analysis.get('candlestick_patterns', [])),
                    'trend_direction': trend_analysis.get('trend_direction', 'NEUTRAL')
                }
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}")
            # Return neutral signal on error
            return MarketSignal(
                symbol=symbol,
                signal_type='HOLD',
                strength=0.0,
                confidence=0.0,
                timestamp=datetime.now(),
                technical_signals={},
                pattern_signals=[],
                trend_analysis={},
                support_resistance={},
                entry_price=df['close'].iloc[-1] if not df.empty else 0.0,
                reasoning="Analysis error occurred"
            )
    
    def _generate_comprehensive_signal(self, symbol: str, df: pd.DataFrame,
                                     technical_signals: Dict[str, IndicatorResult],
                                     pattern_analysis: Dict,
                                     trend_analysis: Dict,
                                     support_resistance: Dict) -> MarketSignal:
        """Generate comprehensive trading signal"""
        
        current_price = df['close'].iloc[-1]
        
        # Calculate signal scores
        buy_score = 0.0
        sell_score = 0.0
        total_weight = 0.0
        
        # Technical indicators scoring
        for indicator_name, result in technical_signals.items():
            weight = self.indicator_weights.get(indicator_name, 0.1)
            total_weight += weight
            
            if result.signal == 'BUY':
                buy_score += weight * result.strength
            elif result.signal == 'SELL':
                sell_score += weight * result.strength
        
        # Pattern analysis scoring
        pattern_weight = self.indicator_weights.get('Patterns', 0.2)
        total_weight += pattern_weight
        
        bullish_patterns = 0
        bearish_patterns = 0
        pattern_confidence = 0.0
        
        candlestick_patterns = pattern_analysis.get('candlestick_patterns', [])
        for pattern in candlestick_patterns:
            if pattern.pattern_type == 'BULLISH':
                bullish_patterns += 1
                buy_score += pattern_weight * pattern.confidence * 0.3
            elif pattern.pattern_type == 'BEARISH':
                bearish_patterns += 1
                sell_score += pattern_weight * pattern.confidence * 0.3
            pattern_confidence += pattern.confidence
        
        # Chart patterns
        triangle_pattern = pattern_analysis.get('triangle_pattern')
        if triangle_pattern:
            if triangle_pattern.pattern_type == 'BULLISH':
                buy_score += pattern_weight * triangle_pattern.confidence * 0.4
            elif triangle_pattern.pattern_type == 'BEARISH':
                sell_score += pattern_weight * triangle_pattern.confidence * 0.4
        
        head_shoulders = pattern_analysis.get('head_shoulders')
        if head_shoulders:
            if head_shoulders.pattern_type == 'BEARISH':
                sell_score += pattern_weight * head_shoulders.confidence * 0.3
        
        # Trend analysis influence
        trend_strength = trend_analysis.get('trend_strength', 0)
        if trend_strength > 0.02:  # Strong bullish trend
            buy_score *= 1.2
        elif trend_strength < -0.02:  # Strong bearish trend
            sell_score *= 1.2
        
        # Normalize scores
        if total_weight > 0:
            buy_score /= total_weight
            sell_score /= total_weight
        
        # Determine signal
        signal_threshold = 0.3
        if buy_score > sell_score and buy_score > signal_threshold:
            signal_type = 'BUY'
            strength = buy_score
            confidence = min(buy_score * 1.5, 1.0)
        elif sell_score > buy_score and sell_score > signal_threshold:
            signal_type = 'SELL'
            strength = sell_score
            confidence = min(sell_score * 1.5, 1.0)
        else:
            signal_type = 'HOLD'
            strength = max(buy_score, sell_score)
            confidence = 0.5
        
        # Calculate price targets
        target_price, stop_loss = self._calculate_price_targets(
            current_price, signal_type, support_resistance, df
        )
        
        # Generate reasoning
        reasoning = self._generate_reasoning(
            signal_type, technical_signals, pattern_analysis, trend_analysis
        )
        
        # Determine risk level
        risk_level = self._assess_risk_level(
            signal_type, confidence, trend_analysis, pattern_analysis
        )
        
        return MarketSignal(
            symbol=symbol,
            signal_type=signal_type,
            strength=strength,
            confidence=confidence,
            timestamp=datetime.now(),
            technical_signals=technical_signals,
            pattern_signals=candlestick_patterns,
            trend_analysis=trend_analysis,
            support_resistance=support_resistance,
            entry_price=current_price,
            target_price=target_price,
            stop_loss=stop_loss,
            reasoning=reasoning,
            risk_level=risk_level
        )
    
    def _calculate_price_targets(self, current_price: float, signal_type: str,
                               support_resistance: Dict, df: pd.DataFrame) -> Tuple[Optional[float], Optional[float]]:
        """Calculate target price and stop loss"""
        try:
            atr = self.technical_indicators.atr(df['high'], df['low'], df['close']).iloc[-1]
            
            if signal_type == 'BUY':
                # Target: next resistance or 2*ATR
                resistances = support_resistance.get('resistance', [])
                target = None
                for resistance in resistances:
                    if resistance > current_price:
                        target = resistance
                        break
                
                if target is None:
                    target = current_price + (2 * atr)
                
                # Stop loss: previous support or 1*ATR below
                supports = support_resistance.get('support', [])
                stop_loss = None
                for support in reversed(supports):
                    if support < current_price:
                        stop_loss = support
                        break
                
                if stop_loss is None:
                    stop_loss = current_price - atr
                
                return target, stop_loss
                
            elif signal_type == 'SELL':
                # Target: next support or 2*ATR below
                supports = support_resistance.get('support', [])
                target = None
                for support in reversed(supports):
                    if support < current_price:
                        target = support
                        break
                
                if target is None:
                    target = current_price - (2 * atr)
                
                # Stop loss: previous resistance or 1*ATR above
                resistances = support_resistance.get('resistance', [])
                stop_loss = None
                for resistance in resistances:
                    if resistance > current_price:
                        stop_loss = resistance
                        break
                
                if stop_loss is None:
                    stop_loss = current_price + atr
                
                return target, stop_loss
            
        except Exception as e:
            logger.warning(f"Error calculating price targets: {e}")
        
        return None, None
    
    def _generate_reasoning(self, signal_type: str, technical_signals: Dict,
                          pattern_analysis: Dict, trend_analysis: Dict) -> str:
        """Generate human-readable reasoning for the signal"""
        reasons = []
        
        # Technical indicators
        for name, result in technical_signals.items():
            if result.signal != 'HOLD':
                reasons.append(f"{name}: {result.signal} ({result.strength:.2f})")
        
        # Patterns
        patterns = pattern_analysis.get('candlestick_patterns', [])
        bullish_patterns = [p for p in patterns if p.pattern_type == 'BULLISH']
        bearish_patterns = [p for p in patterns if p.pattern_type == 'BEARISH']
        
        if bullish_patterns:
            reasons.append(f"Bullish patterns: {len(bullish_patterns)}")
        if bearish_patterns:
            reasons.append(f"Bearish patterns: {len(bearish_patterns)}")
        
        # Trend
        trend_direction = trend_analysis.get('trend_direction', 'NEUTRAL')
        if trend_direction != 'NEUTRAL':
            reasons.append(f"Trend: {trend_direction}")
        
        if not reasons:
            return f"Signal: {signal_type} - Mixed signals, low confidence"
        
        return f"Signal: {signal_type} - " + ", ".join(reasons)
    
    def _assess_risk_level(self, signal_type: str, confidence: float,
                          trend_analysis: Dict, pattern_analysis: Dict) -> str:
        """Assess risk level of the signal"""
        risk_score = 0
        
        # Confidence factor
        if confidence > 0.8:
            risk_score -= 1  # Lower risk
        elif confidence < 0.4:
            risk_score += 2  # Higher risk
        
        # Trend alignment
        trend_direction = trend_analysis.get('trend_direction', 'NEUTRAL')
        if (signal_type == 'BUY' and trend_direction == 'BULLISH') or \
           (signal_type == 'SELL' and trend_direction == 'BEARISH'):
            risk_score -= 1  # Lower risk when aligned with trend
        elif (signal_type == 'BUY' and trend_direction == 'BEARISH') or \
             (signal_type == 'SELL' and trend_direction == 'BULLISH'):
            risk_score += 2  # Higher risk when against trend
        
        # Pattern confirmation
        patterns = pattern_analysis.get('candlestick_patterns', [])
        confirming_patterns = [p for p in patterns if p.pattern_type == signal_type.replace('BUY', 'BULLISH').replace('SELL', 'BEARISH')]
        if len(confirming_patterns) >= 2:
            risk_score -= 1
        
        # Determine risk level
        if risk_score <= -1:
            return 'LOW'
        elif risk_score >= 2:
            return 'HIGH'
        else:
            return 'MEDIUM'
    
    async def analyze_multiple_symbols(self, symbols_data: Dict[str, pd.DataFrame],
                                     timeframe: str = '1h') -> Dict[str, MarketSignal]:
        """Analyze multiple symbols concurrently"""
        tasks = []
        for symbol, df in symbols_data.items():
            task = self.analyze_symbol(symbol, df, timeframe)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        signals = {}
        for i, result in enumerate(results):
            symbol = list(symbols_data.keys())[i]
            if isinstance(result, Exception):
                logger.error(f"Error analyzing {symbol}: {result}")
                continue
            signals[symbol] = result
        
        return signals
    
    def get_market_overview(self, signals: Dict[str, MarketSignal]) -> Dict:
        """Generate market overview from multiple signals"""
        overview = {
            'total_symbols': len(signals),
            'buy_signals': 0,
            'sell_signals': 0,
            'hold_signals': 0,
            'high_confidence_signals': 0,
            'average_confidence': 0.0,
            'market_sentiment': 'NEUTRAL',
            'top_opportunities': [],
            'timestamp': datetime.now().isoformat()
        }
        
        if not signals:
            return overview
        
        confidences = []
        buy_strength = 0
        sell_strength = 0
        
        for symbol, signal in signals.items():
            confidences.append(signal.confidence)
            
            if signal.signal_type == 'BUY':
                overview['buy_signals'] += 1
                buy_strength += signal.strength
            elif signal.signal_type == 'SELL':
                overview['sell_signals'] += 1
                sell_strength += signal.strength
            else:
                overview['hold_signals'] += 1
            
            if signal.confidence > 0.7:
                overview['high_confidence_signals'] += 1
            
            # Collect top opportunities
            if signal.signal_type in ['BUY', 'SELL'] and signal.confidence > 0.6:
                overview['top_opportunities'].append({
                    'symbol': symbol,
                    'signal': signal.signal_type,
                    'confidence': signal.confidence,
                    'strength': signal.strength
                })
        
        overview['average_confidence'] = np.mean(confidences) if confidences else 0.0
        
        # Determine market sentiment
        if buy_strength > sell_strength * 1.2:
            overview['market_sentiment'] = 'BULLISH'
        elif sell_strength > buy_strength * 1.2:
            overview['market_sentiment'] = 'BEARISH'
        
        # Sort top opportunities by confidence
        overview['top_opportunities'].sort(key=lambda x: x['confidence'], reverse=True)
        overview['top_opportunities'] = overview['top_opportunities'][:5]
        
        return overview
