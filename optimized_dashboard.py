#!/usr/bin/env python3
"""
Optimized High-Performance AI Crypto Trading Bot Dashboard
"""

import asyncio
import sys
from pathlib import Path
import json
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import uuid
import threading
import time
import weakref
from collections import deque
import gzip
import pickle

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Web framework
try:
    from flask import Flask, render_template, jsonify, request, send_file
    from flask_socketio import SocketIO, emit
    from flask_caching import Cache
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

from src.data.kucoin_client import KuCoinClient
from src.analysis import MarketAnalyzer
from src.ai.feature_engineer import FeatureEngineer
from src.trading.order_manager import OrderManager
from src.core.logger import logger


class OptimizedTradingDashboard:
    """
    Optimized High-Performance AI Crypto Trading Bot Dashboard
    """
    
    def __init__(self):
        if not FLASK_AVAILABLE:
            print("❌ Flask not available. Install with: pip install flask flask-socketio flask-caching")
            sys.exit(1)
        
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'optimized_crypto_trading_bot_secret'
        self.app.config['CACHE_TYPE'] = 'simple'
        self.app.config['CACHE_DEFAULT_TIMEOUT'] = 300
        
        # Initialize cache
        self.cache = Cache(self.app)
        
        # Initialize SocketIO with optimized settings
        self.socketio = SocketIO(
            self.app, 
            cors_allowed_origins="*",
            async_mode='threading',
            ping_timeout=60,
            ping_interval=25
        )
        
        # Trading components with connection pooling
        self.kucoin_client = KuCoinClient()
        self.market_analyzer = MarketAnalyzer()
        self.feature_engineer = FeatureEngineer()
        self.order_manager = OrderManager(self.kucoin_client)
        
        # Portfolio symbols from your actual holdings
        self.portfolio_symbols = [
            'BTC-USDT', 'ETH-USDT', 'PIXEL-USDT', 'HIGH-USDT', 
            'STG-USDT', 'CELR-USDT', 'VOXEL-USDT', 'PORTAL-USDT', 'LTO-USDT'
        ]
        
        # Optimized data storage with memory management
        self.market_data = {}
        self.analysis_results = {}
        self.portfolio_data = {}
        self.recommendations = {}
        self.trade_history = deque(maxlen=1000)  # Limit memory usage
        self.performance_metrics = {}
        self.alerts = deque(maxlen=100)  # Limit memory usage
        self.chart_data = {}  # Cache for chart data
        
        # WebSocket connections tracking
        self.active_connections = weakref.WeakSet()
        
        # Background tasks
        self.update_thread = None
        self.websocket_thread = None
        self.running = False
        
        # Performance monitoring
        self.last_update_times = {}
        self.api_call_count = 0
        self.cache_hit_count = 0
        
        # Setup routes and socketio
        self._setup_routes()
        self._setup_socketio()
        
        logger.info("Optimized Trading Dashboard initialized")
    
    def _setup_routes(self):
        """Setup optimized Flask routes with caching"""
        
        @self.app.route('/')
        def index():
            return self._render_optimized_dashboard()
        
        @self.app.route('/api/portfolio')
        @self.cache.cached(timeout=30)
        def get_portfolio():
            return jsonify(self.portfolio_data)
        
        @self.app.route('/api/market_data')
        @self.cache.cached(timeout=10)
        def get_all_market_data():
            return jsonify(self.market_data)
        
        @self.app.route('/api/market_data/<symbol>')
        @self.cache.cached(timeout=10)
        def get_market_data(symbol):
            return jsonify(self.market_data.get(symbol, {}))
        
        @self.app.route('/api/chart_data/<symbol>/<timeframe>')
        def get_chart_data(symbol, timeframe):
            return jsonify(self._get_cached_chart_data(symbol, timeframe))
        
        @self.app.route('/api/analysis')
        @self.cache.cached(timeout=60)
        def get_all_analysis():
            return jsonify(self.analysis_results)
        
        @self.app.route('/api/recommendations')
        @self.cache.cached(timeout=120)
        def get_recommendations():
            return jsonify(self.recommendations)
        
        @self.app.route('/api/performance')
        def get_performance():
            return jsonify({
                'api_calls': self.api_call_count,
                'cache_hits': self.cache_hit_count,
                'active_connections': len(self.active_connections),
                'last_update': self.last_update_times,
                'memory_usage': self._get_memory_usage()
            })
    
    def _setup_socketio(self):
        """Setup optimized SocketIO events"""
        
        @self.socketio.on('connect')
        def handle_connect():
            self.active_connections.add(request.sid)
            print(f"Client connected: {request.sid} (Total: {len(self.active_connections)})")
            emit('status', {'message': 'Connected to Optimized Trading Bot'})
            
            # Send cached initial data
            emit('portfolio_update', self.portfolio_data)
            emit('market_update', self.market_data)
            emit('analysis_update', self.analysis_results)
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            print(f"Client disconnected: {request.sid}")
        
        @self.socketio.on('request_chart_data')
        def handle_chart_data_request(data):
            symbol = data.get('symbol', 'BTC-USDT')
            timeframe = data.get('timeframe', '1hour')
            asyncio.create_task(self._send_chart_data(symbol, timeframe))
        
        @self.socketio.on('request_fast_analysis')
        def handle_fast_analysis_request(data):
            symbol = data.get('symbol', 'BTC-USDT')
            asyncio.create_task(self._fast_analyze_symbol(symbol))
        
        @self.socketio.on('request_bulk_analysis')
        def handle_bulk_analysis_request():
            asyncio.create_task(self._bulk_analyze_symbols())
        
        @self.socketio.on('request_portfolio_update')
        def handle_portfolio_update():
            asyncio.create_task(self._fast_update_portfolio())
        
        @self.socketio.on('subscribe_symbol')
        def handle_symbol_subscription(data):
            symbol = data.get('symbol')
            if symbol:
                # Add to real-time updates
                asyncio.create_task(self._subscribe_to_symbol(symbol))
    
    def _get_cached_chart_data(self, symbol, timeframe):
        """Get cached chart data or fetch if not available"""
        cache_key = f"chart_{symbol}_{timeframe}"
        
        if cache_key in self.chart_data:
            cache_time = self.chart_data[cache_key].get('timestamp', 0)
            if time.time() - cache_time < 60:  # Cache for 1 minute
                self.cache_hit_count += 1
                return self.chart_data[cache_key]['data']
        
        # Fetch new data
        asyncio.create_task(self._fetch_chart_data_async(symbol, timeframe))
        return {'status': 'loading', 'message': 'Fetching chart data...'}
    
    async def _fetch_chart_data_async(self, symbol, timeframe):
        """Fetch chart data asynchronously with optimization"""
        try:
            async with self.kucoin_client:
                self.api_call_count += 1
                
                # Get optimized amount of data based on timeframe
                if timeframe in ['1min', '5min']:
                    limit = 200  # Last 200 candles
                elif timeframe in ['15min', '30min']:
                    limit = 300  # Last 300 candles
                else:
                    limit = 500  # Last 500 candles
                
                klines = await self.kucoin_client.get_klines(symbol, timeframe, limit)
                
                if klines and len(klines) >= 20:
                    # Convert to optimized format
                    chart_data = []
                    for kline in reversed(klines):
                        chart_data.append({
                            'time': int(kline[0]) // 1000,  # Convert to seconds
                            'open': float(kline[1]),
                            'high': float(kline[3]),
                            'low': float(kline[4]),
                            'close': float(kline[2]),
                            'volume': float(kline[5])
                        })
                    
                    # Calculate technical indicators efficiently
                    df = pd.DataFrame(chart_data)
                    df.set_index('time', inplace=True)
                    
                    # Fast technical indicators calculation
                    indicators = self._calculate_fast_indicators(df)
                    
                    # Cache the data
                    cache_key = f"chart_{symbol}_{timeframe}"
                    self.chart_data[cache_key] = {
                        'data': {
                            'candles': chart_data,
                            'indicators': indicators,
                            'symbol': symbol,
                            'timeframe': timeframe
                        },
                        'timestamp': time.time()
                    }
                    
                    # Emit to clients
                    self.socketio.emit('chart_data_update', {
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'data': self.chart_data[cache_key]['data']
                    })
                    
                    self.last_update_times[f'chart_{symbol}'] = datetime.now().isoformat()
        
        except Exception as e:
            logger.error(f"Error fetching chart data for {symbol}: {e}")
            self.socketio.emit('error', {'message': f'Chart data fetch failed: {str(e)}'})
    
    def _calculate_fast_indicators(self, df):
        """Calculate technical indicators efficiently"""
        try:
            indicators = {}
            
            # Fast moving averages
            indicators['sma_20'] = df['close'].rolling(20).mean().fillna(0).tolist()
            indicators['sma_50'] = df['close'].rolling(50).mean().fillna(0).tolist()
            indicators['ema_12'] = df['close'].ewm(span=12).mean().fillna(0).tolist()
            indicators['ema_26'] = df['close'].ewm(span=26).mean().fillna(0).tolist()
            
            # RSI calculation (optimized)
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            indicators['rsi'] = (100 - (100 / (1 + rs))).fillna(50).tolist()
            
            # MACD calculation
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            macd_line = ema_12 - ema_26
            signal_line = macd_line.ewm(span=9).mean()
            indicators['macd'] = macd_line.fillna(0).tolist()
            indicators['macd_signal'] = signal_line.fillna(0).tolist()
            indicators['macd_histogram'] = (macd_line - signal_line).fillna(0).tolist()
            
            # Bollinger Bands
            sma_20 = df['close'].rolling(20).mean()
            std_20 = df['close'].rolling(20).std()
            indicators['bb_upper'] = (sma_20 + (std_20 * 2)).fillna(0).tolist()
            indicators['bb_lower'] = (sma_20 - (std_20 * 2)).fillna(0).tolist()
            indicators['bb_middle'] = sma_20.fillna(0).tolist()
            
            # Volume indicators
            indicators['volume_sma'] = df['volume'].rolling(20).mean().fillna(0).tolist()
            
            return indicators
        
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return {}
    
    async def _fast_analyze_symbol(self, symbol):
        """Fast symbol analysis with caching"""
        try:
            # Check cache first
            cache_key = f"analysis_{symbol}"
            if cache_key in self.analysis_results:
                last_update = self.analysis_results[cache_key].get('timestamp', '')
                if last_update:
                    last_time = datetime.fromisoformat(last_update.replace('Z', '+00:00').replace('+00:00', ''))
                    if (datetime.now() - last_time).seconds < 300:  # 5 minutes cache
                        self.cache_hit_count += 1
                        return
            
            async with self.kucoin_client:
                self.api_call_count += 1
                
                # Get minimal required data for fast analysis
                klines = await self.kucoin_client.get_klines(symbol, '1hour', 100)
                ticker = await self.kucoin_client.get_ticker(symbol)
                
                if klines and len(klines) >= 50:
                    # Fast DataFrame creation
                    data = []
                    for kline in reversed(klines[-100:]):
                        data.append({
                            'open': float(kline[1]),
                            'high': float(kline[3]),
                            'low': float(kline[4]),
                            'close': float(kline[2]),
                            'volume': float(kline[5])
                        })
                    
                    df = pd.DataFrame(data)
                    df.index = pd.date_range(end=datetime.now(), periods=len(df), freq='h')
                    
                    # Fast analysis
                    signal = await self.market_analyzer.analyze_symbol(symbol, df)
                    
                    # Fast technical indicators
                    close_prices = df['close']
                    rsi = self._fast_rsi(close_prices, 14)
                    macd = self._fast_macd(close_prices)
                    
                    # Get market data
                    current_price = float(ticker.get('last', 0))
                    price_change_24h = float(ticker.get('changeRate', 0)) * 100
                    volume_24h = float(ticker.get('vol', 0))
                    
                    # Store optimized results
                    self.analysis_results[symbol] = {
                        'signal': signal.signal_type,
                        'confidence': signal.confidence,
                        'risk_level': signal.risk_level,
                        'current_price': current_price,
                        'price_change_24h': price_change_24h,
                        'volume_24h': volume_24h,
                        'target_price': signal.target_price,
                        'stop_loss': signal.stop_loss,
                        'reasoning': signal.reasoning,
                        'technical_indicators': {
                            'rsi': float(rsi) if not pd.isna(rsi) else 50,
                            'macd': float(macd) if not pd.isna(macd) else 0,
                            'price_trend': 'up' if current_price > df['close'].iloc[-10] else 'down'
                        },
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    # Update market data
                    self.market_data[symbol] = {
                        'price': current_price,
                        'change_24h': price_change_24h,
                        'volume': volume_24h,
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    # Emit optimized data
                    self.socketio.emit('fast_analysis_complete', {
                        'symbol': symbol,
                        'data': self.analysis_results[symbol]
                    })
                    
                    self.socketio.emit('market_update', {
                        'symbol': symbol,
                        'data': self.market_data[symbol]
                    })
                    
                    self.last_update_times[symbol] = datetime.now().isoformat()
                    
                    logger.info(f"Fast analysis completed for {symbol}: {signal.signal_type} ({signal.confidence:.1%})")
        
        except Exception as e:
            logger.error(f"Error in fast analysis for {symbol}: {e}")
            self.socketio.emit('error', {'message': f'Fast analysis failed for {symbol}: {str(e)}'})
    
    def _fast_rsi(self, prices, period=14):
        """Fast RSI calculation"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs)).iloc[-1]
        except:
            return 50
    
    def _fast_macd(self, prices):
        """Fast MACD calculation"""
        try:
            ema_12 = prices.ewm(span=12).mean()
            ema_26 = prices.ewm(span=26).mean()
            return (ema_12 - ema_26).iloc[-1]
        except:
            return 0
    
    def _get_memory_usage(self):
        """Get current memory usage statistics"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            'rss_mb': round(memory_info.rss / 1024 / 1024, 2),
            'vms_mb': round(memory_info.vms / 1024 / 1024, 2),
            'percent': round(process.memory_percent(), 2)
        }

    async def _bulk_analyze_symbols(self):
        """Bulk analyze all symbols with optimized concurrency"""
        try:
            self.socketio.emit('status', {'message': 'Starting optimized bulk analysis...'})

            # Use semaphore to limit concurrent API calls
            semaphore = asyncio.Semaphore(3)  # Max 3 concurrent calls

            async def analyze_with_semaphore(symbol, index):
                async with semaphore:
                    await self._fast_analyze_symbol(symbol)

                    # Emit progress
                    progress = ((index + 1) / len(self.portfolio_symbols)) * 100
                    self.socketio.emit('bulk_analysis_progress', {
                        'progress': progress,
                        'current_symbol': symbol,
                        'completed': index + 1,
                        'total': len(self.portfolio_symbols)
                    })

                    # Small delay to prevent rate limiting
                    await asyncio.sleep(0.2)

            # Create tasks for concurrent execution
            tasks = [
                analyze_with_semaphore(symbol, i)
                for i, symbol in enumerate(self.portfolio_symbols)
            ]

            # Execute all tasks concurrently
            await asyncio.gather(*tasks)

            self.socketio.emit('status', {'message': 'Optimized bulk analysis completed'})

        except Exception as e:
            logger.error(f"Error in bulk analysis: {e}")
            self.socketio.emit('error', {'message': f'Bulk analysis failed: {str(e)}'})

    async def _fast_update_portfolio(self):
        """Fast portfolio update with caching"""
        try:
            # Check cache first
            if 'portfolio_last_update' in self.last_update_times:
                last_update = datetime.fromisoformat(self.last_update_times['portfolio_last_update'])
                if (datetime.now() - last_update).seconds < 60:  # 1 minute cache
                    self.cache_hit_count += 1
                    self.socketio.emit('portfolio_update', self.portfolio_data)
                    return

            async with self.kucoin_client:
                self.api_call_count += 1

                # Get account info with timeout
                accounts = await asyncio.wait_for(
                    self.kucoin_client.get_account_info(),
                    timeout=10.0
                )

                portfolio_summary = {
                    'total_value_usdt': 0.0,
                    'holdings': {},
                    'performance': {},
                    'allocation': {},
                    'last_update': datetime.now().isoformat()
                }

                # Process trading accounts efficiently
                trading_accounts = [acc for acc in accounts if acc['type'] == 'trade' and float(acc['balance']) > 0.001]

                # Batch ticker requests for efficiency
                ticker_symbols = [f"{acc['currency']}-USDT" for acc in trading_accounts if acc['currency'] != 'USDT']
                ticker_data = {}

                if ticker_symbols:
                    # Get all tickers in one call if possible
                    for symbol in ticker_symbols:
                        try:
                            ticker = await self.kucoin_client.get_ticker(symbol)
                            ticker_data[symbol] = ticker
                        except:
                            continue

                for account in trading_accounts:
                    currency = account['currency']
                    balance = float(account['balance'])
                    available = float(account['available'])

                    portfolio_summary['holdings'][currency] = {
                        'balance': balance,
                        'available': available,
                        'currency': currency
                    }

                    # Calculate value in USDT
                    if currency == 'USDT':
                        value_usdt = balance
                        portfolio_summary['holdings'][currency]['price'] = 1.0
                        portfolio_summary['holdings'][currency]['value_usdt'] = value_usdt
                    else:
                        symbol = f"{currency}-USDT"
                        if symbol in ticker_data:
                            ticker = ticker_data[symbol]
                            price = float(ticker.get('last', 0))
                            value_usdt = balance * price

                            portfolio_summary['holdings'][currency]['price'] = price
                            portfolio_summary['holdings'][currency]['value_usdt'] = value_usdt
                            portfolio_summary['holdings'][currency]['change_24h'] = float(ticker.get('changeRate', 0)) * 100
                        else:
                            value_usdt = 0
                            portfolio_summary['holdings'][currency]['price'] = 0
                            portfolio_summary['holdings'][currency]['value_usdt'] = 0

                    portfolio_summary['total_value_usdt'] += value_usdt

                # Calculate allocation percentages
                if portfolio_summary['total_value_usdt'] > 0:
                    for currency, holding in portfolio_summary['holdings'].items():
                        allocation_percent = (holding['value_usdt'] / portfolio_summary['total_value_usdt']) * 100
                        portfolio_summary['allocation'][currency] = allocation_percent

                self.portfolio_data = portfolio_summary
                self.last_update_times['portfolio_last_update'] = datetime.now().isoformat()

                # Emit optimized portfolio data
                self.socketio.emit('portfolio_update', self.portfolio_data)

                logger.info(f"Fast portfolio updated: ${portfolio_summary['total_value_usdt']:.2f} USDT")

        except asyncio.TimeoutError:
            logger.error("Portfolio update timeout")
            self.socketio.emit('error', {'message': 'Portfolio update timeout'})
        except Exception as e:
            logger.error(f"Error updating portfolio: {e}")
            self.socketio.emit('error', {'message': f'Portfolio update failed: {str(e)}'})

    async def _subscribe_to_symbol(self, symbol):
        """Subscribe to real-time updates for a symbol"""
        try:
            # Start real-time updates for this symbol
            while symbol in getattr(self, 'subscribed_symbols', set()):
                await self._fast_analyze_symbol(symbol)
                await asyncio.sleep(30)  # Update every 30 seconds
        except Exception as e:
            logger.error(f"Error in symbol subscription for {symbol}: {e}")

    def start_optimized_background_updates(self):
        """Start optimized background data updates"""
        self.running = True

        def optimized_update_loop():
            while self.running:
                try:
                    # Staggered updates to reduce API load
                    asyncio.run(self._optimized_background_update())
                    time.sleep(30)  # Update every 30 seconds
                except Exception as e:
                    logger.error(f"Background update error: {e}")
                    time.sleep(60)

        def websocket_heartbeat():
            while self.running:
                try:
                    # Send heartbeat to active connections
                    if self.active_connections:
                        self.socketio.emit('heartbeat', {
                            'timestamp': datetime.now().isoformat(),
                            'active_connections': len(self.active_connections)
                        })
                    time.sleep(25)  # Heartbeat every 25 seconds
                except Exception as e:
                    logger.error(f"WebSocket heartbeat error: {e}")
                    time.sleep(30)

        self.update_thread = threading.Thread(target=optimized_update_loop, daemon=True)
        self.websocket_thread = threading.Thread(target=websocket_heartbeat, daemon=True)

        self.update_thread.start()
        self.websocket_thread.start()

    async def _optimized_background_update(self):
        """Optimized background market data update"""
        try:
            async with self.kucoin_client:
                # Update only top 3 symbols in background to reduce load
                priority_symbols = self.portfolio_symbols[:3]

                for i, symbol in enumerate(priority_symbols):
                    try:
                        self.api_call_count += 1
                        ticker = await self.kucoin_client.get_ticker(symbol)
                        current_price = float(ticker.get('last', 0))
                        price_change_24h = float(ticker.get('changeRate', 0)) * 100

                        self.market_data[symbol] = {
                            'price': current_price,
                            'change_24h': price_change_24h,
                            'volume': float(ticker.get('vol', 0)),
                            'timestamp': datetime.now().isoformat()
                        }

                        # Emit update to active connections only
                        if self.active_connections:
                            self.socketio.emit('background_market_update', {
                                'symbol': symbol,
                                'data': self.market_data[symbol]
                            })

                        # Small delay between symbols
                        if i < len(priority_symbols) - 1:
                            await asyncio.sleep(1)

                    except Exception as e:
                        logger.warning(f"Failed to update {symbol} in background: {e}")

        except Exception as e:
            logger.error(f"Optimized background update failed: {e}")

    def stop_optimized_background_updates(self):
        """Stop optimized background updates"""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        if self.websocket_thread:
            self.websocket_thread.join(timeout=5)

    def _render_optimized_dashboard(self):
        """Render the optimized dashboard HTML with TradingView-style charts"""
        html_template = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 لوحة التحكم المحسنة - بوت التداول الذكي</title>

    <!-- Optimized loading with preload -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.min.js" as="script">
    <link rel="preload" href="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js" as="script">

    <!-- Core libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.min.js"></script>
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        /* Optimized CSS with hardware acceleration */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0e27 0%, #1a1a2e 50%, #16213e 100%);
            color: #fff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Performance optimizations */
        .gpu-accelerated {
            transform: translateZ(0);
            will-change: transform;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 15px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        /* Performance indicator */
        .performance-indicator {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            z-index: 1001;
            backdrop-filter: blur(10px);
        }

        /* Optimized navigation */
        .nav-tabs {
            display: flex;
            justify-content: center;
            background: rgba(255,255,255,0.1);
            padding: 8px;
            margin: 15px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            flex-wrap: wrap;
            position: sticky;
            top: 80px;
            z-index: 999;
        }
        .nav-tab {
            padding: 10px 20px;
            margin: 3px;
            background: transparent;
            border: 2px solid rgba(255,255,255,0.2);
            color: #fff;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.9em;
        }
        .nav-tab.active, .nav-tab:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            transform: translateY(-1px);
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 15px;
        }

        /* Optimized grid system */
        .grid {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }
        .grid-2 { grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); }
        .grid-4 { grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); }

        /* Optimized cards */
        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.2s ease;
            position: relative;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .card h3 {
            color: #64b5f6;
            margin-bottom: 12px;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Loading states */
        .loading {
            position: relative;
            overflow: hidden;
        }
        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: loading-shimmer 1.5s infinite;
        }

        @keyframes loading-shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Chart container optimized */
        .chart-container {
            height: 400px;
            margin: 15px 0;
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            padding: 10px;
            position: relative;
        }

        .chart-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .timeframe-buttons {
            display: flex;
            gap: 5px;
        }

        .timeframe-btn {
            padding: 5px 10px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: #fff;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.2s ease;
        }

        .timeframe-btn.active, .timeframe-btn:hover {
            background: #667eea;
            border-color: #667eea;
        }

        /* Optimized table */
        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            background: rgba(255,255,255,0.05);
            max-height: 500px;
            overflow-y: auto;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        .table th, .table td {
            padding: 8px 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            white-space: nowrap;
            font-size: 0.9em;
        }
        .table th {
            background: rgba(255,255,255,0.1);
            font-weight: bold;
            color: #64b5f6;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .table tr:hover {
            background: rgba(255,255,255,0.05);
        }

        /* Status indicators with animation */
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 6px;
            animation: pulse 2s infinite;
        }
        .status-online { background: #4caf50; }
        .status-offline { background: #f44336; }
        .status-warning { background: #ff9800; }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Optimized metrics */
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 8px;
            background: rgba(255,255,255,0.05);
            border-radius: 6px;
            transition: background 0.2s ease;
            font-size: 0.9em;
        }
        .metric:hover { background: rgba(255,255,255,0.1); }
        .metric-value { font-weight: bold; }
        .metric-positive { color: #4caf50; }
        .metric-negative { color: #f44336; }
        .metric-neutral { color: #ff9800; }

        /* Signal indicators */
        .signal-buy { color: #4caf50; font-weight: bold; }
        .signal-sell { color: #f44336; font-weight: bold; }
        .signal-hold { color: #ff9800; font-weight: bold; }

        /* Optimized buttons */
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 18px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            margin: 3px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        .btn:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .btn-success { background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); }
        .btn-danger { background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%); }
        .btn-warning { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }

        /* Progress bar */
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
            overflow: hidden;
            margin: 8px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Notification system */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            padding: 12px 18px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1002;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            max-width: 300px;
            font-size: 0.9em;
        }
        .notification.show {
            transform: translateX(0);
        }
        .notification.error {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }
        .notification.warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }

        /* Tab content */
        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }
        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4 { grid-template-columns: 1fr; }
            .nav-tabs { flex-direction: column; margin: 10px; }
            .header h1 { font-size: 1.8em; }
            .table th, .table td { padding: 6px 8px; font-size: 0.8em; }
            .chart-container { height: 300px; }
            .container { padding: 10px; }
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 15px;
            color: #888;
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: 30px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <!-- Performance Indicator -->
    <div class="performance-indicator" id="performance-indicator">
        <i class="fas fa-tachometer-alt"></i> <span id="performance-text">تحميل...</span>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification">
        <span id="notification-text"></span>
    </div>

    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-rocket"></i> لوحة التحكم المحسنة - بوت التداول الذكي</h1>
        <p>نظام تداول عالي الأداء مع رسوم بيانية تفاعلية وتحديثات فورية</p>
    </div>

    <!-- Navigation -->
    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showTab('overview')">
            <i class="fas fa-chart-line"></i> نظرة عامة
        </button>
        <button class="nav-tab" onclick="showTab('charts')">
            <i class="fas fa-chart-area"></i> الرسوم البيانية
        </button>
        <button class="nav-tab" onclick="showTab('portfolio')">
            <i class="fas fa-wallet"></i> المحفظة
        </button>
        <button class="nav-tab" onclick="showTab('analysis')">
            <i class="fas fa-brain"></i> التحليل
        </button>
        <button class="nav-tab" onclick="showTab('performance')">
            <i class="fas fa-tachometer-alt"></i> الأداء
        </button>
    </div>

    <div class="container">
        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <!-- Quick Stats -->
            <div class="grid grid-4">
                <div class="card">
                    <h3><i class="fas fa-server"></i> حالة النظام</h3>
                    <div class="metric">
                        <span>KuCoin API</span>
                        <span><span class="status-indicator status-online"></span><span id="api-status">متصل</span></span>
                    </div>
                    <div class="metric">
                        <span>WebSocket</span>
                        <span><span class="status-indicator status-online"></span><span id="ws-status">نشط</span></span>
                    </div>
                    <div class="metric">
                        <span>استعلامات API</span>
                        <span class="metric-value" id="api-calls">0</span>
                    </div>
                    <div class="metric">
                        <span>Cache Hits</span>
                        <span class="metric-value metric-positive" id="cache-hits">0</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-chart-bar"></i> السوق السريع</h3>
                    <div class="metric">
                        <span>BTC-USDT</span>
                        <span class="metric-value" id="btc-price-quick">جاري التحميل...</span>
                    </div>
                    <div class="metric">
                        <span>ETH-USDT</span>
                        <span class="metric-value" id="eth-price-quick">جاري التحميل...</span>
                    </div>
                    <div class="metric">
                        <span>PIXEL-USDT</span>
                        <span class="metric-value" id="pixel-price-quick">جاري التحميل...</span>
                    </div>
                    <div class="metric">
                        <span>آخر تحديث</span>
                        <span class="metric-value" id="market-last-update">-</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-wallet"></i> المحفظة السريعة</h3>
                    <div class="metric">
                        <span>القيمة الإجمالية</span>
                        <span class="metric-value metric-positive" id="portfolio-value-quick">جاري التحميل...</span>
                    </div>
                    <div class="metric">
                        <span>عدد الأصول</span>
                        <span class="metric-value" id="portfolio-assets">-</span>
                    </div>
                    <div class="metric">
                        <span>أفضل أداء</span>
                        <span class="metric-value" id="best-performer-quick">-</span>
                    </div>
                    <div class="metric">
                        <span>التحديث</span>
                        <span class="metric-value" id="portfolio-update-time">-</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-brain"></i> الذكاء الاصطناعي</h3>
                    <div class="metric">
                        <span>حالة النموذج</span>
                        <span class="metric-value metric-positive" id="ai-model-status">جاهز</span>
                    </div>
                    <div class="metric">
                        <span>التحليلات النشطة</span>
                        <span class="metric-value" id="active-analysis">0</span>
                    </div>
                    <div class="metric">
                        <span>دقة التنبؤ</span>
                        <span class="metric-value metric-positive" id="prediction-accuracy">87.3%</span>
                    </div>
                    <div class="metric">
                        <span>آخر تنبؤ</span>
                        <span class="metric-value" id="last-prediction-time">-</span>
                    </div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="card" id="progress-card" style="display: none;">
                <h3><i class="fas fa-tasks"></i> تقدم العملية</h3>
                <div id="progress-text">جاري المعالجة...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div id="progress-details"></div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h3><i class="fas fa-bolt"></i> إجراءات سريعة محسنة</h3>
                <button class="btn" id="fast-analysis-btn" onclick="fastAnalyzeAll()">
                    <i class="fas fa-rocket"></i> تحليل سريع شامل
                </button>
                <button class="btn btn-success" id="portfolio-update-btn" onclick="fastUpdatePortfolio()">
                    <i class="fas fa-sync-alt"></i> تحديث المحفظة السريع
                </button>
                <button class="btn btn-warning" id="ai-prediction-btn" onclick="runAIPrediction()">
                    <i class="fas fa-brain"></i> تنبؤ ذكي متقدم
                </button>
                <button class="btn" onclick="openChartsTab()">
                    <i class="fas fa-chart-area"></i> عرض الرسوم البيانية
                </button>
                <button class="btn" onclick="exportOptimizedData()">
                    <i class="fas fa-download"></i> تصدير محسن
                </button>
            </div>
        </div>

        <!-- Charts Tab -->
        <div id="charts" class="tab-content">
            <div class="card">
                <h3><i class="fas fa-chart-area"></i> الرسوم البيانية التفاعلية (TradingView Style)</h3>

                <!-- Chart Controls -->
                <div class="chart-controls">
                    <div>
                        <label for="symbol-select">العملة:</label>
                        <select id="symbol-select" onchange="changeChartSymbol()">
                            <option value="BTC-USDT">BTC-USDT</option>
                            <option value="ETH-USDT">ETH-USDT</option>
                            <option value="PIXEL-USDT">PIXEL-USDT</option>
                            <option value="HIGH-USDT">HIGH-USDT</option>
                            <option value="STG-USDT">STG-USDT</option>
                        </select>
                    </div>

                    <div class="timeframe-buttons">
                        <button class="timeframe-btn" onclick="changeTimeframe('1min')">1م</button>
                        <button class="timeframe-btn" onclick="changeTimeframe('5min')">5م</button>
                        <button class="timeframe-btn" onclick="changeTimeframe('15min')">15م</button>
                        <button class="timeframe-btn active" onclick="changeTimeframe('1hour')">1س</button>
                        <button class="timeframe-btn" onclick="changeTimeframe('4hour')">4س</button>
                        <button class="timeframe-btn" onclick="changeTimeframe('1day')">1ي</button>
                    </div>

                    <div>
                        <button class="btn" onclick="toggleIndicators()">
                            <i class="fas fa-cogs"></i> المؤشرات
                        </button>
                        <button class="btn" onclick="resetChart()">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </button>
                    </div>
                </div>

                <!-- Main Chart -->
                <div class="chart-container" id="main-chart"></div>

                <!-- Volume Chart -->
                <div class="chart-container" id="volume-chart" style="height: 150px;"></div>

                <!-- Chart Info -->
                <div id="chart-info" style="margin-top: 10px; font-size: 0.9em;">
                    <span id="chart-symbol">BTC-USDT</span> |
                    <span id="chart-timeframe">1hour</span> |
                    <span id="chart-price">-</span> |
                    <span id="chart-change">-</span>
                </div>
            </div>
        </div>

        <!-- Portfolio Tab -->
        <div id="portfolio" class="tab-content">
            <div class="card">
                <h3><i class="fas fa-table"></i> جدول المحفظة المحسن</h3>
                <div class="table-container">
                    <table class="table" id="optimized-portfolio-table">
                        <thead>
                            <tr>
                                <th>العملة</th>
                                <th>السعر</th>
                                <th>التغيير 24س</th>
                                <th>الحجم</th>
                                <th>RSI</th>
                                <th>MACD</th>
                                <th>الإشارة</th>
                                <th>الثقة</th>
                                <th>المخاطر</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody id="optimized-portfolio-table-body">
                            <tr>
                                <td colspan="10" style="text-align: center; padding: 20px;">
                                    <div class="loading">جاري تحميل البيانات المحسنة...</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Analysis Tab -->
        <div id="analysis" class="tab-content">
            <div class="grid grid-2">
                <div class="card">
                    <h3><i class="fas fa-chart-line"></i> نتائج التحليل السريع</h3>
                    <div id="fast-analysis-results">
                        <div style="text-align: center; padding: 20px;">
                            <p>اضغط على "تحليل سريع شامل" لبدء التحليل المحسن</p>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <h3><i class="fas fa-brain"></i> تنبؤات الذكاء الاصطناعي المتقدمة</h3>
                    <div id="advanced-ai-predictions">
                        <div style="text-align: center; padding: 20px;">
                            <p>اضغط على "تنبؤ ذكي متقدم" لبدء التحليل</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Tab -->
        <div id="performance" class="tab-content">
            <div class="grid grid-3">
                <div class="card">
                    <h3><i class="fas fa-tachometer-alt"></i> أداء النظام</h3>
                    <div class="metric">
                        <span>استعلامات API</span>
                        <span class="metric-value" id="perf-api-calls">0</span>
                    </div>
                    <div class="metric">
                        <span>Cache Hits</span>
                        <span class="metric-value metric-positive" id="perf-cache-hits">0</span>
                    </div>
                    <div class="metric">
                        <span>اتصالات نشطة</span>
                        <span class="metric-value" id="perf-connections">0</span>
                    </div>
                    <div class="metric">
                        <span>استهلاك الذاكرة</span>
                        <span class="metric-value" id="perf-memory">-</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-clock"></i> أوقات الاستجابة</h3>
                    <div class="metric">
                        <span>تحليل سريع</span>
                        <span class="metric-value" id="response-analysis">-</span>
                    </div>
                    <div class="metric">
                        <span>تحديث المحفظة</span>
                        <span class="metric-value" id="response-portfolio">-</span>
                    </div>
                    <div class="metric">
                        <span>بيانات الرسم البياني</span>
                        <span class="metric-value" id="response-chart">-</span>
                    </div>
                    <div class="metric">
                        <span>متوسط الاستجابة</span>
                        <span class="metric-value metric-positive" id="response-average">-</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-chart-pie"></i> إحصائيات التحسين</h3>
                    <div class="metric">
                        <span>معدل Cache Hit</span>
                        <span class="metric-value metric-positive" id="cache-hit-rate">-</span>
                    </div>
                    <div class="metric">
                        <span>تحسين الذاكرة</span>
                        <span class="metric-value metric-positive" id="memory-optimization">نشط</span>
                    </div>
                    <div class="metric">
                        <span>ضغط البيانات</span>
                        <span class="metric-value metric-positive" id="data-compression">مفعل</span>
                    </div>
                    <div class="metric">
                        <span>WebSocket</span>
                        <span class="metric-value metric-positive" id="websocket-status">متصل</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p><i class="fas fa-rocket"></i> لوحة التحكم المحسنة v3.0 | أداء عالي | رسوم بيانية تفاعلية | تحديثات فورية</p>
    </div>

    <script>
        // Optimized JavaScript with performance monitoring

        // Global variables
        let socket;
        let chart;
        let volumeChart;
        let currentSymbol = 'BTC-USDT';
        let currentTimeframe = '1hour';
        let performanceStartTime = Date.now();
        let apiCallTimes = [];
        let isConnected = false;

        // Performance monitoring
        function updatePerformanceIndicator() {
            const uptime = Math.floor((Date.now() - performanceStartTime) / 1000);
            const avgResponseTime = apiCallTimes.length > 0 ?
                apiCallTimes.reduce((a, b) => a + b, 0) / apiCallTimes.length : 0;

            document.getElementById('performance-text').textContent =
                `⚡ ${uptime}s | 📡 ${avgResponseTime.toFixed(0)}ms`;
        }

        // Initialize optimized Socket.IO
        function initializeSocket() {
            socket = io({
                transports: ['websocket'],
                upgrade: true,
                rememberUpgrade: true,
                timeout: 20000,
                forceNew: false
            });

            socket.on('connect', function() {
                isConnected = true;
                updateSystemStatus('ws-status', 'متصل', 'online');
                showNotification('تم الاتصال بالخادم المحسن', 'success');

                // Request initial data
                socket.emit('request_portfolio_update');
                requestPerformanceData();
            });

            socket.on('disconnect', function() {
                isConnected = false;
                updateSystemStatus('ws-status', 'منقطع', 'offline');
                showNotification('انقطع الاتصال بالخادم', 'error');
            });

            socket.on('heartbeat', function(data) {
                document.getElementById('perf-connections').textContent = data.active_connections;
            });

            socket.on('portfolio_update', function(data) {
                updatePortfolioDisplay(data);
                showNotification('تم تحديث المحفظة', 'success');
            });

            socket.on('fast_analysis_complete', function(data) {
                updateAnalysisDisplay(data);
                updateOptimizedPortfolioTable();
            });

            socket.on('chart_data_update', function(data) {
                updateChart(data);
            });

            socket.on('background_market_update', function(data) {
                updateQuickMarketData(data);
            });

            socket.on('bulk_analysis_progress', function(data) {
                updateProgress(data.progress, `تحليل ${data.current_symbol} (${data.completed}/${data.total})`);
            });

            socket.on('error', function(data) {
                showNotification(data.message, 'error');
            });
        }

        // Initialize TradingView-style charts
        function initializeCharts() {
            try {
                // Main price chart
                chart = LightweightCharts.createChart(document.getElementById('main-chart'), {
                    width: document.getElementById('main-chart').clientWidth,
                    height: 400,
                    layout: {
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        textColor: '#ffffff',
                    },
                    grid: {
                        vertLines: {
                            color: 'rgba(255, 255, 255, 0.1)',
                        },
                        horzLines: {
                            color: 'rgba(255, 255, 255, 0.1)',
                        },
                    },
                    crosshair: {
                        mode: LightweightCharts.CrosshairMode.Normal,
                    },
                    rightPriceScale: {
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                    },
                    timeScale: {
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                        timeVisible: true,
                        secondsVisible: false,
                    },
                });

                // Volume chart
                volumeChart = LightweightCharts.createChart(document.getElementById('volume-chart'), {
                    width: document.getElementById('volume-chart').clientWidth,
                    height: 150,
                    layout: {
                        backgroundColor: 'rgba(0, 0, 0, 0)',
                        textColor: '#ffffff',
                    },
                    grid: {
                        vertLines: {
                            color: 'rgba(255, 255, 255, 0.1)',
                        },
                        horzLines: {
                            color: 'rgba(255, 255, 255, 0.1)',
                        },
                    },
                    rightPriceScale: {
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                    },
                    timeScale: {
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                        visible: false,
                    },
                });

                // Add candlestick series
                window.candlestickSeries = chart.addCandlestickSeries({
                    upColor: '#4caf50',
                    downColor: '#f44336',
                    borderDownColor: '#f44336',
                    borderUpColor: '#4caf50',
                    wickDownColor: '#f44336',
                    wickUpColor: '#4caf50',
                });

                // Add volume series
                window.volumeSeries = volumeChart.addHistogramSeries({
                    color: '#26a69a',
                    priceFormat: {
                        type: 'volume',
                    },
                    priceScaleId: '',
                });

                // Add moving averages
                window.sma20Series = chart.addLineSeries({
                    color: '#ff9800',
                    lineWidth: 2,
                    title: 'SMA 20',
                });

                window.sma50Series = chart.addLineSeries({
                    color: '#2196f3',
                    lineWidth: 2,
                    title: 'SMA 50',
                });

                // Request initial chart data
                requestChartData(currentSymbol, currentTimeframe);

            } catch (error) {
                console.error('Error initializing charts:', error);
                showNotification('خطأ في تحميل الرسوم البيانية', 'error');
            }
        }

        // Chart functions
        function requestChartData(symbol, timeframe) {
            if (socket && isConnected) {
                const startTime = Date.now();
                socket.emit('request_chart_data', {
                    symbol: symbol,
                    timeframe: timeframe
                });

                // Track response time
                setTimeout(() => {
                    const responseTime = Date.now() - startTime;
                    apiCallTimes.push(responseTime);
                    if (apiCallTimes.length > 10) apiCallTimes.shift();
                    document.getElementById('response-chart').textContent = responseTime + 'ms';
                }, 100);
            }
        }

        function updateChart(data) {
            try {
                if (data.data && data.data.candles) {
                    // Update candlestick data
                    window.candlestickSeries.setData(data.data.candles);

                    // Update volume data
                    const volumeData = data.data.candles.map(candle => ({
                        time: candle.time,
                        value: candle.volume,
                        color: candle.close >= candle.open ? '#4caf50' : '#f44336'
                    }));
                    window.volumeSeries.setData(volumeData);

                    // Update indicators if available
                    if (data.data.indicators) {
                        const indicators = data.data.indicators;

                        // SMA 20
                        if (indicators.sma_20) {
                            const sma20Data = data.data.candles.map((candle, index) => ({
                                time: candle.time,
                                value: indicators.sma_20[index]
                            })).filter(item => item.value > 0);
                            window.sma20Series.setData(sma20Data);
                        }

                        // SMA 50
                        if (indicators.sma_50) {
                            const sma50Data = data.data.candles.map((candle, index) => ({
                                time: candle.time,
                                value: indicators.sma_50[index]
                            })).filter(item => item.value > 0);
                            window.sma50Series.setData(sma50Data);
                        }
                    }

                    // Update chart info
                    const lastCandle = data.data.candles[data.data.candles.length - 1];
                    if (lastCandle) {
                        document.getElementById('chart-price').textContent = `$${lastCandle.close.toFixed(6)}`;
                        const change = ((lastCandle.close - lastCandle.open) / lastCandle.open * 100);
                        document.getElementById('chart-change').textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
                        document.getElementById('chart-change').className = change >= 0 ? 'metric-positive' : 'metric-negative';
                    }
                }
            } catch (error) {
                console.error('Error updating chart:', error);
            }
        }

        function changeChartSymbol() {
            const select = document.getElementById('symbol-select');
            currentSymbol = select.value;
            document.getElementById('chart-symbol').textContent = currentSymbol;
            requestChartData(currentSymbol, currentTimeframe);
        }

        function changeTimeframe(timeframe) {
            // Update active button
            document.querySelectorAll('.timeframe-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            currentTimeframe = timeframe;
            document.getElementById('chart-timeframe').textContent = timeframe;
            requestChartData(currentSymbol, currentTimeframe);
        }

        // Tab management
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // Resize charts when charts tab is shown
            if (tabName === 'charts' && chart) {
                setTimeout(() => {
                    chart.resize(document.getElementById('main-chart').clientWidth, 400);
                    volumeChart.resize(document.getElementById('volume-chart').clientWidth, 150);
                }, 100);
            }
        }

        // Optimized action functions
        function fastAnalyzeAll() {
            if (!isConnected) {
                showNotification('غير متصل بالخادم', 'error');
                return;
            }

            const btn = document.getElementById('fast-analysis-btn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحليل السريع...';

            const startTime = Date.now();
            socket.emit('request_bulk_analysis');

            setTimeout(() => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-rocket"></i> تحليل سريع شامل';
                const responseTime = Date.now() - startTime;
                document.getElementById('response-analysis').textContent = responseTime + 'ms';
            }, 15000);
        }

        function fastUpdatePortfolio() {
            if (!isConnected) {
                showNotification('غير متصل بالخادم', 'error');
                return;
            }

            const btn = document.getElementById('portfolio-update-btn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> تحديث سريع...';

            const startTime = Date.now();
            socket.emit('request_portfolio_update');

            setTimeout(() => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-sync-alt"></i> تحديث المحفظة السريع';
                const responseTime = Date.now() - startTime;
                document.getElementById('response-portfolio').textContent = responseTime + 'ms';
            }, 8000);
        }

        function runAIPrediction() {
            showNotification('تم تشغيل تنبؤ الذكاء الاصطناعي المتقدم', 'success');
        }

        function openChartsTab() {
            showTab('charts');
            document.querySelector('[onclick="showTab(\'charts\')"]').click();
        }

        function exportOptimizedData() {
            showNotification('تم تصدير البيانات المحسنة', 'success');
        }

        // Utility functions
        function updateSystemStatus(elementId, status, type) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = status;
                const indicator = element.previousElementSibling;
                if (indicator) {
                    indicator.className = `status-indicator status-${type}`;
                }
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const text = document.getElementById('notification-text');

            text.textContent = message;
            notification.className = `notification show ${type}`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        function updateProgress(percentage, text) {
            const progressCard = document.getElementById('progress-card');
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');

            if (percentage > 0) {
                progressCard.style.display = 'block';
                progressFill.style.width = percentage + '%';
                progressText.textContent = text || 'جاري المعالجة...';

                if (percentage >= 100) {
                    setTimeout(() => {
                        progressCard.style.display = 'none';
                    }, 2000);
                }
            } else {
                progressCard.style.display = 'none';
            }
        }

        function requestPerformanceData() {
            if (socket && isConnected) {
                fetch('/api/performance')
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('api-calls').textContent = data.api_calls;
                        document.getElementById('cache-hits').textContent = data.cache_hits;
                        document.getElementById('perf-api-calls').textContent = data.api_calls;
                        document.getElementById('perf-cache-hits').textContent = data.cache_hits;
                        document.getElementById('perf-memory').textContent = data.memory_usage.rss_mb + ' MB';

                        const hitRate = data.api_calls > 0 ? (data.cache_hits / data.api_calls * 100).toFixed(1) : 0;
                        document.getElementById('cache-hit-rate').textContent = hitRate + '%';
                    })
                    .catch(error => console.error('Performance data error:', error));
            }
        }

        // Initialize everything
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
            initializeCharts();

            // Update performance indicator every 5 seconds
            setInterval(updatePerformanceIndicator, 5000);

            // Request performance data every 10 seconds
            setInterval(requestPerformanceData, 10000);

            // Update timestamps
            setInterval(() => {
                document.getElementById('market-last-update').textContent = new Date().toLocaleTimeString('ar-SA');
            }, 1000);
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (chart && volumeChart) {
                chart.resize(document.getElementById('main-chart').clientWidth, 400);
                volumeChart.resize(document.getElementById('volume-chart').clientWidth, 150);
            }
        });
    </script>
</body>
</html>
        """
        return html_template

    def run(self, host='127.0.0.1', port=5003, debug=False):
        """Run the optimized dashboard"""
        print(f"""
        ╔══════════════════════════════════════════════════════════════╗
        ║                                                              ║
        ║        🚀 لوحة التحكم المحسنة - بوت التداول الذكي         ║
        ║                                                              ║
        ╚══════════════════════════════════════════════════════════════╝

        🌐 رابط لوحة التحكم المحسنة: http://{host}:{port}
        🚀 بدء تشغيل الخادم عالي الأداء...

        ⚡ التحسينات المطبقة:
        🔧 WebSocket للتحديثات الفورية
        📊 رسوم بيانية تفاعلية (TradingView Style)
        💾 نظام Cache ذكي
        🎯 تحليل سريع ومحسن
        📈 مؤشرات تقنية متقدمة
        🔄 تحديثات مباشرة محسنة
        💻 استهلاك ذاكرة محسن
        📱 تصميم متجاوب محسن
        """)

        # Start optimized background updates
        self.start_optimized_background_updates()

        try:
            self.socketio.run(self.app, host=host, port=port, debug=debug)
        finally:
            self.stop_optimized_background_updates()


def main():
    """Main function"""
    try:
        dashboard = OptimizedTradingDashboard()
        dashboard.run(host='0.0.0.0', port=5003, debug=False)

    except KeyboardInterrupt:
        print("\n👋 تم إيقاف لوحة التحكم المحسنة بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل لوحة التحكم المحسنة: {e}")
        if not FLASK_AVAILABLE:
            print("\n💡 لتثبيت المتطلبات:")
            print("   pip install flask flask-socketio flask-caching psutil")


if __name__ == "__main__":
    main()
