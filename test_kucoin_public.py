#!/usr/bin/env python3
"""
KuCoin Public API Test - No Authentication Required
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
import pandas as pd

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.kucoin_client import KuCoinClient, KuCoinAPIError
from src.analysis import MarketAnalyzer
from src.core.logger import logger


class KuCoinPublicClient(KuCoinClient):
    """KuCoin client for public API only (no authentication)"""
    
    def __init__(self):
        # Initialize without credentials
        super().__init__(api_key="", secret_key="", passphrase="", sandbox=False)
    
    async def connect(self):
        """Connect without testing credentials"""
        if self.session is None:
            import aiohttp
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
        
        self.connected = True
        logger.success("✅ Connected to KuCoin Public API")


async def test_kucoin_public_api():
    """Test KuCoin public API functionality"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🤖 KuCoin Public API Test - No Authentication        ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    client = KuCoinPublicClient()
    
    try:
        async with client:
            print("✅ Connected to KuCoin Public API")
            
            # Test 1: Server Time
            print("\n🔍 Testing Server Time...")
            server_time = await client.get_server_time()
            current_time = datetime.fromtimestamp(server_time / 1000)
            print(f"   ✅ Server time: {current_time}")
            
            # Test 2: Get Symbols
            print("\n🔍 Testing Symbol Information...")
            symbols = await client.get_symbols()
            print(f"   ✅ Available symbols: {len(symbols)}")
            
            # Find USDT pairs
            usdt_pairs = [s for s in symbols if s['quoteCurrency'] == 'USDT' and s['enableTrading']]
            print(f"   ✅ Active USDT pairs: {len(usdt_pairs)}")
            
            # Show popular pairs
            popular_symbols = ['BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'DOT-USDT']
            available_popular = [s for s in symbols if s['symbol'] in popular_symbols]
            
            print(f"   📊 Popular pairs available:")
            for symbol in available_popular:
                print(f"      {symbol['symbol']}: {symbol['baseCurrency']}/{symbol['quoteCurrency']}")
            
            # Test 3: Get All Tickers
            print("\n🔍 Testing All Tickers...")
            all_tickers = await client.get_all_tickers()
            tickers_data = all_tickers.get('ticker', [])
            print(f"   ✅ Retrieved {len(tickers_data)} ticker data")
            
            # Find top gainers and losers
            usdt_tickers = [t for t in tickers_data if t.get('symbol', '').endswith('-USDT')]
            
            if usdt_tickers:
                # Top gainers
                gainers = sorted(usdt_tickers, key=lambda x: float(x.get('changeRate', 0)), reverse=True)[:5]
                print(f"\n   📈 Top 5 Gainers (USDT pairs):")
                for gainer in gainers:
                    change_rate = float(gainer.get('changeRate', 0)) * 100
                    last_price = float(gainer.get('last', 0))
                    print(f"      {gainer['symbol']}: ${last_price:,.4f} ({change_rate:+.2f}%)")
                
                # Top losers
                losers = sorted(usdt_tickers, key=lambda x: float(x.get('changeRate', 0)))[:5]
                print(f"\n   📉 Top 5 Losers (USDT pairs):")
                for loser in losers:
                    change_rate = float(loser.get('changeRate', 0)) * 100
                    last_price = float(loser.get('last', 0))
                    print(f"      {loser['symbol']}: ${last_price:,.4f} ({change_rate:+.2f}%)")
            
            # Test 4: Individual Ticker
            print("\n🔍 Testing Individual Ticker (BTC-USDT)...")
            btc_ticker = await client.get_ticker('BTC-USDT')
            
            print(f"   ✅ BTC-USDT Details:")
            print(f"      Last Price: ${float(btc_ticker.get('last', 0)):,.2f}")
            print(f"      24h Change: {float(btc_ticker.get('changeRate', 0)) * 100:+.2f}%")
            print(f"      24h Volume: {float(btc_ticker.get('vol', 0)):,.2f} BTC")
            print(f"      24h High: ${float(btc_ticker.get('high', 0)):,.2f}")
            print(f"      24h Low: ${float(btc_ticker.get('low', 0)):,.2f}")
            print(f"      Best Bid: ${float(btc_ticker.get('buy', 0)):,.2f}")
            print(f"      Best Ask: ${float(btc_ticker.get('sell', 0)):,.2f}")
            
            # Calculate spread
            bid = float(btc_ticker.get('buy', 0))
            ask = float(btc_ticker.get('sell', 0))
            if bid and ask:
                spread = ask - bid
                spread_percent = (spread / ask) * 100
                print(f"      Spread: ${spread:.2f} ({spread_percent:.3f}%)")
            
            # Test 5: Order Book
            print("\n🔍 Testing Order Book (BTC-USDT)...")
            orderbook = await client.get_orderbook('BTC-USDT')
            
            bids = orderbook.get('bids', [])
            asks = orderbook.get('asks', [])
            
            print(f"   ✅ Order Book:")
            print(f"      Bid levels: {len(bids)}")
            print(f"      Ask levels: {len(asks)}")
            
            if bids and asks:
                print(f"      Top 3 Bids:")
                for i, bid in enumerate(bids[:3]):
                    print(f"        {i+1}. ${float(bid[0]):,.2f} × {float(bid[1]):,.4f}")
                
                print(f"      Top 3 Asks:")
                for i, ask in enumerate(asks[:3]):
                    print(f"        {i+1}. ${float(ask[0]):,.2f} × {float(ask[1]):,.4f}")
            
            # Test 6: Klines Data
            print("\n🔍 Testing Klines Data (BTC-USDT, 1 hour)...")
            klines = await client.get_klines('BTC-USDT', '1hour')
            
            print(f"   ✅ Klines Data:")
            print(f"      Retrieved: {len(klines)} candles")
            
            if klines:
                # Show latest 3 candles
                print(f"      Latest 3 candles:")
                for i, kline in enumerate(klines[:3]):
                    candle_time = datetime.fromtimestamp(int(kline[0]))
                    open_price = float(kline[1])
                    close_price = float(kline[2])
                    high_price = float(kline[3])
                    low_price = float(kline[4])
                    volume = float(kline[5])
                    
                    change = ((close_price - open_price) / open_price) * 100
                    print(f"        {i+1}. {candle_time.strftime('%Y-%m-%d %H:%M')}")
                    print(f"           OHLC: ${open_price:,.0f} | ${high_price:,.0f} | ${low_price:,.0f} | ${close_price:,.0f}")
                    print(f"           Change: {change:+.2f}% | Volume: {volume:,.2f}")
            
            print("\n✅ All KuCoin public API tests passed!")
            return True
            
    except KuCoinAPIError as e:
        print(f"❌ KuCoin API Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


async def test_kucoin_technical_analysis():
    """Test technical analysis with KuCoin data"""
    print(f"\n🔍 Testing Technical Analysis with KuCoin Data...")
    print("=" * 70)
    
    client = KuCoinPublicClient()
    analyzer = MarketAnalyzer()
    
    try:
        async with client:
            # Test symbols
            test_symbols = ['BTC-USDT', 'ETH-USDT', 'ADA-USDT']
            
            print(f"📊 Performing technical analysis on {len(test_symbols)} symbols...")
            
            for symbol in test_symbols:
                try:
                    print(f"\n📈 Analyzing {symbol}...")
                    
                    # Get historical data (last 100 hours)
                    klines = await client.get_klines(symbol, '1hour')
                    
                    if not klines or len(klines) < 50:
                        print(f"   ⚠️  Insufficient data for {symbol}")
                        continue
                    
                    # Convert to DataFrame
                    # KuCoin format: [time, open, close, high, low, volume, turnover]
                    data = []
                    for kline in reversed(klines[:100]):  # Take last 100, reverse for chronological order
                        data.append({
                            'open': float(kline[1]),
                            'high': float(kline[3]),
                            'low': float(kline[4]),
                            'close': float(kline[2]),
                            'volume': float(kline[5]),
                            'quote_volume': float(kline[6]),
                            'trades_count': 100  # Default value
                        })
                    
                    df = pd.DataFrame(data)
                    df.index = pd.date_range(end=datetime.now(), periods=len(df), freq='H')
                    
                    print(f"   ✅ Data prepared: {len(df)} periods")
                    print(f"   📊 Price range: ${df['low'].min():,.2f} - ${df['high'].max():,.2f}")
                    print(f"   📈 Current price: ${df['close'].iloc[-1]:,.2f}")
                    
                    # Perform technical analysis
                    signal = await analyzer.analyze_symbol(symbol.replace('-', ''), df)
                    
                    print(f"   🎯 Technical Analysis Result:")
                    print(f"      Signal: {signal.signal_type}")
                    print(f"      Confidence: {signal.confidence:.1%}")
                    print(f"      Strength: {signal.strength:.3f}")
                    print(f"      Risk Level: {signal.risk_level}")
                    
                    if signal.target_price:
                        potential_return = ((signal.target_price - signal.entry_price) / signal.entry_price) * 100
                        print(f"      Target: ${signal.target_price:,.2f} ({potential_return:+.2f}%)")
                    
                    if signal.stop_loss:
                        risk_percent = ((signal.stop_loss - signal.entry_price) / signal.entry_price) * 100
                        print(f"      Stop Loss: ${signal.stop_loss:,.2f} ({risk_percent:+.2f}%)")
                    
                    # Show technical indicators
                    active_indicators = []
                    for name, tech_signal in signal.technical_signals.items():
                        if tech_signal.signal != 'HOLD':
                            active_indicators.append(f"{name}:{tech_signal.signal}")
                    
                    if active_indicators:
                        print(f"      Active Indicators: {', '.join(active_indicators)}")
                    
                    # Show patterns
                    if signal.pattern_signals:
                        bullish = len([p for p in signal.pattern_signals if p.pattern_type == 'BULLISH'])
                        bearish = len([p for p in signal.pattern_signals if p.pattern_type == 'BEARISH'])
                        print(f"      Patterns: {bullish} Bullish, {bearish} Bearish")
                    
                    print(f"      Reasoning: {signal.reasoning}")
                    
                except Exception as e:
                    print(f"   ❌ Error analyzing {symbol}: {e}")
            
            print("\n✅ Technical analysis with KuCoin data completed!")
            return True
            
    except Exception as e:
        print(f"❌ Technical analysis failed: {e}")
        return False


async def main():
    """Main test function"""
    try:
        # Test 1: Public API functionality
        success1 = await test_kucoin_public_api()
        
        # Test 2: Technical analysis integration
        success2 = await test_kucoin_technical_analysis()
        
        # Summary
        total_tests = 2
        passed_tests = sum([success1, success2])
        
        print(f"\n" + "=" * 70)
        print(f"📊 KuCoin Public API Test Results: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 All KuCoin public API tests passed!")
            print("\n✨ KuCoin integration is working excellently!")
            print("\n💡 Successfully tested:")
            print("   ✅ Real-time market data from KuCoin")
            print("   ✅ Price tickers and order books")
            print("   ✅ Historical candlestick data")
            print("   ✅ Technical analysis on KuCoin data")
            print("   ✅ Trading signal generation")
            
            print("\n🔧 To enable account features:")
            print("   Add KUCOIN_PASSPHRASE to your .env file")
            print("   This will enable:")
            print("   • Account balance checking")
            print("   • Order placement and management")
            print("   • Portfolio tracking")
        else:
            print("⚠️  Some tests failed. Check the errors above.")
        
        return passed_tests == total_tests
        
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        return False


if __name__ == "__main__":
    asyncio.run(main())
