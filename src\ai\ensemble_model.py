"""
Ensemble Model for Advanced Price Prediction
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import asyncio
from dataclasses import dataclass

from .price_predictor import PricePredictor
from .sentiment_analyzer import SentimentAnalyzer
from ..analysis import MarketAnalyzer
from ..core.logger import logger


@dataclass
class PredictionResult:
    """Comprehensive prediction result"""
    symbol: str
    current_price: float
    predicted_price: float
    price_change_percent: float
    confidence: float
    time_horizon: str
    technical_signal: str
    sentiment_signal: str
    ensemble_signal: str
    risk_level: str
    reasoning: str
    timestamp: datetime
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'current_price': self.current_price,
            'predicted_price': self.predicted_price,
            'price_change_percent': self.price_change_percent,
            'confidence': self.confidence,
            'time_horizon': self.time_horizon,
            'technical_signal': self.technical_signal,
            'sentiment_signal': self.sentiment_signal,
            'ensemble_signal': self.ensemble_signal,
            'risk_level': self.risk_level,
            'reasoning': self.reasoning,
            'timestamp': self.timestamp.isoformat()
        }


class EnsembleModel:
    """
    Advanced ensemble model combining technical analysis, sentiment analysis, and AI predictions
    """
    
    def __init__(self):
        self.price_predictor = PricePredictor(use_deep_learning=True)
        self.sentiment_analyzer = SentimentAnalyzer()
        self.market_analyzer = MarketAnalyzer()
        
        # Model weights (can be optimized)
        self.weights = {
            'technical': 0.4,
            'sentiment': 0.2,
            'ai_prediction': 0.4
        }
        
        # Prediction history
        self.prediction_history = []
        
        # Performance tracking
        self.model_accuracy = {}
        
        logger.info("Ensemble Model initialized")
    
    async def train_models(self, df: pd.DataFrame, symbol: str, target_col: str = 'close') -> Dict[str, Any]:
        """Train all models in the ensemble"""
        try:
            logger.info(f"Training ensemble models for {symbol}")
            
            # Prepare data for AI models
            features_df, feature_cols = self.price_predictor.prepare_data(df, target_col)
            
            if len(feature_cols) == 0:
                logger.error("No features available for training")
                return {}
            
            # Scale features
            scaled_df, scaler = self.price_predictor.feature_engineer.scale_features(
                features_df, feature_cols, 'standard'
            )
            self.price_predictor.scalers['features'] = scaler
            
            # Create sequences for deep learning
            sequence_length = 60
            X, y = self.price_predictor.feature_engineer.create_sequences(
                scaled_df, feature_cols, target_col, sequence_length
            )
            
            if len(X) == 0:
                logger.error("No sequences created for training")
                return {}
            
            # Split data
            split_idx = int(len(X) * 0.8)
            val_split_idx = int(len(X) * 0.9)
            
            X_train, X_val, X_test = X[:split_idx], X[split_idx:val_split_idx], X[val_split_idx:]
            y_train, y_val, y_test = y[:split_idx], y[split_idx:val_split_idx], y[val_split_idx:]
            
            # Train deep learning models
            dl_results = self.price_predictor.train_deep_learning_models(X_train, y_train, X_val, y_val)
            
            # Train traditional models
            traditional_results = self.price_predictor.train_traditional_models(X_train, y_train)
            
            # Evaluate all models
            evaluation_results = self.price_predictor.evaluate_models(X_test, y_test)
            
            # Train sentiment analyzer (collect initial sentiment data)
            async with self.sentiment_analyzer:
                await self.sentiment_analyzer.analyze_market_sentiment(symbol.replace('USDT', '').lower())
            
            training_results = {
                'deep_learning': dl_results,
                'traditional': traditional_results,
                'evaluation': evaluation_results,
                'data_shape': {
                    'features': len(feature_cols),
                    'sequences': len(X),
                    'train_size': len(X_train),
                    'val_size': len(X_val),
                    'test_size': len(X_test)
                }
            }
            
            logger.info(f"Ensemble training complete for {symbol}")
            return training_results
            
        except Exception as e:
            logger.error(f"Error training ensemble models: {e}")
            return {}
    
    async def predict_price(self, df: pd.DataFrame, symbol: str, time_horizon: str = '1h') -> PredictionResult:
        """Generate comprehensive price prediction"""
        try:
            logger.info(f"Generating prediction for {symbol} ({time_horizon})")
            
            current_price = df['close'].iloc[-1]
            
            # 1. Technical Analysis
            technical_signal = await self.market_analyzer.analyze_symbol(symbol, df)
            
            # 2. Sentiment Analysis
            async with self.sentiment_analyzer:
                sentiment_data = await self.sentiment_analyzer.analyze_market_sentiment(
                    symbol.replace('USDT', '').lower()
                )
                sentiment_signal = self.sentiment_analyzer.get_sentiment_signal(
                    symbol.replace('USDT', '').lower()
                )
            
            # 3. AI Price Prediction
            ai_prediction = await self._get_ai_prediction(df, symbol)
            
            # 4. Ensemble Decision
            ensemble_result = self._combine_predictions(
                technical_signal, sentiment_signal, ai_prediction, current_price
            )
            
            # Create comprehensive result
            result = PredictionResult(
                symbol=symbol,
                current_price=current_price,
                predicted_price=ensemble_result['predicted_price'],
                price_change_percent=ensemble_result['price_change_percent'],
                confidence=ensemble_result['confidence'],
                time_horizon=time_horizon,
                technical_signal=technical_signal.signal_type,
                sentiment_signal=sentiment_signal['signal'],
                ensemble_signal=ensemble_result['signal'],
                risk_level=ensemble_result['risk_level'],
                reasoning=ensemble_result['reasoning'],
                timestamp=datetime.now()
            )
            
            # Store prediction
            self.prediction_history.append(result)
            
            # Keep only recent predictions (last 1000)
            self.prediction_history = self.prediction_history[-1000:]
            
            logger.info(f"Prediction complete for {symbol}: {result.ensemble_signal} ({result.confidence:.1%})")
            return result
            
        except Exception as e:
            logger.error(f"Error generating prediction: {e}")
            return self._get_default_prediction(symbol, df['close'].iloc[-1] if not df.empty else 0)
    
    async def _get_ai_prediction(self, df: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """Get AI model prediction"""
        try:
            if not self.price_predictor.models:
                logger.warning("No trained AI models available")
                return {'prediction': df['close'].iloc[-1], 'confidence': 0.0, 'model': 'none'}
            
            # Prepare data
            features_df, feature_cols = self.price_predictor.prepare_data(df)
            
            if not feature_cols:
                return {'prediction': df['close'].iloc[-1], 'confidence': 0.0, 'model': 'none'}
            
            # Scale features
            if 'features' in self.price_predictor.scalers:
                scaler = self.price_predictor.scalers['features']
                scaled_values = scaler.transform(features_df[feature_cols].iloc[-60:])
                features_df.loc[features_df.index[-60:], feature_cols] = scaled_values
            
            # Create sequence
            sequence_length = 60
            if len(features_df) >= sequence_length:
                X = features_df[feature_cols].iloc[-sequence_length:].values.reshape(1, sequence_length, -1)
                
                # Get prediction from best model
                prediction = self.price_predictor.predict(X, 'best')
                
                if len(prediction) > 0:
                    # Calculate confidence based on model performance
                    confidence = 0.7  # Default confidence
                    if self.price_predictor.model_performance:
                        best_model_name = min(self.price_predictor.model_performance.items(), 
                                            key=lambda x: x[1]['rmse'])[0]
                        r2_score = self.price_predictor.model_performance[best_model_name].get('r2', 0)
                        confidence = max(0.1, min(0.9, r2_score))
                    
                    return {
                        'prediction': float(prediction[0]),
                        'confidence': confidence,
                        'model': 'ensemble_ai'
                    }
            
            return {'prediction': df['close'].iloc[-1], 'confidence': 0.0, 'model': 'fallback'}
            
        except Exception as e:
            logger.error(f"Error getting AI prediction: {e}")
            return {'prediction': df['close'].iloc[-1], 'confidence': 0.0, 'model': 'error'}
    
    def _combine_predictions(self, technical_signal, sentiment_signal: Dict, ai_prediction: Dict, 
                           current_price: float) -> Dict[str, Any]:
        """Combine all predictions into ensemble result"""
        try:
            # Extract signals
            tech_signal = technical_signal.signal_type
            tech_confidence = technical_signal.confidence
            
            sent_signal = sentiment_signal['signal']
            sent_confidence = sentiment_signal['strength']
            
            ai_price = ai_prediction['prediction']
            ai_confidence = ai_prediction['confidence']
            
            # Calculate price change from AI prediction
            ai_price_change = ((ai_price - current_price) / current_price) * 100
            
            # Convert signals to numeric scores (-1 to 1)
            tech_score = self._signal_to_score(tech_signal) * tech_confidence
            sent_score = self._signal_to_score(sent_signal) * sent_confidence
            ai_score = np.sign(ai_price_change) * min(abs(ai_price_change) / 5, 1) * ai_confidence
            
            # Weighted ensemble score
            ensemble_score = (
                tech_score * self.weights['technical'] +
                sent_score * self.weights['sentiment'] +
                ai_score * self.weights['ai_prediction']
            )
            
            # Convert ensemble score to signal
            if ensemble_score > 0.1:
                ensemble_signal = 'BUY'
            elif ensemble_score < -0.1:
                ensemble_signal = 'SELL'
            else:
                ensemble_signal = 'HOLD'
            
            # Calculate predicted price (weighted average)
            tech_price = current_price * (1 + (tech_score * 0.02))  # 2% max change from technical
            sent_price = current_price * (1 + (sent_score * 0.01))  # 1% max change from sentiment
            
            predicted_price = (
                tech_price * self.weights['technical'] +
                sent_price * self.weights['sentiment'] +
                ai_price * self.weights['ai_prediction']
            )
            
            price_change_percent = ((predicted_price - current_price) / current_price) * 100
            
            # Calculate overall confidence
            confidence = (
                tech_confidence * self.weights['technical'] +
                sent_confidence * self.weights['sentiment'] +
                ai_confidence * self.weights['ai_prediction']
            )
            
            # Determine risk level
            risk_level = self._calculate_risk_level(
                abs(price_change_percent), confidence, technical_signal.risk_level
            )
            
            # Generate reasoning
            reasoning = self._generate_reasoning(
                tech_signal, tech_confidence, sent_signal, sent_confidence,
                ai_price_change, ai_confidence, ensemble_signal
            )
            
            return {
                'predicted_price': predicted_price,
                'price_change_percent': price_change_percent,
                'confidence': confidence,
                'signal': ensemble_signal,
                'risk_level': risk_level,
                'reasoning': reasoning,
                'component_scores': {
                    'technical': tech_score,
                    'sentiment': sent_score,
                    'ai': ai_score,
                    'ensemble': ensemble_score
                }
            }
            
        except Exception as e:
            logger.error(f"Error combining predictions: {e}")
            return {
                'predicted_price': current_price,
                'price_change_percent': 0.0,
                'confidence': 0.5,
                'signal': 'HOLD',
                'risk_level': 'MEDIUM',
                'reasoning': 'Error in ensemble calculation',
                'component_scores': {}
            }
    
    def _signal_to_score(self, signal: str) -> float:
        """Convert signal to numeric score"""
        signal_map = {
            'BUY': 1.0,
            'SELL': -1.0,
            'HOLD': 0.0
        }
        return signal_map.get(signal.upper(), 0.0)
    
    def _calculate_risk_level(self, price_change: float, confidence: float, tech_risk: str) -> str:
        """Calculate overall risk level"""
        try:
            # Base risk from technical analysis
            risk_score = {'LOW': 1, 'MEDIUM': 2, 'HIGH': 3}.get(tech_risk, 2)
            
            # Adjust for price volatility
            if price_change > 5:
                risk_score += 1
            elif price_change > 10:
                risk_score += 2
            
            # Adjust for confidence
            if confidence < 0.3:
                risk_score += 1
            elif confidence > 0.7:
                risk_score -= 1
            
            # Convert back to risk level
            if risk_score <= 1:
                return 'LOW'
            elif risk_score <= 3:
                return 'MEDIUM'
            else:
                return 'HIGH'
                
        except Exception:
            return 'MEDIUM'
    
    def _generate_reasoning(self, tech_signal: str, tech_conf: float, sent_signal: str, 
                          sent_conf: float, ai_change: float, ai_conf: float, 
                          ensemble_signal: str) -> str:
        """Generate human-readable reasoning"""
        try:
            reasons = []
            
            # Technical analysis
            if tech_conf > 0.6:
                reasons.append(f"Technical analysis shows {tech_signal} signal ({tech_conf:.1%} confidence)")
            
            # Sentiment analysis
            if sent_conf > 0.3:
                reasons.append(f"Market sentiment is {sent_signal.lower()} ({sent_conf:.1%} strength)")
            
            # AI prediction
            if ai_conf > 0.5:
                direction = "upward" if ai_change > 0 else "downward"
                reasons.append(f"AI models predict {direction} movement ({ai_change:+.1f}%)")
            
            # Ensemble conclusion
            if reasons:
                reasoning = f"Ensemble {ensemble_signal}: " + "; ".join(reasons)
            else:
                reasoning = f"Ensemble {ensemble_signal}: Limited signal strength across models"
            
            return reasoning
            
        except Exception:
            return f"Ensemble {ensemble_signal}: Analysis complete"
    
    def _get_default_prediction(self, symbol: str, current_price: float) -> PredictionResult:
        """Get default prediction when analysis fails"""
        return PredictionResult(
            symbol=symbol,
            current_price=current_price,
            predicted_price=current_price,
            price_change_percent=0.0,
            confidence=0.5,
            time_horizon='1h',
            technical_signal='HOLD',
            sentiment_signal='HOLD',
            ensemble_signal='HOLD',
            risk_level='MEDIUM',
            reasoning='Default prediction due to analysis error',
            timestamp=datetime.now()
        )
    
    async def analyze_multiple_symbols(self, symbols_data: Dict[str, pd.DataFrame]) -> Dict[str, PredictionResult]:
        """Analyze multiple symbols simultaneously"""
        try:
            logger.info(f"Analyzing {len(symbols_data)} symbols with ensemble model")
            
            results = {}
            
            # Process symbols concurrently
            tasks = []
            for symbol, df in symbols_data.items():
                task = self.predict_price(df, symbol)
                tasks.append((symbol, task))
            
            # Wait for all predictions
            for symbol, task in tasks:
                try:
                    result = await task
                    results[symbol] = result
                except Exception as e:
                    logger.error(f"Error analyzing {symbol}: {e}")
                    results[symbol] = self._get_default_prediction(symbol, 
                                                                 symbols_data[symbol]['close'].iloc[-1])
            
            logger.info(f"Multi-symbol analysis complete: {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"Error in multi-symbol analysis: {e}")
            return {}
    
    def get_model_performance(self) -> Dict[str, Any]:
        """Get performance metrics for the ensemble model"""
        try:
            if not self.prediction_history:
                return {'message': 'No prediction history available'}
            
            # Calculate basic statistics
            recent_predictions = self.prediction_history[-100:]  # Last 100 predictions
            
            signal_distribution = {}
            confidence_stats = []
            
            for pred in recent_predictions:
                signal = pred.ensemble_signal
                signal_distribution[signal] = signal_distribution.get(signal, 0) + 1
                confidence_stats.append(pred.confidence)
            
            return {
                'total_predictions': len(self.prediction_history),
                'recent_predictions': len(recent_predictions),
                'signal_distribution': signal_distribution,
                'average_confidence': np.mean(confidence_stats) if confidence_stats else 0,
                'confidence_std': np.std(confidence_stats) if confidence_stats else 0,
                'model_weights': self.weights,
                'ai_model_performance': self.price_predictor.model_performance
            }
            
        except Exception as e:
            logger.error(f"Error calculating model performance: {e}")
            return {'error': str(e)}
    
    def update_model_weights(self, new_weights: Dict[str, float]):
        """Update ensemble model weights"""
        try:
            # Validate weights
            if abs(sum(new_weights.values()) - 1.0) > 0.01:
                raise ValueError("Weights must sum to 1.0")
            
            required_keys = {'technical', 'sentiment', 'ai_prediction'}
            if not required_keys.issubset(new_weights.keys()):
                raise ValueError(f"Missing required weight keys: {required_keys}")
            
            self.weights.update(new_weights)
            logger.info(f"Model weights updated: {self.weights}")
            
        except Exception as e:
            logger.error(f"Error updating model weights: {e}")
    
    def save_ensemble(self, filepath: str):
        """Save ensemble model"""
        try:
            # Save AI models
            self.price_predictor.save_models(f"{filepath}_ai")
            
            # Save ensemble metadata
            import joblib
            ensemble_data = {
                'weights': self.weights,
                'prediction_history': self.prediction_history[-100:],  # Save recent history
                'model_accuracy': self.model_accuracy
            }
            joblib.dump(ensemble_data, f"{filepath}_ensemble.pkl")
            
            logger.info(f"Ensemble model saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving ensemble model: {e}")
    
    def load_ensemble(self, filepath: str):
        """Load ensemble model"""
        try:
            # Load AI models
            self.price_predictor.load_models(f"{filepath}_ai")
            
            # Load ensemble metadata
            import joblib
            ensemble_data = joblib.load(f"{filepath}_ensemble.pkl")
            
            self.weights = ensemble_data['weights']
            self.prediction_history = ensemble_data['prediction_history']
            self.model_accuracy = ensemble_data['model_accuracy']
            
            logger.info(f"Ensemble model loaded from {filepath}")
            
        except Exception as e:
            logger.error(f"Error loading ensemble model: {e}")
