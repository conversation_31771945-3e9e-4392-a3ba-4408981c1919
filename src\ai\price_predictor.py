"""
Advanced Price Prediction using Deep Learning Models
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import joblib
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Deep Learning
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, Model
    from tensorflow.keras.layers import LSTM, GRU, Dense, Dropout, BatchNormalization, Input, Attention
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    from tensorflow.keras.regularizers import l2
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False

# Traditional ML
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.linear_model import LinearRegression, Ridge, Lasso
    from sklearn.svm import SVR
    from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
    from sklearn.model_selection import train_test_split
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

from .feature_engineer import FeatureEngineer
from ..core.logger import logger


class PricePredictor:
    """
    Advanced price prediction system using multiple ML/DL models
    """
    
    def __init__(self, use_deep_learning: bool = True):
        self.use_deep_learning = use_deep_learning and TENSORFLOW_AVAILABLE
        self.feature_engineer = FeatureEngineer()
        
        # Models
        self.models = {}
        self.scalers = {}
        self.feature_columns = []
        
        # Model configurations
        self.model_configs = {
            'lstm': {
                'sequence_length': 60,
                'units': [128, 64, 32],
                'dropout': 0.2,
                'epochs': 100,
                'batch_size': 32
            },
            'gru': {
                'sequence_length': 60,
                'units': [128, 64],
                'dropout': 0.2,
                'epochs': 100,
                'batch_size': 32
            },
            'transformer': {
                'sequence_length': 60,
                'embed_dim': 64,
                'num_heads': 8,
                'ff_dim': 128,
                'epochs': 100,
                'batch_size': 32
            }
        }
        
        # Performance tracking
        self.model_performance = {}
        
        if not self.use_deep_learning:
            logger.warning("TensorFlow not available. Using traditional ML models only.")
        
        logger.info(f"Price Predictor initialized - Deep Learning: {self.use_deep_learning}")
    
    def prepare_data(self, df: pd.DataFrame, target_col: str = 'close') -> Tuple[pd.DataFrame, List[str]]:
        """Prepare data with comprehensive feature engineering"""
        try:
            logger.info("Preparing data with feature engineering...")
            
            # Engineer all features
            features_df = self.feature_engineer.engineer_all_features(df)
            
            # Select important features
            feature_cols = self.feature_engineer.select_features(
                features_df, target_col, method='correlation', top_k=50
            )
            
            # Store feature columns
            self.feature_columns = feature_cols
            
            logger.info(f"Data preparation complete: {len(feature_cols)} features selected")
            return features_df, feature_cols
            
        except Exception as e:
            logger.error(f"Error preparing data: {e}")
            return df, []
    
    def create_lstm_model(self, input_shape: Tuple[int, int]):
        """Create LSTM model for price prediction"""
        try:
            config = self.model_configs['lstm']
            
            model = Sequential([
                LSTM(config['units'][0], return_sequences=True, input_shape=input_shape,
                     kernel_regularizer=l2(0.001)),
                BatchNormalization(),
                Dropout(config['dropout']),
                
                LSTM(config['units'][1], return_sequences=True,
                     kernel_regularizer=l2(0.001)),
                BatchNormalization(),
                Dropout(config['dropout']),
                
                LSTM(config['units'][2], return_sequences=False,
                     kernel_regularizer=l2(0.001)),
                BatchNormalization(),
                Dropout(config['dropout']),
                
                Dense(32, activation='relu', kernel_regularizer=l2(0.001)),
                BatchNormalization(),
                Dropout(config['dropout']),
                
                Dense(1, activation='linear')
            ])
            
            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='mse',
                metrics=['mae']
            )
            
            logger.info(f"LSTM model created with input shape {input_shape}")
            return model
            
        except Exception as e:
            logger.error(f"Error creating LSTM model: {e}")
            return None
    
    def create_gru_model(self, input_shape: Tuple[int, int]):
        """Create GRU model for price prediction"""
        try:
            config = self.model_configs['gru']
            
            model = Sequential([
                GRU(config['units'][0], return_sequences=True, input_shape=input_shape,
                    kernel_regularizer=l2(0.001)),
                BatchNormalization(),
                Dropout(config['dropout']),
                
                GRU(config['units'][1], return_sequences=False,
                    kernel_regularizer=l2(0.001)),
                BatchNormalization(),
                Dropout(config['dropout']),
                
                Dense(32, activation='relu', kernel_regularizer=l2(0.001)),
                BatchNormalization(),
                Dropout(config['dropout']),
                
                Dense(1, activation='linear')
            ])
            
            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='mse',
                metrics=['mae']
            )
            
            logger.info(f"GRU model created with input shape {input_shape}")
            return model
            
        except Exception as e:
            logger.error(f"Error creating GRU model: {e}")
            return None
    
    def create_transformer_model(self, input_shape: Tuple[int, int]):
        """Create Transformer model for price prediction"""
        try:
            config = self.model_configs['transformer']
            
            # Input layer
            inputs = Input(shape=input_shape)
            
            # Multi-head attention
            attention_output = tf.keras.layers.MultiHeadAttention(
                num_heads=config['num_heads'],
                key_dim=config['embed_dim']
            )(inputs, inputs)
            
            # Add & Norm
            attention_output = tf.keras.layers.LayerNormalization()(inputs + attention_output)
            
            # Feed Forward
            ff_output = Dense(config['ff_dim'], activation='relu')(attention_output)
            ff_output = Dense(input_shape[-1])(ff_output)
            
            # Add & Norm
            ff_output = tf.keras.layers.LayerNormalization()(attention_output + ff_output)
            
            # Global pooling and output
            pooled = tf.keras.layers.GlobalAveragePooling1D()(ff_output)
            outputs = Dense(1, activation='linear')(pooled)
            
            model = Model(inputs=inputs, outputs=outputs)
            
            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='mse',
                metrics=['mae']
            )
            
            logger.info(f"Transformer model created with input shape {input_shape}")
            return model
            
        except Exception as e:
            logger.error(f"Error creating Transformer model: {e}")
            return None
    
    def train_deep_learning_models(self, X_train: np.ndarray, y_train: np.ndarray, 
                                  X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Any]:
        """Train deep learning models"""
        try:
            if not self.use_deep_learning:
                logger.warning("Deep learning not available")
                return {}
            
            results = {}
            input_shape = (X_train.shape[1], X_train.shape[2])
            
            # Callbacks
            early_stopping = EarlyStopping(patience=20, restore_best_weights=True)
            reduce_lr = ReduceLROnPlateau(factor=0.5, patience=10, min_lr=1e-6)
            
            # Train LSTM
            logger.info("Training LSTM model...")
            lstm_model = self.create_lstm_model(input_shape)
            if lstm_model:
                history = lstm_model.fit(
                    X_train, y_train,
                    validation_data=(X_val, y_val),
                    epochs=self.model_configs['lstm']['epochs'],
                    batch_size=self.model_configs['lstm']['batch_size'],
                    callbacks=[early_stopping, reduce_lr],
                    verbose=0
                )
                
                self.models['lstm'] = lstm_model
                results['lstm'] = {
                    'model': lstm_model,
                    'history': history.history,
                    'val_loss': min(history.history['val_loss'])
                }
                logger.info(f"LSTM training complete - Val Loss: {results['lstm']['val_loss']:.6f}")
            
            # Train GRU
            logger.info("Training GRU model...")
            gru_model = self.create_gru_model(input_shape)
            if gru_model:
                history = gru_model.fit(
                    X_train, y_train,
                    validation_data=(X_val, y_val),
                    epochs=self.model_configs['gru']['epochs'],
                    batch_size=self.model_configs['gru']['batch_size'],
                    callbacks=[early_stopping, reduce_lr],
                    verbose=0
                )
                
                self.models['gru'] = gru_model
                results['gru'] = {
                    'model': gru_model,
                    'history': history.history,
                    'val_loss': min(history.history['val_loss'])
                }
                logger.info(f"GRU training complete - Val Loss: {results['gru']['val_loss']:.6f}")
            
            # Train Transformer
            logger.info("Training Transformer model...")
            transformer_model = self.create_transformer_model(input_shape)
            if transformer_model:
                history = transformer_model.fit(
                    X_train, y_train,
                    validation_data=(X_val, y_val),
                    epochs=self.model_configs['transformer']['epochs'],
                    batch_size=self.model_configs['transformer']['batch_size'],
                    callbacks=[early_stopping, reduce_lr],
                    verbose=0
                )
                
                self.models['transformer'] = transformer_model
                results['transformer'] = {
                    'model': transformer_model,
                    'history': history.history,
                    'val_loss': min(history.history['val_loss'])
                }
                logger.info(f"Transformer training complete - Val Loss: {results['transformer']['val_loss']:.6f}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error training deep learning models: {e}")
            return {}
    
    def train_traditional_models(self, X_train: np.ndarray, y_train: np.ndarray) -> Dict[str, Any]:
        """Train traditional ML models"""
        try:
            if not SKLEARN_AVAILABLE:
                logger.warning("Scikit-learn not available, skipping traditional models")
                return {}

            results = {}

            # Flatten sequences for traditional models
            if len(X_train.shape) == 3:
                X_train_flat = X_train.reshape(X_train.shape[0], -1)
            else:
                X_train_flat = X_train

            # Random Forest
            logger.info("Training Random Forest...")
            rf_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
            rf_model.fit(X_train_flat, y_train)
            self.models['random_forest'] = rf_model
            results['random_forest'] = {'model': rf_model}

            # Gradient Boosting
            logger.info("Training Gradient Boosting...")
            gb_model = GradientBoostingRegressor(n_estimators=100, random_state=42)
            gb_model.fit(X_train_flat, y_train)
            self.models['gradient_boosting'] = gb_model
            results['gradient_boosting'] = {'model': gb_model}

            # Ridge Regression
            logger.info("Training Ridge Regression...")
            ridge_model = Ridge(alpha=1.0)
            ridge_model.fit(X_train_flat, y_train)
            self.models['ridge'] = ridge_model
            results['ridge'] = {'model': ridge_model}

            logger.info("Traditional models training complete")
            return results

        except Exception as e:
            logger.error(f"Error training traditional models: {e}")
            return {}
    
    def evaluate_models(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Dict[str, float]]:
        """Evaluate all trained models"""
        try:
            results = {}
            
            for model_name, model in self.models.items():
                try:
                    # Prepare test data
                    if model_name in ['random_forest', 'gradient_boosting', 'ridge']:
                        # Flatten for traditional models
                        X_test_input = X_test.reshape(X_test.shape[0], -1) if len(X_test.shape) == 3 else X_test
                    else:
                        X_test_input = X_test
                    
                    # Make predictions
                    y_pred = model.predict(X_test_input)
                    if len(y_pred.shape) > 1:
                        y_pred = y_pred.flatten()
                    
                    # Calculate metrics
                    if SKLEARN_AVAILABLE:
                        mse = mean_squared_error(y_test, y_pred)
                        mae = mean_absolute_error(y_test, y_pred)
                        rmse = np.sqrt(mse)
                        r2 = r2_score(y_test, y_pred)
                    else:
                        # Manual metric calculation
                        mse = np.mean((y_test - y_pred) ** 2)
                        mae = np.mean(np.abs(y_test - y_pred))
                        rmse = np.sqrt(mse)
                        ss_res = np.sum((y_test - y_pred) ** 2)
                        ss_tot = np.sum((y_test - np.mean(y_test)) ** 2)
                        r2 = 1 - (ss_res / (ss_tot + 1e-8))
                    
                    # Calculate percentage error
                    mape = np.mean(np.abs((y_test - y_pred) / y_test)) * 100
                    
                    results[model_name] = {
                        'mse': mse,
                        'mae': mae,
                        'rmse': rmse,
                        'r2': r2,
                        'mape': mape
                    }
                    
                    logger.info(f"{model_name} - RMSE: {rmse:.6f}, MAE: {mae:.6f}, R²: {r2:.4f}")
                    
                except Exception as e:
                    logger.error(f"Error evaluating {model_name}: {e}")
            
            # Store performance
            self.model_performance = results
            return results
            
        except Exception as e:
            logger.error(f"Error evaluating models: {e}")
            return {}
    
    def predict(self, X: np.ndarray, model_name: str = 'best') -> np.ndarray:
        """Make predictions using specified model"""
        try:
            if model_name == 'best':
                # Use best performing model
                if self.model_performance:
                    best_model = min(self.model_performance.items(), key=lambda x: x[1]['rmse'])[0]
                else:
                    best_model = list(self.models.keys())[0] if self.models else None
                
                if not best_model:
                    raise ValueError("No trained models available")
                
                model_name = best_model
                logger.info(f"Using best model: {model_name}")
            
            if model_name not in self.models:
                raise ValueError(f"Model {model_name} not found")
            
            model = self.models[model_name]
            
            # Prepare input data
            if model_name in ['random_forest', 'gradient_boosting', 'ridge']:
                X_input = X.reshape(X.shape[0], -1) if len(X.shape) == 3 else X
            else:
                X_input = X
            
            # Make prediction
            predictions = model.predict(X_input)
            if len(predictions.shape) > 1:
                predictions = predictions.flatten()
            
            return predictions
            
        except Exception as e:
            logger.error(f"Error making predictions: {e}")
            return np.array([])
    
    def save_models(self, filepath: str):
        """Save all trained models"""
        try:
            model_data = {
                'models': {},
                'scalers': self.scalers,
                'feature_columns': self.feature_columns,
                'model_performance': self.model_performance,
                'model_configs': self.model_configs
            }
            
            # Save traditional models
            for name, model in self.models.items():
                if name in ['random_forest', 'gradient_boosting', 'ridge']:
                    model_data['models'][name] = model
            
            # Save traditional models and metadata
            joblib.dump(model_data, f"{filepath}_traditional.pkl")
            
            # Save deep learning models separately
            if self.use_deep_learning:
                for name, model in self.models.items():
                    if name in ['lstm', 'gru', 'transformer']:
                        model.save(f"{filepath}_{name}.h5")
            
            logger.info(f"Models saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving models: {e}")
    
    def load_models(self, filepath: str):
        """Load trained models"""
        try:
            # Load traditional models and metadata
            model_data = joblib.load(f"{filepath}_traditional.pkl")
            
            self.scalers = model_data['scalers']
            self.feature_columns = model_data['feature_columns']
            self.model_performance = model_data['model_performance']
            self.model_configs = model_data['model_configs']
            
            # Load traditional models
            for name, model in model_data['models'].items():
                self.models[name] = model
            
            # Load deep learning models
            if self.use_deep_learning:
                for name in ['lstm', 'gru', 'transformer']:
                    try:
                        model = tf.keras.models.load_model(f"{filepath}_{name}.h5")
                        self.models[name] = model
                    except:
                        logger.warning(f"Could not load {name} model")
            
            logger.info(f"Models loaded from {filepath}")
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
