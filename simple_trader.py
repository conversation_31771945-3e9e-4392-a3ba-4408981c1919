#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة التداول البسيطة والسريعة
Simple & Fast Trading Tool

أداة تداول حقيقية مع KuCoin:
- تنفيذ فوري للصفقات
- مضاربة سريعة
- إدارة مخاطر بسيطة
- بدون تعقيدات
"""

import requests
import json
import time
import hashlib
import hmac
import base64
from datetime import datetime

# KuCoin API Configuration
API_KEY = "686a4e782301b10001e7457c"
SECRET_KEY = "61718954-dc69-4b89-b21c-dff5b80fff15"
PASSPHRASE = "Eslam*17*3*1999"
BASE_URL = "https://api.kucoin.com"

class SimpleTrader:
    """متداول بسيط وسريع"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 5  # 5 seconds only
        print("🚀 Simple Trader initialized")
    
    def _sign_request(self, timestamp, method, endpoint, body=""):
        """توقيع الطلب"""
        message = timestamp + method.upper() + endpoint + body
        signature = base64.b64encode(
            hmac.new(SECRET_KEY.encode(), message.encode(), hashlib.sha256).digest()
        ).decode()
        
        passphrase = base64.b64encode(
            hmac.new(SECRET_KEY.encode(), PASSPHRASE.encode(), hashlib.sha256).digest()
        ).decode()
        
        return {
            "KC-API-KEY": API_KEY,
            "KC-API-SIGN": signature,
            "KC-API-TIMESTAMP": timestamp,
            "KC-API-PASSPHRASE": passphrase,
            "KC-API-KEY-VERSION": "2",
            "Content-Type": "application/json"
        }
    
    def _api_call(self, method, endpoint, data=None, signed=False):
        """استدعاء API بسيط"""
        url = BASE_URL + endpoint
        headers = {}
        
        if signed:
            timestamp = str(int(time.time() * 1000))
            body = json.dumps(data) if data else ""
            headers = self._sign_request(timestamp, method, endpoint, body)
            data = body if body else None
        
        try:
            response = self.session.request(method, url, data=data, headers=headers)
            result = response.json()
            
            if response.status_code == 200 and result.get("code") == "200000":
                return result.get("data")
            else:
                print(f"❌ API Error: {result.get('msg', 'Unknown')}")
                return None
        except Exception as e:
            print(f"❌ Request failed: {e}")
            return None
    
    def get_price(self, symbol):
        """الحصول على السعر الحالي"""
        ticker = self._api_call("GET", f"/api/v1/market/stats?symbol={symbol}")
        if ticker:
            return float(ticker["last"])
        return None
    
    def get_balance(self, currency="USDT"):
        """الحصول على الرصيد"""
        accounts = self._api_call("GET", "/api/v1/accounts", signed=True)
        if accounts:
            for account in accounts:
                if account["currency"] == currency and account["type"] == "trade":
                    return float(account["available"])
        return 0.0
    
    def buy_market(self, symbol, usdt_amount):
        """شراء بسعر السوق"""
        print(f"🟢 Buying {symbol} with ${usdt_amount}")
        
        data = {
            "clientOid": f"buy_{int(time.time())}",
            "symbol": symbol,
            "side": "buy",
            "type": "market",
            "funds": str(usdt_amount)
        }
        
        result = self._api_call("POST", "/api/v1/orders", data=data, signed=True)
        if result:
            print(f"✅ Buy order placed! Order ID: {result.get('orderId')}")
            return result.get('orderId')
        else:
            print("❌ Buy order failed!")
            return None
    
    def sell_market(self, symbol, crypto_amount):
        """بيع بسعر السوق"""
        print(f"🔴 Selling {crypto_amount} {symbol.split('-')[0]}")
        
        data = {
            "clientOid": f"sell_{int(time.time())}",
            "symbol": symbol,
            "side": "sell",
            "type": "market",
            "size": str(crypto_amount)
        }
        
        result = self._api_call("POST", "/api/v1/orders", data=data, signed=True)
        if result:
            print(f"✅ Sell order placed! Order ID: {result.get('orderId')}")
            return result.get('orderId')
        else:
            print("❌ Sell order failed!")
            return None
    
    def quick_scalp(self, symbol, amount=10, profit_target=0.005):
        """مضاربة سريعة"""
        print(f"⚡ Quick scalping {symbol} with ${amount}")
        
        # Get current price
        current_price = self.get_price(symbol)
        if not current_price:
            print("❌ Cannot get current price")
            return False
        
        print(f"💰 Current price: ${current_price}")
        
        # Check balance
        usdt_balance = self.get_balance("USDT")
        if usdt_balance < amount:
            print(f"❌ Insufficient USDT balance: ${usdt_balance}")
            return False
        
        # Buy
        buy_order = self.buy_market(symbol, amount)
        if not buy_order:
            return False
        
        # Wait a bit for order to fill
        time.sleep(2)
        
        # Calculate crypto amount bought (approximate)
        crypto_amount = amount / current_price
        
        # Wait for price movement or timeout
        target_price = current_price * (1 + profit_target)
        stop_loss_price = current_price * 0.995  # 0.5% stop loss
        
        print(f"🎯 Target: ${target_price:.2f} | Stop Loss: ${stop_loss_price:.2f}")
        
        start_time = time.time()
        timeout = 300  # 5 minutes timeout
        
        while time.time() - start_time < timeout:
            new_price = self.get_price(symbol)
            if not new_price:
                continue
            
            print(f"📊 Price: ${new_price:.2f}", end="\r")
            
            # Check if target reached
            if new_price >= target_price:
                print(f"\n🎉 Target reached! Selling at ${new_price:.2f}")
                sell_order = self.sell_market(symbol, crypto_amount * 0.99)  # 99% to account for fees
                if sell_order:
                    profit = (new_price - current_price) * crypto_amount
                    print(f"💰 Estimated profit: ${profit:.2f}")
                    return True
                break
            
            # Check stop loss
            elif new_price <= stop_loss_price:
                print(f"\n🛑 Stop loss triggered! Selling at ${new_price:.2f}")
                sell_order = self.sell_market(symbol, crypto_amount * 0.99)
                if sell_order:
                    loss = (current_price - new_price) * crypto_amount
                    print(f"💸 Estimated loss: ${loss:.2f}")
                    return False
                break
            
            time.sleep(1)  # Check every second
        
        # Timeout - sell at market
        print(f"\n⏰ Timeout reached. Selling at market price...")
        final_price = self.get_price(symbol)
        sell_order = self.sell_market(symbol, crypto_amount * 0.99)
        if sell_order and final_price:
            result = (final_price - current_price) * crypto_amount
            if result > 0:
                print(f"💰 Small profit: ${result:.2f}")
            else:
                print(f"💸 Small loss: ${abs(result):.2f}")
        
        return False
    
    def monitor_and_scalp(self, symbols=["BTC-USDT", "ETH-USDT"], amount=10):
        """مراقبة ومضاربة مستمرة"""
        print(f"👁️ Monitoring {symbols} for scalping opportunities...")
        
        while True:
            try:
                for symbol in symbols:
                    # Get price change
                    ticker = self._api_call("GET", f"/api/v1/market/stats?symbol={symbol}")
                    if ticker:
                        change_rate = float(ticker["changeRate"]) * 100
                        volume = float(ticker["vol"])
                        price = float(ticker["last"])
                        
                        print(f"📊 {symbol}: ${price:.2f} ({change_rate:+.2f}%) Vol: {volume:.0f}")
                        
                        # Scalping conditions
                        if abs(change_rate) > 1.0 and volume > 100:  # 1% move + decent volume
                            print(f"🎯 Scalping opportunity detected for {symbol}!")
                            success = self.quick_scalp(symbol, amount)
                            if success:
                                print(f"✅ Successful scalp on {symbol}!")
                            else:
                                print(f"❌ Scalp failed on {symbol}")
                            
                            # Wait before next scalp
                            time.sleep(60)
                
                time.sleep(10)  # Check every 10 seconds
                
            except KeyboardInterrupt:
                print("\n👋 Stopping monitor...")
                break
            except Exception as e:
                print(f"❌ Monitor error: {e}")
                time.sleep(30)

def main():
    """الدالة الرئيسية"""
    trader = SimpleTrader()
    
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        ⚡ أداة التداول البسيطة والسريعة                     ║
    ║           Simple & Fast Trading Tool                         ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    
    الأوامر المتاحة:
    1. price <SYMBOL>     - الحصول على السعر
    2. balance <CURRENCY> - الحصول على الرصيد  
    3. buy <SYMBOL> <AMOUNT> - شراء بسعر السوق
    4. sell <SYMBOL> <AMOUNT> - بيع بسعر السوق
    5. scalp <SYMBOL> <AMOUNT> - مضاربة سريعة
    6. monitor - مراقبة ومضاربة مستمرة
    7. quit - خروج
    
    أمثلة:
    price BTC-USDT
    balance USDT
    scalp BTC-USDT 20
    monitor
    """)
    
    while True:
        try:
            command = input("\n💻 Enter command: ").strip().split()
            
            if not command:
                continue
            
            cmd = command[0].lower()
            
            if cmd == "quit":
                print("👋 Goodbye!")
                break
            
            elif cmd == "price" and len(command) == 2:
                symbol = command[1].upper()
                price = trader.get_price(symbol)
                if price:
                    print(f"💰 {symbol}: ${price}")
            
            elif cmd == "balance" and len(command) == 2:
                currency = command[1].upper()
                balance = trader.get_balance(currency)
                print(f"💰 {currency} balance: {balance}")
            
            elif cmd == "buy" and len(command) == 3:
                symbol = command[1].upper()
                amount = float(command[2])
                trader.buy_market(symbol, amount)
            
            elif cmd == "sell" and len(command) == 3:
                symbol = command[1].upper()
                amount = float(command[2])
                trader.sell_market(symbol, amount)
            
            elif cmd == "scalp":
                symbol = command[1].upper() if len(command) > 1 else "BTC-USDT"
                amount = float(command[2]) if len(command) > 2 else 10
                trader.quick_scalp(symbol, amount)
            
            elif cmd == "monitor":
                trader.monitor_and_scalp()
            
            else:
                print("❌ Invalid command. Type 'quit' to exit.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
