#!/usr/bin/env python3
"""
Test script for data collection and storage system
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data import BinanceClient, DataCollector, db_manager, redis_client
from src.core.config import settings
from src.core.logger import logger


async def test_data_collection():
    """Test basic data collection functionality"""
    print("🔍 Testing Data Collection System...")
    print("=" * 50)
    
    try:
        # Initialize components
        binance_client = BinanceClient(testnet=True)
        collector = DataCollector(binance_client)
        
        async with binance_client:
            print("✅ Binance client connected")
            
            # Test ticker data collection
            print("\n📊 Testing ticker data collection...")
            tickers = await collector.collect_all_tickers()
            
            if tickers:
                print(f"✅ Collected ticker data for {len(tickers)} symbols")
                
                # Show sample data
                sample_symbols = list(tickers.keys())[:3]
                for symbol in sample_symbols:
                    ticker = tickers[symbol]
                    print(f"   {symbol}: ${ticker.last_price:.2f} ({ticker.price_change_percent:+.2f}%)")
            else:
                print("❌ No ticker data collected")
                return False
            
            # Test klines data collection
            print("\n📈 Testing klines data collection...")
            klines_data = await collector.collect_klines_batch('1h')
            
            if klines_data:
                print(f"✅ Collected klines data for {len(klines_data)} symbols")
                
                # Show sample data
                for symbol, df in list(klines_data.items())[:2]:
                    if not df.empty:
                        latest_close = df['close'].iloc[-1]
                        print(f"   {symbol}: Latest close ${latest_close:.2f} ({len(df)} candles)")
            else:
                print("❌ No klines data collected")
                return False
            
            # Test historical data collection (small sample)
            print("\n📚 Testing historical data collection...")
            historical_df = await collector.collect_historical_data("BTCUSDT", "1h", days=1)
            
            if not historical_df.empty:
                print(f"✅ Collected {len(historical_df)} historical records")
                print(f"   Date range: {historical_df.index.min()} to {historical_df.index.max()}")
                print(f"   Price range: ${historical_df['low'].min():.2f} - ${historical_df['high'].max():.2f}")
            else:
                print("❌ No historical data collected")
                return False
            
            print("\n✅ All data collection tests passed!")
            return True
            
    except Exception as e:
        print(f"❌ Data collection test failed: {e}")
        return False


async def test_redis_integration():
    """Test Redis caching functionality"""
    print("\n🔍 Testing Redis Integration...")
    print("=" * 50)
    
    try:
        async with redis_client:
            print("✅ Redis client connected")
            
            # Test basic cache operations
            test_key = "test_key"
            test_value = {"message": "Hello Redis!", "timestamp": "2024-01-01"}
            
            await redis_client.set_cache(test_key, test_value, ttl=60)
            retrieved_value = await redis_client.get_cache(test_key)
            
            if retrieved_value == test_value:
                print("✅ Basic cache operations working")
            else:
                print("❌ Cache operations failed")
                return False
            
            # Test market data caching
            print("\n📊 Testing market data caching...")
            
            # Create sample DataFrame
            import pandas as pd
            from datetime import datetime, timedelta
            
            sample_data = pd.DataFrame({
                'open': [50000, 50100, 50200],
                'high': [50200, 50300, 50400],
                'low': [49900, 50000, 50100],
                'close': [50100, 50200, 50300],
                'volume': [100, 150, 120],
                'quote_volume': [5010000, 7530000, 6036000],
                'trades_count': [1000, 1500, 1200]
            }, index=pd.date_range(start=datetime.now() - timedelta(hours=3), periods=3, freq='H'))
            
            await redis_client.cache_market_data("TESTUSDT", "1h", sample_data)
            cached_data = await redis_client.get_market_data("TESTUSDT", "1h")
            
            if cached_data is not None and len(cached_data) == 3:
                print("✅ Market data caching working")
            else:
                print("❌ Market data caching failed")
                return False
            
            # Test cache statistics
            stats = await redis_client.get_cache_stats()
            print(f"📈 Cache stats: {stats['used_memory']} used, {stats['hit_rate']:.2%} hit rate")
            
            # Cleanup
            await redis_client.delete_cache(test_key)
            await redis_client.clear_cache_pattern("md:TESTUSDT:*")
            
            print("✅ Redis integration tests passed!")
            return True
            
    except Exception as e:
        print(f"❌ Redis integration test failed: {e}")
        print("   Make sure Redis is running and accessible")
        return False


async def test_database_integration():
    """Test database storage functionality"""
    print("\n🔍 Testing Database Integration...")
    print("=" * 50)
    
    try:
        # Initialize database
        db_manager.initialize()
        print("✅ Database initialized")
        
        # Test market data storage
        print("\n📊 Testing market data storage...")
        
        import pandas as pd
        from datetime import datetime, timedelta
        
        # Create sample DataFrame
        sample_data = pd.DataFrame({
            'symbol': ['TESTUSDT'] * 3,
            'open': [50000, 50100, 50200],
            'high': [50200, 50300, 50400],
            'low': [49900, 50000, 50100],
            'close': [50100, 50200, 50300],
            'volume': [100, 150, 120],
            'quote_volume': [5010000, 7530000, 6036000],
            'trades_count': [1000, 1500, 1200]
        }, index=pd.date_range(start=datetime.now() - timedelta(hours=3), periods=3, freq='H'))
        
        await db_manager.store_market_data(sample_data, "TESTUSDT", "1h")
        print("✅ Market data stored in database")
        
        # Test data retrieval
        retrieved_data = await db_manager.get_latest_market_data("TESTUSDT", "1h", limit=5)
        
        if not retrieved_data.empty:
            print(f"✅ Retrieved {len(retrieved_data)} records from database")
        else:
            print("❌ No data retrieved from database")
            return False
        
        print("✅ Database integration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Database integration test failed: {e}")
        print("   Make sure PostgreSQL is running and accessible")
        print("   Check your DATABASE_URL in .env file")
        return False


async def test_integrated_system():
    """Test the complete integrated data system"""
    print("\n🔍 Testing Integrated Data System...")
    print("=" * 50)
    
    try:
        # Initialize all components
        binance_client = BinanceClient(testnet=True)
        collector = DataCollector(binance_client)
        
        # Enable all storage options
        collector.use_database = True
        collector.use_redis = True
        
        async with binance_client:
            print("✅ All components initialized")
            
            # Collect and store data
            print("\n📊 Collecting and storing ticker data...")
            tickers = await collector.collect_all_tickers()
            
            if tickers:
                print(f"✅ Collected and stored ticker data for {len(tickers)} symbols")
            
            # Test cached data retrieval
            print("\n🔍 Testing cached data retrieval...")
            cached_price = await collector.get_latest_price("BTCUSDT")
            
            if cached_price:
                print(f"✅ Retrieved cached BTC price: ${cached_price:.2f}")
            
            # Test market summary
            print("\n📈 Testing market summary...")
            summary = await collector.get_market_summary()
            
            print(f"✅ Market summary generated:")
            print(f"   Active symbols: {summary['active_symbols']}")
            print(f"   Market trend: {summary['market_trend']}")
            print(f"   Total 24h volume: ${summary['total_volume_24h']:,.0f}")
            
            if summary['top_gainers']:
                top_gainer = summary['top_gainers'][0]
                print(f"   Top gainer: {top_gainer[0]} (+{top_gainer[1]:.2f}%)")
            
            print("\n✅ Integrated system tests passed!")
            return True
            
    except Exception as e:
        print(f"❌ Integrated system test failed: {e}")
        return False


async def main():
    """Main test function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🤖 AI Crypto Trading Bot - Data System Test          ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    print(f"🔧 Configuration:")
    print(f"   Database URL: {settings.database_url[:50]}...")
    print(f"   Redis URL: {settings.redis_url}")
    print(f"   Trading pairs: {len(settings.trading_pairs_list)} configured")
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Data collection
    if await test_data_collection():
        tests_passed += 1
    
    # Test 2: Redis integration
    if await test_redis_integration():
        tests_passed += 1
    
    # Test 3: Database integration
    if await test_database_integration():
        tests_passed += 1
    
    # Test 4: Integrated system
    if await test_integrated_system():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All data system tests passed!")
        print("\n💡 Your data collection and storage system is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("\n🔧 Troubleshooting:")
        print("   1. Ensure Redis is running: redis-server")
        print("   2. Ensure PostgreSQL is running and accessible")
        print("   3. Check your .env file configuration")
        print("   4. Install required dependencies: pip install -r requirements.txt")
    
    return tests_passed == total_tests


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
