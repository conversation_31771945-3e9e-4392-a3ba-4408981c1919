"""
Setup script for AI Crypto Trading Bot
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text(encoding="utf-8").strip().split("\n")
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith("#")]

setup(
    name="ai-crypto-trading-bot",
    version="1.0.0",
    author="AI Trading Bot Team",
    author_email="<EMAIL>",
    description="AI-powered cryptocurrency trading bot with advanced risk management",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/ai-crypto-trading-bot",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
            "pre-commit>=3.0.0",
        ],
        "docs": [
            "sphinx>=5.0.0",
            "sphinx-rtd-theme>=1.0.0",
            "myst-parser>=0.18.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "ai-trading-bot=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yml", "*.yaml", "*.json", "*.txt"],
    },
    keywords=[
        "cryptocurrency",
        "trading",
        "artificial intelligence",
        "machine learning",
        "binance",
        "algorithmic trading",
        "risk management",
        "fintech",
    ],
    project_urls={
        "Bug Reports": "https://github.com/your-username/ai-crypto-trading-bot/issues",
        "Source": "https://github.com/your-username/ai-crypto-trading-bot",
        "Documentation": "https://ai-crypto-trading-bot.readthedocs.io/",
    },
)
