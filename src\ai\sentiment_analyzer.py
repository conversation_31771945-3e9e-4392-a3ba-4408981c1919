"""
Advanced Sentiment Analysis for Cryptocurrency Markets
"""

import asyncio
import aiohttp
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import re
import json
from dataclasses import dataclass

# NLP Libraries
try:
    from textblob import TextBlob
    import nltk
    from nltk.sentiment import SentimentIntensityAnalyzer
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

from ..core.logger import logger
from ..core.config import settings


@dataclass
class SentimentScore:
    """Sentiment analysis result"""
    positive: float
    negative: float
    neutral: float
    compound: float
    confidence: float
    source: str
    timestamp: datetime


class SentimentAnalyzer:
    """
    Advanced sentiment analysis for cryptocurrency markets
    """
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.sentiment_models = {}
        self.sentiment_history = []
        
        # Initialize NLP models
        self._initialize_models()
        
        # Data sources
        self.news_sources = [
            'https://api.coindesk.com/v1/news',
            'https://newsapi.org/v2/everything',
            'https://api.cryptopanic.com/v1/posts/'
        ]
        
        # Social media keywords
        self.crypto_keywords = [
            'bitcoin', 'btc', 'ethereum', 'eth', 'crypto', 'cryptocurrency',
            'blockchain', 'defi', 'nft', 'altcoin', 'trading', 'hodl'
        ]
        
        logger.info(f"Sentiment Analyzer initialized - NLTK: {NLTK_AVAILABLE}, Transformers: {TRANSFORMERS_AVAILABLE}")
    
    def _initialize_models(self):
        """Initialize sentiment analysis models"""
        try:
            # NLTK VADER sentiment analyzer
            if NLTK_AVAILABLE:
                try:
                    nltk.download('vader_lexicon', quiet=True)
                    self.sentiment_models['vader'] = SentimentIntensityAnalyzer()
                    logger.info("VADER sentiment analyzer initialized")
                except Exception as e:
                    logger.warning(f"Could not initialize VADER: {e}")
            
            # Transformers-based sentiment analysis
            if TRANSFORMERS_AVAILABLE:
                try:
                    # Use a pre-trained model for financial sentiment
                    self.sentiment_models['finbert'] = pipeline(
                        "sentiment-analysis",
                        model="ProsusAI/finbert",
                        tokenizer="ProsusAI/finbert"
                    )
                    logger.info("FinBERT sentiment analyzer initialized")
                except Exception as e:
                    logger.warning(f"Could not initialize FinBERT: {e}")
                    
                try:
                    # General sentiment analysis model
                    self.sentiment_models['roberta'] = pipeline(
                        "sentiment-analysis",
                        model="cardiffnlp/twitter-roberta-base-sentiment-latest"
                    )
                    logger.info("RoBERTa sentiment analyzer initialized")
                except Exception as e:
                    logger.warning(f"Could not initialize RoBERTa: {e}")
            
        except Exception as e:
            logger.error(f"Error initializing sentiment models: {e}")
    
    async def __aenter__(self):
        """Async context manager entry"""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
            self.session = None
    
    def analyze_text_sentiment(self, text: str) -> Dict[str, SentimentScore]:
        """Analyze sentiment of a single text using multiple models"""
        try:
            results = {}
            
            # Clean text
            cleaned_text = self._clean_text(text)
            
            # TextBlob analysis
            try:
                blob = TextBlob(cleaned_text)
                polarity = blob.sentiment.polarity
                subjectivity = blob.sentiment.subjectivity
                
                # Convert to positive/negative/neutral
                if polarity > 0.1:
                    positive, negative, neutral = polarity, 0, 1 - polarity
                elif polarity < -0.1:
                    positive, negative, neutral = 0, abs(polarity), 1 - abs(polarity)
                else:
                    positive, negative, neutral = 0, 0, 1
                
                results['textblob'] = SentimentScore(
                    positive=positive,
                    negative=negative,
                    neutral=neutral,
                    compound=polarity,
                    confidence=1 - subjectivity,
                    source='textblob',
                    timestamp=datetime.now()
                )
            except Exception as e:
                logger.warning(f"TextBlob analysis failed: {e}")
            
            # VADER analysis
            if 'vader' in self.sentiment_models:
                try:
                    scores = self.sentiment_models['vader'].polarity_scores(cleaned_text)
                    results['vader'] = SentimentScore(
                        positive=scores['pos'],
                        negative=scores['neg'],
                        neutral=scores['neu'],
                        compound=scores['compound'],
                        confidence=abs(scores['compound']),
                        source='vader',
                        timestamp=datetime.now()
                    )
                except Exception as e:
                    logger.warning(f"VADER analysis failed: {e}")
            
            # FinBERT analysis
            if 'finbert' in self.sentiment_models:
                try:
                    result = self.sentiment_models['finbert'](cleaned_text)[0]
                    label = result['label'].lower()
                    score = result['score']
                    
                    if label == 'positive':
                        positive, negative, neutral = score, 0, 1 - score
                        compound = score
                    elif label == 'negative':
                        positive, negative, neutral = 0, score, 1 - score
                        compound = -score
                    else:
                        positive, negative, neutral = 0, 0, score
                        compound = 0
                    
                    results['finbert'] = SentimentScore(
                        positive=positive,
                        negative=negative,
                        neutral=neutral,
                        compound=compound,
                        confidence=score,
                        source='finbert',
                        timestamp=datetime.now()
                    )
                except Exception as e:
                    logger.warning(f"FinBERT analysis failed: {e}")
            
            # RoBERTa analysis
            if 'roberta' in self.sentiment_models:
                try:
                    result = self.sentiment_models['roberta'](cleaned_text)[0]
                    label = result['label'].lower()
                    score = result['score']
                    
                    if 'positive' in label:
                        positive, negative, neutral = score, 0, 1 - score
                        compound = score
                    elif 'negative' in label:
                        positive, negative, neutral = 0, score, 1 - score
                        compound = -score
                    else:
                        positive, negative, neutral = 0, 0, score
                        compound = 0
                    
                    results['roberta'] = SentimentScore(
                        positive=positive,
                        negative=negative,
                        neutral=neutral,
                        compound=compound,
                        confidence=score,
                        source='roberta',
                        timestamp=datetime.now()
                    )
                except Exception as e:
                    logger.warning(f"RoBERTa analysis failed: {e}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error analyzing text sentiment: {e}")
            return {}
    
    def _clean_text(self, text: str) -> str:
        """Clean and preprocess text for sentiment analysis"""
        try:
            # Remove URLs
            text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
            
            # Remove user mentions and hashtags
            text = re.sub(r'@\w+|#\w+', '', text)
            
            # Remove extra whitespace
            text = re.sub(r'\s+', ' ', text).strip()
            
            # Remove special characters but keep basic punctuation
            text = re.sub(r'[^\w\s.,!?-]', '', text)
            
            return text
            
        except Exception as e:
            logger.error(f"Error cleaning text: {e}")
            return text
    
    async def fetch_crypto_news(self, symbol: str = 'bitcoin', limit: int = 50) -> List[Dict]:
        """Fetch cryptocurrency news from various sources"""
        try:
            news_articles = []
            
            if not self.session:
                await self.__aenter__()
            
            # CryptoPanic API (free tier)
            try:
                url = f"https://cryptopanic.com/api/v1/posts/?auth_token=free&public=true&currencies={symbol}&filter=hot"
                async with self.session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        for post in data.get('results', [])[:limit//2]:
                            news_articles.append({
                                'title': post.get('title', ''),
                                'content': post.get('title', ''),  # CryptoPanic doesn't provide full content
                                'source': 'cryptopanic',
                                'url': post.get('url', ''),
                                'published_at': post.get('published_at', ''),
                                'votes': post.get('votes', {})
                            })
            except Exception as e:
                logger.warning(f"Error fetching from CryptoPanic: {e}")
            
            # Alternative: Use a simple news aggregator approach
            # This would require API keys for full functionality
            
            logger.info(f"Fetched {len(news_articles)} news articles")
            return news_articles
            
        except Exception as e:
            logger.error(f"Error fetching crypto news: {e}")
            return []
    
    async def analyze_market_sentiment(self, symbol: str = 'bitcoin') -> Dict[str, float]:
        """Analyze overall market sentiment for a cryptocurrency"""
        try:
            logger.info(f"Analyzing market sentiment for {symbol}")
            
            # Fetch news articles
            news_articles = await self.fetch_crypto_news(symbol)
            
            if not news_articles:
                logger.warning("No news articles found for sentiment analysis")
                return self._get_neutral_sentiment()
            
            # Analyze sentiment of each article
            all_sentiments = []
            
            for article in news_articles:
                text = f"{article.get('title', '')} {article.get('content', '')}"
                if text.strip():
                    sentiments = self.analyze_text_sentiment(text)
                    all_sentiments.extend(sentiments.values())
            
            if not all_sentiments:
                return self._get_neutral_sentiment()
            
            # Aggregate sentiments
            aggregated = self._aggregate_sentiments(all_sentiments)
            
            # Store in history
            self.sentiment_history.append({
                'symbol': symbol,
                'timestamp': datetime.now(),
                'sentiment': aggregated,
                'article_count': len(news_articles)
            })
            
            # Keep only recent history (last 100 entries)
            self.sentiment_history = self.sentiment_history[-100:]
            
            logger.info(f"Market sentiment analysis complete for {symbol}")
            return aggregated
            
        except Exception as e:
            logger.error(f"Error analyzing market sentiment: {e}")
            return self._get_neutral_sentiment()
    
    def _aggregate_sentiments(self, sentiments: List[SentimentScore]) -> Dict[str, float]:
        """Aggregate multiple sentiment scores"""
        try:
            if not sentiments:
                return self._get_neutral_sentiment()
            
            # Weight sentiments by confidence
            total_weight = sum(s.confidence for s in sentiments)
            
            if total_weight == 0:
                return self._get_neutral_sentiment()
            
            weighted_positive = sum(s.positive * s.confidence for s in sentiments) / total_weight
            weighted_negative = sum(s.negative * s.confidence for s in sentiments) / total_weight
            weighted_neutral = sum(s.neutral * s.confidence for s in sentiments) / total_weight
            weighted_compound = sum(s.compound * s.confidence for s in sentiments) / total_weight
            
            # Calculate overall sentiment score
            if weighted_compound > 0.05:
                overall_sentiment = 'bullish'
            elif weighted_compound < -0.05:
                overall_sentiment = 'bearish'
            else:
                overall_sentiment = 'neutral'
            
            return {
                'positive': weighted_positive,
                'negative': weighted_negative,
                'neutral': weighted_neutral,
                'compound': weighted_compound,
                'overall': overall_sentiment,
                'confidence': sum(s.confidence for s in sentiments) / len(sentiments),
                'sample_size': len(sentiments)
            }
            
        except Exception as e:
            logger.error(f"Error aggregating sentiments: {e}")
            return self._get_neutral_sentiment()
    
    def _get_neutral_sentiment(self) -> Dict[str, float]:
        """Return neutral sentiment scores"""
        return {
            'positive': 0.33,
            'negative': 0.33,
            'neutral': 0.34,
            'compound': 0.0,
            'overall': 'neutral',
            'confidence': 0.5,
            'sample_size': 0
        }
    
    def get_sentiment_trend(self, symbol: str, hours: int = 24) -> Dict[str, float]:
        """Get sentiment trend over specified time period"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Filter recent sentiment data
            recent_sentiments = [
                entry for entry in self.sentiment_history
                if entry['symbol'] == symbol and entry['timestamp'] > cutoff_time
            ]
            
            if not recent_sentiments:
                return {'trend': 'neutral', 'change': 0.0, 'data_points': 0}
            
            # Calculate trend
            compounds = [entry['sentiment']['compound'] for entry in recent_sentiments]
            
            if len(compounds) < 2:
                return {'trend': 'neutral', 'change': 0.0, 'data_points': len(compounds)}
            
            # Simple linear trend
            x = np.arange(len(compounds))
            slope = np.polyfit(x, compounds, 1)[0]
            
            # Determine trend direction
            if slope > 0.01:
                trend = 'improving'
            elif slope < -0.01:
                trend = 'declining'
            else:
                trend = 'stable'
            
            return {
                'trend': trend,
                'change': slope,
                'data_points': len(compounds),
                'latest_sentiment': compounds[-1],
                'average_sentiment': np.mean(compounds)
            }
            
        except Exception as e:
            logger.error(f"Error calculating sentiment trend: {e}")
            return {'trend': 'neutral', 'change': 0.0, 'data_points': 0}
    
    def get_sentiment_signal(self, symbol: str) -> Dict[str, any]:
        """Generate trading signal based on sentiment analysis"""
        try:
            # Get current sentiment
            current_sentiment = None
            for entry in reversed(self.sentiment_history):
                if entry['symbol'] == symbol:
                    current_sentiment = entry['sentiment']
                    break
            
            if not current_sentiment:
                return {'signal': 'HOLD', 'strength': 0.0, 'reasoning': 'No sentiment data available'}
            
            # Get sentiment trend
            trend = self.get_sentiment_trend(symbol)
            
            # Generate signal
            compound = current_sentiment['compound']
            confidence = current_sentiment['confidence']
            trend_change = trend['change']
            
            # Signal logic
            if compound > 0.1 and trend_change > 0 and confidence > 0.6:
                signal = 'BUY'
                strength = min(abs(compound) + abs(trend_change), 1.0)
                reasoning = f"Strong positive sentiment ({compound:.3f}) with improving trend"
            elif compound < -0.1 and trend_change < 0 and confidence > 0.6:
                signal = 'SELL'
                strength = min(abs(compound) + abs(trend_change), 1.0)
                reasoning = f"Strong negative sentiment ({compound:.3f}) with declining trend"
            else:
                signal = 'HOLD'
                strength = 0.0
                reasoning = f"Neutral sentiment ({compound:.3f}) or low confidence"
            
            return {
                'signal': signal,
                'strength': strength,
                'reasoning': reasoning,
                'sentiment_score': compound,
                'confidence': confidence,
                'trend': trend['trend']
            }
            
        except Exception as e:
            logger.error(f"Error generating sentiment signal: {e}")
            return {'signal': 'HOLD', 'strength': 0.0, 'reasoning': 'Error in sentiment analysis'}
