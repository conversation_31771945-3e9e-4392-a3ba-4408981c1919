import asyncio
from src.data.kucoin_client import KuCoinClient

async def main():
    """
    Fetches and prints the list of all available currencies on KuCoin.
    """
    async with KuCoinClient() as client:
        try:
            symbols = await client.get_symbols()
            if symbols:
                print("List of available currencies on KuCoin:")
                for symbol in symbols:
                    print(symbol['symbol'])
            else:
                print("Could not fetch the list of currencies.")
        except Exception as e:
            print(f"An error occurred: {e}")

if __name__ == "__main__":
    asyncio.run(main())