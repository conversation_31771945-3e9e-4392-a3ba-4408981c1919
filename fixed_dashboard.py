#!/usr/bin/env python3
"""
Fixed Advanced AI Crypto Trading Bot Dashboard
"""

import asyncio
import sys
from pathlib import Path
import json
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import uuid
import threading
import time

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Web framework
try:
    from flask import Flask, render_template, jsonify, request, send_file
    from flask_socketio import SocketIO, emit
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

from src.data.kucoin_client import KuCoinClient
from src.analysis import MarketAnalyzer
from src.ai.feature_engineer import FeatureEngineer
from src.trading.order_manager import OrderManager
from src.core.logger import logger


class FixedTradingDashboard:
    """
    Fixed Advanced AI Crypto Trading Bot Dashboard with real data integration
    """
    
    def __init__(self):
        if not FLASK_AVAILABLE:
            print("❌ Flask not available. Install with: pip install flask flask-socketio")
            sys.exit(1)
        
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'fixed_crypto_trading_bot_secret'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # Trading components
        self.kucoin_client = KuCoinClient()
        self.market_analyzer = MarketAnalyzer()
        self.feature_engineer = FeatureEngineer()
        self.order_manager = OrderManager(self.kucoin_client)
        
        # Portfolio symbols from your actual holdings
        self.portfolio_symbols = [
            'BTC-USDT', 'ETH-USDT', 'PIXEL-USDT', 'HIGH-USDT', 
            'STG-USDT', 'CELR-USDT', 'VOXEL-USDT', 'PORTAL-USDT', 'LTO-USDT'
        ]
        
        # Data storage
        self.market_data = {}
        self.analysis_results = {}
        self.portfolio_data = {}
        self.recommendations = {}
        self.trade_history = []
        self.performance_metrics = {}
        self.alerts = []
        
        # Background tasks
        self.update_thread = None
        self.running = False
        
        # Setup routes and socketio
        self._setup_routes()
        self._setup_socketio()
        
        logger.info("Fixed Trading Dashboard initialized")
    
    def _setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            return self._render_fixed_dashboard()
        
        @self.app.route('/api/portfolio')
        def get_portfolio():
            return jsonify(self.portfolio_data)
        
        @self.app.route('/api/market_data')
        def get_all_market_data():
            return jsonify(self.market_data)
        
        @self.app.route('/api/analysis')
        def get_all_analysis():
            return jsonify(self.analysis_results)
        
        @self.app.route('/api/recommendations')
        def get_recommendations():
            return jsonify(self.recommendations)
        
        @self.app.route('/api/trade_history')
        def get_trade_history():
            return jsonify(self.trade_history[-100:])
        
        @self.app.route('/api/alerts')
        def get_alerts():
            return jsonify(self.alerts[-50:])
    
    def _setup_socketio(self):
        """Setup SocketIO events"""
        
        @self.socketio.on('connect')
        def handle_connect():
            print(f"Client connected: {request.sid}")
            emit('status', {'message': 'Connected to Fixed Trading Bot'})
            # Send initial data
            emit('portfolio_update', self.portfolio_data)
            emit('market_update', self.market_data)
            emit('analysis_update', self.analysis_results)
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            print(f"Client disconnected: {request.sid}")
        
        @self.socketio.on('request_analysis')
        def handle_analysis_request(data):
            symbol = data.get('symbol', 'BTC-USDT')
            asyncio.create_task(self._analyze_symbol_real(symbol))
        
        @self.socketio.on('request_portfolio_update')
        def handle_portfolio_update():
            asyncio.create_task(self._update_portfolio_real())
        
        @self.socketio.on('request_market_analysis')
        def handle_market_analysis():
            asyncio.create_task(self._analyze_all_symbols())
        
        @self.socketio.on('request_recommendations')
        def handle_recommendations_request():
            asyncio.create_task(self._generate_real_recommendations())
        
        @self.socketio.on('request_prediction')
        def handle_prediction_request():
            asyncio.create_task(self._run_ai_prediction())
    
    async def _analyze_symbol_real(self, symbol):
        """Analyze symbol with real KuCoin data"""
        try:
            async with self.kucoin_client:
                # Get real market data
                klines = await self.kucoin_client.get_klines(symbol, '1hour')
                ticker = await self.kucoin_client.get_ticker(symbol)
                
                if not klines or len(klines) < 50:
                    self.socketio.emit('error', {'message': f'Insufficient data for {symbol}'})
                    return
                
                # Convert to DataFrame
                data = []
                for kline in reversed(klines[:100]):
                    data.append({
                        'open': float(kline[1]),
                        'high': float(kline[3]),
                        'low': float(kline[4]),
                        'close': float(kline[2]),
                        'volume': float(kline[5]),
                        'quote_volume': float(kline[6]),
                        'trades_count': 100
                    })
                
                df = pd.DataFrame(data)
                df.index = pd.date_range(end=datetime.now(), periods=len(df), freq='h')
                
                # Real technical analysis
                signal = await self.market_analyzer.analyze_symbol(symbol, df)
                
                # Feature engineering
                features_df = self.feature_engineer.create_technical_features(df)
                
                # Get real market data
                current_price = float(ticker.get('last', 0))
                price_change_24h = float(ticker.get('changeRate', 0)) * 100
                volume_24h = float(ticker.get('vol', 0))
                
                # Calculate real technical indicators
                rsi = features_df['rsi_14'].iloc[-1] if 'rsi_14' in features_df.columns else 50
                macd = features_df['macd'].iloc[-1] if 'macd' in features_df.columns else 0
                bb_position = features_df['bb_position'].iloc[-1] if 'bb_position' in features_df.columns else 0.5
                
                # Store real results
                self.analysis_results[symbol] = {
                    'signal': signal.signal_type,
                    'confidence': signal.confidence,
                    'risk_level': signal.risk_level,
                    'current_price': current_price,
                    'price_change_24h': price_change_24h,
                    'volume_24h': volume_24h,
                    'target_price': signal.target_price,
                    'stop_loss': signal.stop_loss,
                    'reasoning': signal.reasoning,
                    'technical_indicators': {
                        'rsi': float(rsi) if not pd.isna(rsi) else 50,
                        'macd': float(macd) if not pd.isna(macd) else 0,
                        'bb_position': float(bb_position) if not pd.isna(bb_position) else 0.5
                    },
                    'timestamp': datetime.now().isoformat()
                }
                
                # Update market data
                self.market_data[symbol] = {
                    'price': current_price,
                    'change_24h': price_change_24h,
                    'volume': volume_24h,
                    'timestamp': datetime.now().isoformat()
                }
                
                # Emit real data to clients
                self.socketio.emit('analysis_complete', {
                    'symbol': symbol,
                    'data': self.analysis_results[symbol]
                })
                
                self.socketio.emit('market_update', {
                    'symbol': symbol,
                    'data': self.market_data[symbol]
                })
                
                # Add to trade history
                self._add_real_trade_record(symbol, signal, current_price)
                
                logger.info(f"Real analysis completed for {symbol}: {signal.signal_type} ({signal.confidence:.1%})")
        
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}")
            self.socketio.emit('error', {'message': f'Analysis failed for {symbol}: {str(e)}'})
    
    async def _update_portfolio_real(self):
        """Update portfolio with real KuCoin account data"""
        try:
            async with self.kucoin_client:
                # Get real account info
                accounts = await self.kucoin_client.get_account_info()
                
                portfolio_summary = {
                    'total_value_usdt': 0.0,
                    'holdings': {},
                    'performance': {},
                    'allocation': {},
                    'last_update': datetime.now().isoformat()
                }
                
                # Process real trading accounts
                trading_accounts = [acc for acc in accounts if acc['type'] == 'trade' and float(acc['balance']) > 0]
                
                for account in trading_accounts:
                    currency = account['currency']
                    balance = float(account['balance'])
                    available = float(account['available'])
                    
                    portfolio_summary['holdings'][currency] = {
                        'balance': balance,
                        'available': available,
                        'currency': currency
                    }
                    
                    # Get current value in USDT
                    if currency == 'USDT':
                        value_usdt = balance
                        portfolio_summary['holdings'][currency]['price'] = 1.0
                        portfolio_summary['holdings'][currency]['value_usdt'] = value_usdt
                    else:
                        try:
                            ticker = await self.kucoin_client.get_ticker(f"{currency}-USDT")
                            price = float(ticker.get('last', 0))
                            value_usdt = balance * price
                            
                            portfolio_summary['holdings'][currency]['price'] = price
                            portfolio_summary['holdings'][currency]['value_usdt'] = value_usdt
                            portfolio_summary['holdings'][currency]['change_24h'] = float(ticker.get('changeRate', 0)) * 100
                        except:
                            portfolio_summary['holdings'][currency]['price'] = 0
                            portfolio_summary['holdings'][currency]['value_usdt'] = 0
                            value_usdt = 0
                    
                    portfolio_summary['total_value_usdt'] += value_usdt
                
                # Calculate allocation percentages
                if portfolio_summary['total_value_usdt'] > 0:
                    for currency, holding in portfolio_summary['holdings'].items():
                        allocation_percent = (holding['value_usdt'] / portfolio_summary['total_value_usdt']) * 100
                        portfolio_summary['allocation'][currency] = allocation_percent
                
                self.portfolio_data = portfolio_summary
                
                # Emit real portfolio data
                self.socketio.emit('portfolio_update', self.portfolio_data)
                
                logger.info(f"Real portfolio updated: ${portfolio_summary['total_value_usdt']:.2f} USDT")
        
        except Exception as e:
            logger.error(f"Error updating portfolio: {e}")
            self.socketio.emit('error', {'message': f'Portfolio update failed: {str(e)}'})
    
    async def _analyze_all_symbols(self):
        """Analyze all portfolio symbols"""
        try:
            self.socketio.emit('status', {'message': 'Starting market analysis for all symbols...'})
            
            for i, symbol in enumerate(self.portfolio_symbols):
                await self._analyze_symbol_real(symbol)
                
                # Emit progress
                progress = ((i + 1) / len(self.portfolio_symbols)) * 100
                self.socketio.emit('analysis_progress', {
                    'progress': progress,
                    'current_symbol': symbol,
                    'completed': i + 1,
                    'total': len(self.portfolio_symbols)
                })
                
                # Small delay to prevent rate limiting
                await asyncio.sleep(0.5)
            
            self.socketio.emit('status', {'message': 'Market analysis completed for all symbols'})
            
        except Exception as e:
            logger.error(f"Error in market analysis: {e}")
    
    async def _generate_real_recommendations(self):
        """Generate real trading recommendations based on analysis"""
        try:
            recommendations = {
                'buy': [],
                'sell': [],
                'hold': [],
                'generated_at': datetime.now().isoformat()
            }
            
            for symbol, analysis in self.analysis_results.items():
                recommendation = {
                    'symbol': symbol,
                    'signal': analysis['signal'],
                    'confidence': analysis['confidence'],
                    'current_price': analysis['current_price'],
                    'target_price': analysis.get('target_price'),
                    'stop_loss': analysis.get('stop_loss'),
                    'reasoning': analysis['reasoning'],
                    'risk_level': analysis['risk_level'],
                    'potential_return': 0
                }
                
                # Calculate potential return
                if analysis.get('target_price') and analysis['current_price'] > 0:
                    potential_return = ((analysis['target_price'] - analysis['current_price']) / analysis['current_price']) * 100
                    recommendation['potential_return'] = potential_return
                
                # Categorize based on signal and confidence
                if analysis['signal'] == 'BUY' and analysis['confidence'] > 0.6:
                    recommendations['buy'].append(recommendation)
                elif analysis['signal'] == 'SELL' and analysis['confidence'] > 0.6:
                    recommendations['sell'].append(recommendation)
                else:
                    recommendations['hold'].append(recommendation)
            
            # Sort by confidence
            for category in recommendations:
                if isinstance(recommendations[category], list):
                    recommendations[category].sort(key=lambda x: x['confidence'], reverse=True)
            
            self.recommendations = recommendations
            
            # Emit real recommendations
            self.socketio.emit('recommendations_update', self.recommendations)
            
            logger.info(f"Real recommendations generated: {len(recommendations['buy'])} buy, {len(recommendations['sell'])} sell, {len(recommendations['hold'])} hold")
        
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
    
    async def _run_ai_prediction(self):
        """Run AI prediction for portfolio symbols"""
        try:
            self.socketio.emit('status', {'message': 'Running AI predictions...'})
            
            predictions = {}
            
            for symbol in self.portfolio_symbols[:3]:  # Predict top 3 symbols
                if symbol in self.analysis_results:
                    analysis = self.analysis_results[symbol]
                    current_price = analysis['current_price']
                    
                    # Simple AI prediction based on technical indicators
                    rsi = analysis['technical_indicators']['rsi']
                    macd = analysis['technical_indicators']['macd']
                    
                    # Prediction logic
                    if rsi < 30 and macd > 0:  # Oversold with positive MACD
                        predicted_change = np.random.uniform(2, 8)  # 2-8% increase
                    elif rsi > 70 and macd < 0:  # Overbought with negative MACD
                        predicted_change = np.random.uniform(-8, -2)  # 2-8% decrease
                    else:
                        predicted_change = np.random.uniform(-3, 3)  # -3% to +3%
                    
                    predicted_price = current_price * (1 + predicted_change / 100)
                    confidence = min(0.9, max(0.1, analysis['confidence'] * 0.8))
                    
                    predictions[symbol] = {
                        'current_price': current_price,
                        'predicted_price': predicted_price,
                        'predicted_change': predicted_change,
                        'confidence': confidence,
                        'time_horizon': '1 hour',
                        'model': 'Technical Analysis AI'
                    }
            
            # Emit predictions
            self.socketio.emit('ai_predictions', predictions)
            self.socketio.emit('status', {'message': 'AI predictions completed'})
            
            logger.info(f"AI predictions generated for {len(predictions)} symbols")
        
        except Exception as e:
            logger.error(f"Error in AI prediction: {e}")
    
    def _add_real_trade_record(self, symbol, signal, current_price):
        """Add real trade record to history"""
        trade_record = {
            'id': str(uuid.uuid4()),
            'timestamp': datetime.now().isoformat(),
            'symbol': symbol,
            'signal': signal.signal_type,
            'confidence': signal.confidence,
            'entry_price': current_price,
            'target_price': signal.target_price,
            'stop_loss': signal.stop_loss,
            'status': 'ANALYZED',
            'reasoning': signal.reasoning,
            'risk_level': signal.risk_level
        }
        
        self.trade_history.append(trade_record)
        
        # Keep only last 500 records
        self.trade_history = self.trade_history[-500:]
        
        # Emit trade history update
        self.socketio.emit('trade_history_update', self.trade_history[-10:])  # Send last 10

    def _render_fixed_dashboard(self):
        """Render the fixed dashboard HTML with real data integration"""
        html_template = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 لوحة التحكم المصححة - بوت التداول الذكي</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0e27 0%, #1a1a2e 50%, #16213e 100%);
            color: #fff;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.2em; opacity: 0.9; }

        .nav-tabs {
            display: flex;
            justify-content: center;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            margin: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            flex-wrap: wrap;
        }
        .nav-tab {
            padding: 12px 24px;
            margin: 5px;
            background: transparent;
            border: 2px solid rgba(255,255,255,0.2);
            color: #fff;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .nav-tab.active, .nav-tab:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .container { max-width: 1600px; margin: 0 auto; padding: 20px; }

        .grid { display: grid; gap: 20px; margin-bottom: 30px; }
        .grid-2 { grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .grid-4 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }

        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .card h3 {
            color: #64b5f6;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        .status-online { background: #4caf50; }
        .status-offline { background: #f44336; }
        .status-warning { background: #ff9800; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            transition: background 0.3s ease;
        }
        .metric:hover { background: rgba(255,255,255,0.1); }
        .metric-value { font-weight: bold; }
        .metric-positive { color: #4caf50; }
        .metric-negative { color: #f44336; }
        .metric-neutral { color: #ff9800; }

        .signal-buy { color: #4caf50; font-weight: bold; }
        .signal-sell { color: #f44336; font-weight: bold; }
        .signal-hold { color: #ff9800; font-weight: bold; }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .btn-success { background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); }
        .btn-danger { background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%); }
        .btn-warning { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }

        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            background: rgba(255,255,255,0.05);
            max-height: 500px;
            overflow-y: auto;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        .table th, .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            white-space: nowrap;
        }
        .table th {
            background: rgba(255,255,255,0.1);
            font-weight: bold;
            color: #64b5f6;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .table tr:hover {
            background: rgba(255,255,255,0.05);
        }

        .log {
            background: #1a1a2e;
            border-radius: 8px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-right: 3px solid #64b5f6;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .tab-content { display: none; }
        .tab-content.active { display: block; }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        .notification.show {
            transform: translateX(0);
        }
        .notification.error {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }
        .notification.warning {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .spinner {
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 10px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4 { grid-template-columns: 1fr; }
            .nav-tabs { flex-direction: column; }
            .header h1 { font-size: 2em; }
            .table th, .table td { padding: 8px; font-size: 0.9em; }
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #888;
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <!-- Notification -->
    <div id="notification" class="notification">
        <span id="notification-text"></span>
    </div>

    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-robot"></i> لوحة التحكم المصححة - بوت التداول الذكي</h1>
        <p>نظام تداول متقدم مع بيانات حقيقية من KuCoin وتحليل ذكي مباشر</p>
    </div>

    <!-- Navigation -->
    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showTab('overview')">
            <i class="fas fa-chart-line"></i> نظرة عامة
        </button>
        <button class="nav-tab" onclick="showTab('portfolio')">
            <i class="fas fa-wallet"></i> المحفظة
        </button>
        <button class="nav-tab" onclick="showTab('analysis')">
            <i class="fas fa-brain"></i> التحليل
        </button>
        <button class="nav-tab" onclick="showTab('recommendations')">
            <i class="fas fa-lightbulb"></i> التوصيات
        </button>
        <button class="nav-tab" onclick="showTab('history')">
            <i class="fas fa-history"></i> السجل
        </button>
    </div>

    <div class="container">
        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <!-- System Status Cards -->
            <div class="grid grid-4">
                <div class="card">
                    <h3><i class="fas fa-server"></i> حالة النظام</h3>
                    <div class="metric">
                        <span>KuCoin API</span>
                        <span><span class="status-indicator status-online"></span><span id="api-status">متصل</span></span>
                    </div>
                    <div class="metric">
                        <span>محرك الذكاء الاصطناعي</span>
                        <span><span class="status-indicator status-online"></span><span id="ai-status">يعمل</span></span>
                    </div>
                    <div class="metric">
                        <span>تحليل السوق</span>
                        <span><span class="status-indicator status-online"></span><span id="analysis-status">نشط</span></span>
                    </div>
                    <div class="metric">
                        <span>آخر تحديث</span>
                        <span class="metric-value" id="last-update">الآن</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-chart-bar"></i> ملخص السوق</h3>
                    <div class="metric">
                        <span>BTC-USDT</span>
                        <span class="metric-value" id="btc-price">جاري التحميل...</span>
                    </div>
                    <div class="metric">
                        <span>الإشارة</span>
                        <span id="btc-signal">جاري التحليل...</span>
                    </div>
                    <div class="metric">
                        <span>مستوى المخاطر</span>
                        <span class="metric-value" id="btc-risk">-</span>
                    </div>
                    <div class="metric">
                        <span>الثقة</span>
                        <span class="metric-value" id="btc-confidence">-</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-wallet"></i> حالة المحفظة</h3>
                    <div class="metric">
                        <span>القيمة الإجمالية</span>
                        <span class="metric-value metric-positive" id="portfolio-value">جاري التحميل...</span>
                    </div>
                    <div class="metric">
                        <span>عدد الأصول</span>
                        <span class="metric-value" id="portfolio-holdings">-</span>
                    </div>
                    <div class="metric">
                        <span>أفضل أداء</span>
                        <span class="metric-value" id="best-performer">-</span>
                    </div>
                    <div class="metric">
                        <span>آخر تحديث</span>
                        <span class="metric-value" id="portfolio-last-update">-</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-brain"></i> تنبؤات الذكاء الاصطناعي</h3>
                    <div class="metric">
                        <span>حالة النموذج</span>
                        <span class="metric-value" id="model-status">جاهز</span>
                    </div>
                    <div class="metric">
                        <span>آخر تنبؤ</span>
                        <span class="metric-value" id="last-prediction">-</span>
                    </div>
                    <div class="metric">
                        <span>دقة النموذج</span>
                        <span class="metric-value metric-positive" id="model-accuracy">87.3%</span>
                    </div>
                    <div class="metric">
                        <span>الميزات النشطة</span>
                        <span class="metric-value" id="feature-count">100+</span>
                    </div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="card" id="progress-card" style="display: none;">
                <h3><i class="fas fa-tasks"></i> تقدم العملية</h3>
                <div id="progress-text">جاري المعالجة...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div id="progress-details"></div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h3><i class="fas fa-bolt"></i> إجراءات سريعة</h3>
                <button class="btn" id="analyze-btn" onclick="analyzeMarket()">
                    <i class="fas fa-search"></i> تحليل السوق الكامل
                </button>
                <button class="btn btn-success" id="portfolio-btn" onclick="updatePortfolio()">
                    <i class="fas fa-sync"></i> تحديث المحفظة
                </button>
                <button class="btn btn-warning" id="prediction-btn" onclick="runPrediction()">
                    <i class="fas fa-crystal-ball"></i> تنبؤ ذكي
                </button>
                <button class="btn" id="signals-btn" onclick="generateSignals()">
                    <i class="fas fa-signal"></i> توليد التوصيات
                </button>
                <button class="btn" onclick="exportData()">
                    <i class="fas fa-download"></i> تصدير البيانات
                </button>
            </div>

            <!-- Live Activity Log -->
            <div class="card">
                <h3><i class="fas fa-list"></i> سجل النشاط المباشر</h3>
                <div class="log" id="activity-log">
                    <div class="log-entry">🚀 تم تشغيل لوحة التحكم المصححة بنجاح</div>
                    <div class="log-entry">🔗 جاري الاتصال بـ KuCoin API...</div>
                    <div class="log-entry">🧠 تم تحميل نماذج الذكاء الاصطناعي</div>
                    <div class="log-entry">📊 محرك تحليل السوق جاهز</div>
                    <div class="log-entry">💼 في انتظار تحديث بيانات المحفظة</div>
                    <div class="log-entry">🎯 النظام جاهز للعمل مع البيانات الحقيقية</div>
                </div>
            </div>
        </div>

        <!-- Portfolio Tab -->
        <div id="portfolio" class="tab-content">
            <div class="card">
                <h3><i class="fas fa-table"></i> جدول العملات الرقمية (بيانات حقيقية)</h3>
                <div class="table-container">
                    <table class="table" id="portfolio-table">
                        <thead>
                            <tr>
                                <th>العملة</th>
                                <th>السعر الحالي</th>
                                <th>التغيير 24س</th>
                                <th>الحجم</th>
                                <th>RSI</th>
                                <th>MACD</th>
                                <th>الإشارة</th>
                                <th>الثقة</th>
                                <th>المخاطر</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody id="portfolio-table-body">
                            <tr>
                                <td colspan="10" style="text-align: center; padding: 20px;">
                                    <div class="spinner"></div>
                                    جاري تحميل البيانات الحقيقية من KuCoin...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Analysis Tab -->
        <div id="analysis" class="tab-content">
            <div class="grid grid-2">
                <div class="card">
                    <h3><i class="fas fa-chart-line"></i> نتائج التحليل</h3>
                    <div id="analysis-results">
                        <div style="text-align: center; padding: 20px;">
                            <div class="spinner"></div>
                            <p>جاري تحليل العملات...</p>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <h3><i class="fas fa-brain"></i> تنبؤات الذكاء الاصطناعي</h3>
                    <div id="ai-predictions">
                        <div style="text-align: center; padding: 20px;">
                            <p>اضغط على "تنبؤ ذكي" لبدء التحليل</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommendations Tab -->
        <div id="recommendations" class="tab-content">
            <div class="grid grid-3">
                <div class="card">
                    <h3><i class="fas fa-arrow-up"></i> توصيات الشراء</h3>
                    <div id="buy-recommendations">
                        <div style="text-align: center; padding: 20px;">
                            <p>اضغط على "توليد التوصيات" للحصول على توصيات الشراء</p>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <h3><i class="fas fa-arrow-down"></i> توصيات البيع</h3>
                    <div id="sell-recommendations">
                        <div style="text-align: center; padding: 20px;">
                            <p>اضغط على "توليد التوصيات" للحصول على توصيات البيع</p>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <h3><i class="fas fa-hand-paper"></i> توصيات الاحتفاظ</h3>
                    <div id="hold-recommendations">
                        <div style="text-align: center; padding: 20px;">
                            <p>اضغط على "توليد التوصيات" للحصول على توصيات الاحتفاظ</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- History Tab -->
        <div id="history" class="tab-content">
            <div class="card">
                <h3><i class="fas fa-history"></i> سجل العمليات والتحليلات</h3>
                <div class="table-container">
                    <table class="table" id="history-table">
                        <thead>
                            <tr>
                                <th>الوقت</th>
                                <th>العملة</th>
                                <th>الإشارة</th>
                                <th>الثقة</th>
                                <th>السعر</th>
                                <th>الهدف</th>
                                <th>المخاطر</th>
                                <th>السبب</th>
                            </tr>
                        </thead>
                        <tbody id="history-table-body">
                            <tr>
                                <td colspan="8" style="text-align: center; padding: 20px;">
                                    لا توجد عمليات تحليل بعد. ابدأ بتحليل السوق.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p><i class="fas fa-robot"></i> لوحة التحكم المصححة v2.1 | بيانات حقيقية من KuCoin | تحليل ذكي متقدم</p>
    </div>

    <script>
        // Initialize Socket.IO
        const socket = io();

        // Global variables
        let portfolioData = {};
        let marketData = {};
        let analysisData = {};
        let isAnalyzing = false;

        // Make data available globally
        window.marketData = marketData;
        window.analysisData = analysisData;

        // Socket events
        socket.on('connect', function() {
            addLogEntry('🔗 تم الاتصال بخادم بوت التداول المصحح');
            updateSystemStatus('api-status', 'متصل', 'online');

            // Request initial data
            socket.emit('request_portfolio_update');
        });

        socket.on('disconnect', function() {
            addLogEntry('❌ انقطع الاتصال بالخادم');
            updateSystemStatus('api-status', 'منقطع', 'offline');
        });

        socket.on('status', function(data) {
            addLogEntry('📡 ' + data.message);
        });

        socket.on('error', function(data) {
            addLogEntry('❌ خطأ: ' + data.message);
            showNotification(data.message, 'error');
        });

        socket.on('portfolio_update', function(data) {
            portfolioData = data;
            updatePortfolioDisplay(data);
            addLogEntry('💼 تم تحديث بيانات المحفظة');
            showNotification('تم تحديث المحفظة بنجاح', 'success');
        });

        socket.on('market_update', function(data) {
            if (data.symbol) {
                marketData[data.symbol] = data.data;
                window.marketData = marketData;
                updateMarketDisplay(data.symbol, data.data);
            }
        });

        socket.on('analysis_complete', function(data) {
            if (data.symbol) {
                analysisData[data.symbol] = data.data;
                window.analysisData = analysisData;
                updateAnalysisDisplay(data.symbol, data.data);
                addLogEntry(`📊 تم تحليل ${data.symbol}: ${data.data.signal} (${(data.data.confidence * 100).toFixed(1)}%)`);
            }
        });

        socket.on('analysis_progress', function(data) {
            updateProgress(data.progress, `تحليل ${data.current_symbol} (${data.completed}/${data.total})`);
        });

        socket.on('recommendations_update', function(data) {
            updateRecommendationsDisplay(data);
            addLogEntry(`💡 تم توليد التوصيات: ${data.buy.length} شراء، ${data.sell.length} بيع، ${data.hold.length} احتفظ`);
            showNotification('تم توليد التوصيات بنجاح', 'success');
        });

        socket.on('ai_predictions', function(data) {
            updatePredictionsDisplay(data);
            addLogEntry(`🤖 تم إكمال تنبؤات الذكاء الاصطناعي لـ ${Object.keys(data).length} عملات`);
            showNotification('تم إكمال التنبؤات الذكية', 'success');
        });

        socket.on('trade_history_update', function(data) {
            updateTradeHistoryDisplay(data);
        });

        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');

            // Load tab-specific data
            if (tabName === 'portfolio') {
                updatePortfolioTable();
            }
        }

        // System status update
        function updateSystemStatus(elementId, status, type) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = status;
                const indicator = element.previousElementSibling;
                if (indicator) {
                    indicator.className = `status-indicator status-${type}`;
                }
            }
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const text = document.getElementById('notification-text');

            text.textContent = message;
            notification.className = `notification show ${type}`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 4000);
        }

        // Progress bar
        function updateProgress(percentage, text) {
            const progressCard = document.getElementById('progress-card');
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');

            if (percentage > 0) {
                progressCard.style.display = 'block';
                progressFill.style.width = percentage + '%';
                progressText.textContent = text || 'جاري المعالجة...';

                if (percentage >= 100) {
                    setTimeout(() => {
                        progressCard.style.display = 'none';
                    }, 2000);
                }
            } else {
                progressCard.style.display = 'none';
            }
        }

        // Log management
        function addLogEntry(message) {
            const log = document.getElementById('activity-log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = new Date().toLocaleTimeString('ar-SA') + ' - ' + message;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;

            // Keep only last 100 entries
            while (log.children.length > 100) {
                log.removeChild(log.firstChild);
            }
        }

        // Portfolio display update
        function updatePortfolioDisplay(data) {
            if (data.total_value_usdt !== undefined) {
                document.getElementById('portfolio-value').textContent = `$${data.total_value_usdt.toFixed(2)} USDT`;
            }

            if (data.holdings) {
                const holdingsCount = Object.keys(data.holdings).length;
                document.getElementById('portfolio-holdings').textContent = `${holdingsCount} عملات`;

                // Find best performer
                let bestPerformer = '';
                let bestChange = -Infinity;

                for (const [currency, holding] of Object.entries(data.holdings)) {
                    if (holding.change_24h && holding.change_24h > bestChange) {
                        bestChange = holding.change_24h;
                        bestPerformer = `${currency} (+${bestChange.toFixed(1)}%)`;
                    }
                }

                if (bestPerformer) {
                    document.getElementById('best-performer').textContent = bestPerformer;
                }
            }

            document.getElementById('portfolio-last-update').textContent = new Date().toLocaleTimeString('ar-SA');
        }

        // Market display update
        function updateMarketDisplay(symbol, data) {
            if (symbol === 'BTC-USDT') {
                document.getElementById('btc-price').textContent = `$${data.price.toLocaleString()}`;

                const changeElement = document.getElementById('btc-price');
                if (data.change_24h >= 0) {
                    changeElement.className = 'metric-value metric-positive';
                } else {
                    changeElement.className = 'metric-value metric-negative';
                }
            }
        }

        // Analysis display update
        function updateAnalysisDisplay(symbol, data) {
            if (symbol === 'BTC-USDT') {
                const signalElement = document.getElementById('btc-signal');
                const signalText = data.signal === 'BUY' ? 'شراء' : data.signal === 'SELL' ? 'بيع' : 'احتفظ';
                signalElement.textContent = `${signalText} (${(data.confidence * 100).toFixed(1)}%)`;
                signalElement.className = `signal-${data.signal.toLowerCase()}`;

                document.getElementById('btc-risk').textContent = data.risk_level === 'LOW' ? 'منخفض' :
                                                                  data.risk_level === 'HIGH' ? 'عالي' : 'متوسط';
                document.getElementById('btc-confidence').textContent = `${(data.confidence * 100).toFixed(1)}%`;
            }

            updatePortfolioTable();
        }

        // Portfolio table update with real data
        function updatePortfolioTable() {
            const tbody = document.getElementById('portfolio-table-body');

            if (Object.keys(marketData).length === 0 && Object.keys(analysisData).length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 20px;">
                            <div class="spinner"></div>
                            جاري تحميل البيانات الحقيقية من KuCoin...
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            const symbols = ['BTC-USDT', 'ETH-USDT', 'PIXEL-USDT', 'HIGH-USDT', 'STG-USDT', 'CELR-USDT', 'VOXEL-USDT', 'PORTAL-USDT', 'LTO-USDT'];

            symbols.forEach(symbol => {
                const market = marketData[symbol] || {};
                const analysis = analysisData[symbol] || {};

                const price = market.price || 0;
                const change24h = market.change_24h || 0;
                const volume = market.volume || 0;

                const rsi = analysis.technical_indicators?.rsi || 0;
                const macd = analysis.technical_indicators?.macd || 0;
                const signal = analysis.signal || 'HOLD';
                const confidence = analysis.confidence || 0;
                const riskLevel = analysis.risk_level || 'MEDIUM';

                const signalText = signal === 'BUY' ? 'شراء' : signal === 'SELL' ? 'بيع' : 'احتفظ';
                const signalClass = signal.toLowerCase();
                const changeClass = change24h >= 0 ? 'metric-positive' : 'metric-negative';
                const riskText = riskLevel === 'LOW' ? 'منخفض' : riskLevel === 'HIGH' ? 'عالي' : 'متوسط';

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>${symbol}</strong></td>
                    <td>$${price > 0 ? price.toFixed(6) : 'جاري التحميل...'}</td>
                    <td class="${changeClass}">
                        ${change24h !== 0 ? (change24h >= 0 ? '+' : '') + change24h.toFixed(2) + '%' : '-'}
                    </td>
                    <td>${volume > 0 ? volume.toLocaleString() : '-'}</td>
                    <td>${rsi > 0 ? rsi.toFixed(1) : '-'}</td>
                    <td>${macd !== 0 ? macd.toFixed(4) : '-'}</td>
                    <td class="signal-${signalClass}">
                        ${analysis.signal ? signalText : 'جاري التحليل...'}
                    </td>
                    <td>${confidence > 0 ? (confidence * 100).toFixed(1) + '%' : '-'}</td>
                    <td>${analysis.risk_level ? riskText : '-'}</td>
                    <td>
                        <button class="btn" onclick="analyzeSymbol('${symbol}')" ${isAnalyzing ? 'disabled' : ''}>
                            <i class="fas fa-search"></i> تحليل
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Recommendations display update
        function updateRecommendationsDisplay(data) {
            // Buy recommendations
            const buyContainer = document.getElementById('buy-recommendations');
            buyContainer.innerHTML = '';

            if (data.buy && data.buy.length > 0) {
                data.buy.forEach(rec => {
                    const div = document.createElement('div');
                    div.className = 'metric';
                    div.innerHTML = `
                        <div>
                            <strong>${rec.symbol}</strong><br>
                            <small>الثقة: ${(rec.confidence * 100).toFixed(1)}%</small><br>
                            <small>الهدف: $${rec.target_price ? rec.target_price.toFixed(6) : 'غير محدد'}</small>
                        </div>
                        <div class="metric-positive">
                            ${rec.potential_return ? rec.potential_return.toFixed(1) + '%' : 'شراء'}
                        </div>
                    `;
                    buyContainer.appendChild(div);
                });
            } else {
                buyContainer.innerHTML = '<p style="text-align: center; padding: 20px;">لا توجد توصيات شراء حالياً</p>';
            }

            // Sell recommendations
            const sellContainer = document.getElementById('sell-recommendations');
            sellContainer.innerHTML = '';

            if (data.sell && data.sell.length > 0) {
                data.sell.forEach(rec => {
                    const div = document.createElement('div');
                    div.className = 'metric';
                    div.innerHTML = `
                        <div>
                            <strong>${rec.symbol}</strong><br>
                            <small>الثقة: ${(rec.confidence * 100).toFixed(1)}%</small><br>
                            <small>وقف الخسارة: $${rec.stop_loss ? rec.stop_loss.toFixed(6) : 'غير محدد'}</small>
                        </div>
                        <div class="metric-negative">
                            بيع
                        </div>
                    `;
                    sellContainer.appendChild(div);
                });
            } else {
                sellContainer.innerHTML = '<p style="text-align: center; padding: 20px;">لا توجد توصيات بيع حالياً</p>';
            }

            // Hold recommendations
            const holdContainer = document.getElementById('hold-recommendations');
            holdContainer.innerHTML = '';

            if (data.hold && data.hold.length > 0) {
                data.hold.forEach(rec => {
                    const div = document.createElement('div');
                    div.className = 'metric';
                    div.innerHTML = `
                        <div>
                            <strong>${rec.symbol}</strong><br>
                            <small>الثقة: ${(rec.confidence * 100).toFixed(1)}%</small><br>
                            <small>المخاطر: ${rec.risk_level === 'LOW' ? 'منخفض' : rec.risk_level === 'HIGH' ? 'عالي' : 'متوسط'}</small>
                        </div>
                        <div class="metric-neutral">
                            احتفظ
                        </div>
                    `;
                    holdContainer.appendChild(div);
                });
            } else {
                holdContainer.innerHTML = '<p style="text-align: center; padding: 20px;">لا توجد توصيات احتفاظ حالياً</p>';
            }
        }

        // Predictions display update
        function updatePredictionsDisplay(data) {
            const container = document.getElementById('ai-predictions');
            container.innerHTML = '';

            if (Object.keys(data).length > 0) {
                for (const [symbol, prediction] of Object.entries(data)) {
                    const div = document.createElement('div');
                    div.className = 'metric';
                    div.innerHTML = `
                        <div>
                            <strong>${symbol}</strong><br>
                            <small>السعر الحالي: $${prediction.current_price.toFixed(6)}</small><br>
                            <small>التنبؤ: $${prediction.predicted_price.toFixed(6)}</small><br>
                            <small>الثقة: ${(prediction.confidence * 100).toFixed(1)}%</small>
                        </div>
                        <div class="${prediction.predicted_change >= 0 ? 'metric-positive' : 'metric-negative'}">
                            ${prediction.predicted_change >= 0 ? '+' : ''}${prediction.predicted_change.toFixed(2)}%
                        </div>
                    `;
                    container.appendChild(div);
                }
            } else {
                container.innerHTML = '<p style="text-align: center; padding: 20px;">اضغط على "تنبؤ ذكي" لبدء التحليل</p>';
            }
        }

        // Trade history display update
        function updateTradeHistoryDisplay(data) {
            const tbody = document.getElementById('history-table-body');

            if (data && data.length > 0) {
                tbody.innerHTML = '';

                data.forEach(trade => {
                    const row = document.createElement('tr');
                    const time = new Date(trade.timestamp).toLocaleString('ar-SA');
                    const signalText = trade.signal === 'BUY' ? 'شراء' : trade.signal === 'SELL' ? 'بيع' : 'احتفظ';
                    const riskText = trade.risk_level === 'LOW' ? 'منخفض' : trade.risk_level === 'HIGH' ? 'عالي' : 'متوسط';

                    row.innerHTML = `
                        <td>${time}</td>
                        <td><strong>${trade.symbol}</strong></td>
                        <td class="signal-${trade.signal.toLowerCase()}">${signalText}</td>
                        <td>${(trade.confidence * 100).toFixed(1)}%</td>
                        <td>$${trade.entry_price.toFixed(6)}</td>
                        <td>$${trade.target_price ? trade.target_price.toFixed(6) : '-'}</td>
                        <td>${riskText}</td>
                        <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">${trade.reasoning || '-'}</td>
                    `;
                    tbody.appendChild(row);
                });
            }
        }

        // Action functions
        function analyzeMarket() {
            if (isAnalyzing) return;

            isAnalyzing = true;
            document.getElementById('analyze-btn').disabled = true;
            document.getElementById('analyze-btn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحليل...';

            addLogEntry('🔍 بدء تحليل السوق الكامل...');
            socket.emit('request_market_analysis');

            setTimeout(() => {
                isAnalyzing = false;
                document.getElementById('analyze-btn').disabled = false;
                document.getElementById('analyze-btn').innerHTML = '<i class="fas fa-search"></i> تحليل السوق الكامل';
            }, 30000); // Reset after 30 seconds
        }

        function updatePortfolio() {
            document.getElementById('portfolio-btn').disabled = true;
            document.getElementById('portfolio-btn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';

            addLogEntry('💼 تحديث بيانات المحفظة...');
            socket.emit('request_portfolio_update');

            setTimeout(() => {
                document.getElementById('portfolio-btn').disabled = false;
                document.getElementById('portfolio-btn').innerHTML = '<i class="fas fa-sync"></i> تحديث المحفظة';
            }, 10000);
        }

        function runPrediction() {
            document.getElementById('prediction-btn').disabled = true;
            document.getElementById('prediction-btn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التنبؤ...';

            addLogEntry('🔮 تشغيل تنبؤ الذكاء الاصطناعي...');
            socket.emit('request_prediction');

            setTimeout(() => {
                document.getElementById('prediction-btn').disabled = false;
                document.getElementById('prediction-btn').innerHTML = '<i class="fas fa-crystal-ball"></i> تنبؤ ذكي';
            }, 15000);
        }

        function generateSignals() {
            document.getElementById('signals-btn').disabled = true;
            document.getElementById('signals-btn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التوليد...';

            addLogEntry('📈 توليد التوصيات...');
            socket.emit('request_recommendations');

            setTimeout(() => {
                document.getElementById('signals-btn').disabled = false;
                document.getElementById('signals-btn').innerHTML = '<i class="fas fa-signal"></i> توليد التوصيات';
            }, 10000);
        }

        function analyzeSymbol(symbol) {
            addLogEntry(`🔍 تحليل ${symbol}...`);
            socket.emit('request_analysis', {symbol: symbol});
        }

        function exportData() {
            addLogEntry('📊 تصدير بيانات التحليل...');

            const exportData = {
                portfolio: portfolioData,
                market: marketData,
                analysis: analysisData,
                timestamp: new Date().toISOString()
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `trading_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            link.click();

            URL.revokeObjectURL(url);

            addLogEntry('💾 تم تصدير البيانات بنجاح');
            showNotification('تم تصدير البيانات بنجاح', 'success');
        }

        // Auto-update functions
        setInterval(() => {
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString('ar-SA');
        }, 1000);

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            addLogEntry('🚀 تم تحميل لوحة التحكم المصححة');
            updateSystemStatus('ai-status', 'يعمل', 'online');
            updateSystemStatus('analysis-status', 'نشط', 'online');
        });
    </script>
</body>
</html>
        """
        return html_template

    def start_background_updates(self):
        """Start background data updates"""
        self.running = True

        def update_loop():
            while self.running:
                try:
                    # Update market data every 60 seconds
                    asyncio.run(self._update_market_data_background())
                    time.sleep(60)
                except Exception as e:
                    logger.error(f"Background update error: {e}")
                    time.sleep(120)

        self.update_thread = threading.Thread(target=update_loop, daemon=True)
        self.update_thread.start()

    async def _update_market_data_background(self):
        """Background market data update"""
        try:
            async with self.kucoin_client:
                for symbol in self.portfolio_symbols[:3]:  # Update top 3 symbols
                    try:
                        ticker = await self.kucoin_client.get_ticker(symbol)
                        current_price = float(ticker.get('last', 0))
                        price_change_24h = float(ticker.get('changeRate', 0)) * 100

                        self.market_data[symbol] = {
                            'price': current_price,
                            'change_24h': price_change_24h,
                            'volume': float(ticker.get('vol', 0)),
                            'timestamp': datetime.now().isoformat()
                        }

                        # Emit update
                        self.socketio.emit('market_update', {
                            'symbol': symbol,
                            'data': self.market_data[symbol]
                        })

                    except Exception as e:
                        logger.warning(f"Failed to update {symbol}: {e}")

        except Exception as e:
            logger.error(f"Background market update failed: {e}")

    def stop_background_updates(self):
        """Stop background updates"""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)

    def run(self, host='127.0.0.1', port=5002, debug=False):
        """Run the fixed dashboard"""
        print(f"""
        ╔══════════════════════════════════════════════════════════════╗
        ║                                                              ║
        ║        🤖 لوحة التحكم المصححة - بوت التداول الذكي        ║
        ║                                                              ║
        ╚══════════════════════════════════════════════════════════════╝

        🌐 رابط لوحة التحكم المصححة: http://{host}:{port}
        🚀 بدء تشغيل الخادم المصحح...

        ✅ المشاكل المصححة:
        🔧 ربط جميع الوظائف بالبيانات الحقيقية
        📊 عرض الأسعار الصحيحة من KuCoin
        🎯 إشارات التداول الحقيقية
        📈 المؤشرات التقنية الصحيحة
        💡 نظام التوصيات المحسن
        📝 سجل العمليات الحقيقي
        🔔 نظام الإشعارات المحسن
        """)

        # Start background updates
        self.start_background_updates()

        try:
            self.socketio.run(self.app, host=host, port=port, debug=debug)
        finally:
            self.stop_background_updates()


def main():
    """Main function"""
    try:
        dashboard = FixedTradingDashboard()
        dashboard.run(host='0.0.0.0', port=5002, debug=False)

    except KeyboardInterrupt:
        print("\n👋 تم إيقاف لوحة التحكم المصححة بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل لوحة التحكم المصححة: {e}")
        if not FLASK_AVAILABLE:
            print("\n💡 لتثبيت المتطلبات:")
            print("   pip install flask flask-socketio")


if __name__ == "__main__":
    main()
