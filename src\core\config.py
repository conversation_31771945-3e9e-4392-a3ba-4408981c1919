"""
Configuration management for AI Crypto Trading Bot
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator
from pydantic_settings import BaseSettings as PydanticBaseSettings


class Settings(PydanticBaseSettings):
    """Application settings loaded from environment variables"""
    
    # Application Settings
    app_name: str = "AI Crypto Trading Bot"
    app_version: str = "1.0.0"
    debug: bool = False
    log_level: str = "INFO"
    
    # Trading Configuration
    binance_api_key: str = ""
    binance_secret_key: str = ""
    binance_testnet: bool = True
    
    # Trading Parameters
    default_trading_pairs: str = "BTCUSDT,ETHUSDT,ADAUSDT,DOTUSDT,LINKUSDT"
    max_risk_per_trade: float = 0.02
    stop_loss_percentage: float = 0.05
    take_profit_percentage: float = 0.10
    min_trade_amount: float = 10.0
    
    # Database Configuration
    database_url: str = "postgresql://username:password@localhost:5432/crypto_trading_bot"
    database_pool_size: int = 10
    database_max_overflow: int = 20
    
    # Redis Configuration
    redis_url: str = "redis://localhost:6379/0"
    redis_password: str = ""
    redis_db: int = 0
    
    # AI & ML Configuration
    ai_model_update_interval: int = 3600
    prediction_confidence_threshold: float = 0.7
    sentiment_analysis_enabled: bool = True
    technical_indicators_enabled: bool = True
    
    # Data Collection
    historical_data_days: int = 365
    real_time_data_interval: int = 60
    
    # Notification Configuration
    telegram_bot_token: str = ""
    telegram_chat_id: str = ""
    telegram_notifications_enabled: bool = False
    
    # Email Configuration
    smtp_server: str = "smtp.gmail.com"
    smtp_port: int = 587
    smtp_username: str = ""
    smtp_password: str = ""
    email_notifications_enabled: bool = False
    notification_email: str = ""
    
    # Security Configuration
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # API Security
    api_key_encryption_enabled: bool = True
    rate_limiting_enabled: bool = True
    max_requests_per_minute: int = 60
    
    # Web Interface Configuration
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False
    
    # CORS Settings
    allowed_origins: str = "http://localhost:3000,http://localhost:8000"
    allowed_methods: str = "GET,POST,PUT,DELETE"
    allowed_headers: str = "*"
    
    # Monitoring & Logging
    log_file_path: str = "logs/trading_bot.log"
    log_rotation: str = "1 day"
    log_retention: str = "30 days"
    log_format: str = "json"
    
    # Monitoring
    prometheus_enabled: bool = False
    prometheus_port: int = 9090
    health_check_interval: int = 300
    
    # Advanced Trading Settings
    max_open_positions: int = 5
    max_daily_loss: float = 0.10
    portfolio_rebalance_interval: int = 86400
    
    # Market Analysis
    market_volatility_threshold: float = 0.05
    trend_confirmation_periods: int = 3
    volume_analysis_enabled: bool = True
    
    # Backtesting
    backtest_start_date: str = "2023-01-01"
    backtest_end_date: str = "2024-01-01"
    backtest_initial_balance: float = 10000.0
    
    # External APIs
    news_api_key: str = ""
    twitter_bearer_token: str = ""
    reddit_client_id: str = ""
    reddit_client_secret: str = ""
    alpha_vantage_api_key: str = ""
    coinmarketcap_api_key: str = ""
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @validator("default_trading_pairs")
    def validate_trading_pairs(cls, v):
        """Validate and parse trading pairs"""
        if isinstance(v, str):
            return [pair.strip().upper() for pair in v.split(",")]
        return v
    
    @validator("allowed_origins")
    def validate_allowed_origins(cls, v):
        """Parse allowed origins for CORS"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("allowed_methods")
    def validate_allowed_methods(cls, v):
        """Parse allowed methods for CORS"""
        if isinstance(v, str):
            return [method.strip().upper() for method in v.split(",")]
        return v
    
    @property
    def trading_pairs_list(self) -> List[str]:
        """Get trading pairs as a list"""
        if isinstance(self.default_trading_pairs, str):
            return [pair.strip().upper() for pair in self.default_trading_pairs.split(",")]
        return self.default_trading_pairs
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode"""
        return not self.debug and not self.binance_testnet
    
    @property
    def database_config(self) -> dict:
        """Get database configuration"""
        return {
            "url": self.database_url,
            "pool_size": self.database_pool_size,
            "max_overflow": self.database_max_overflow
        }
    
    @property
    def redis_config(self) -> dict:
        """Get Redis configuration"""
        return {
            "url": self.redis_url,
            "password": self.redis_password if self.redis_password else None,
            "db": self.redis_db
        }


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings


# Trading configuration constants
class TradingConfig:
    """Trading configuration constants"""
    
    # Supported trading pairs
    MAJOR_PAIRS = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "DOTUSDT"]
    ALTCOIN_PAIRS = ["LINKUSDT", "UNIUSDT", "LTCUSDT", "BCHUSDT", "XLMUSDT"]
    
    # Risk management
    MAX_PORTFOLIO_RISK = 0.20  # 20% maximum portfolio risk
    MIN_RISK_REWARD_RATIO = 1.5  # Minimum 1.5:1 risk/reward
    
    # Technical analysis
    RSI_OVERSOLD = 30
    RSI_OVERBOUGHT = 70
    MACD_SIGNAL_THRESHOLD = 0.001
    
    # AI model parameters
    LSTM_SEQUENCE_LENGTH = 60
    PREDICTION_HORIZON = 24  # Hours
    MODEL_RETRAIN_INTERVAL = 168  # Hours (1 week)


# Logging configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
        "json": {
            "format": "%(asctime)s %(name)s %(levelname)s %(message)s",
            "class": "pythonjsonlogger.jsonlogger.JsonFormatter",
        },
    },
    "handlers": {
        "default": {
            "formatter": "default",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stdout",
        },
        "file": {
            "formatter": "json",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": settings.log_file_path,
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
        },
    },
    "root": {
        "level": settings.log_level,
        "handlers": ["default", "file"],
    },
}
