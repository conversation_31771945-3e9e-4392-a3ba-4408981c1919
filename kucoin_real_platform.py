#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
منصة التداول الحقيقية مع KuCoin API
Real KuCoin Trading Platform

نظام تداول حقيقي متكامل:
- اتصال حقيقي مع KuCoin API
- تنفيذ صفقات حقيقية
- بيانات السوق الحقيقية
- إدارة مخاطر فعلية
"""

import json
import time
import uuid
import sqlite3
import asyncio
import threading
import logging
import hashlib
import hmac
import base64
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

# Flask and WebSocket
from flask import Flask, render_template_string, jsonify, request
from flask_socketio import SocketIO, emit

# HTTP requests
import aiohttp

# Data processing
import pandas as pd
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RealSignal:
    """إشارة تداول حقيقية"""
    symbol: str
    signal_type: str  # BUY, SELL, HOLD
    confidence: float
    current_price: float
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    risk_level: str = "MEDIUM"
    reasoning: str = ""
    volume_24h: float = 0.0
    price_change_24h: float = 0.0
    timestamp: str = ""

@dataclass
class RealTrade:
    """صفقة حقيقية"""
    trade_id: str
    order_id: str
    symbol: str
    side: str
    quantity: float
    price: float
    status: str
    fees: float
    timestamp: str

class KuCoinAPI:
    """عميل KuCoin API الحقيقي"""
    
    def __init__(self):
        """تهيئة العميل"""
        # KuCoin API credentials
        self.api_key = "686a4e782301b10001e7457c"
        self.secret_key = "61718954-dc69-4b89-b21c-dff5b80fff15"
        self.passphrase = "Eslam*17*3*1999"
        
        # API endpoints
        self.base_url = "https://api.kucoin.com"
        self.session = None
        self.connected = False
        
        logger.info("KuCoin API Client initialized - REAL TRADING MODE")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.disconnect()
    
    async def connect(self):
        """الاتصال بـ KuCoin API"""
        try:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # Test connection
            await self._test_connection()
            
            self.connected = True
            logger.info("✅ Connected to KuCoin API successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to KuCoin API: {e}")
            raise
    
    async def disconnect(self):
        """قطع الاتصال"""
        if self.session:
            await self.session.close()
            self.session = None
        self.connected = False
        logger.info("Disconnected from KuCoin API")
    
    def _generate_signature(self, timestamp: str, method: str, endpoint: str, body: str = "") -> str:
        """توليد التوقيع"""
        message = timestamp + method.upper() + endpoint + body
        signature = base64.b64encode(
            hmac.new(
                self.secret_key.encode('utf-8'),
                message.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        return signature
    
    def _generate_passphrase(self) -> str:
        """توليد كلمة المرور المشفرة"""
        return base64.b64encode(
            hmac.new(
                self.secret_key.encode('utf-8'),
                self.passphrase.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
    
    def _get_timestamp(self) -> str:
        """الحصول على الوقت الحالي"""
        return str(int(time.time() * 1000))
    
    async def _make_request(self, method: str, endpoint: str, params: Dict = None, data: Dict = None, signed: bool = False) -> Dict:
        """إجراء طلب HTTP"""
        if not self.session:
            raise Exception("Client not connected")
        
        url = f"{self.base_url}{endpoint}"
        headers = {"Content-Type": "application/json"}
        
        body = ""
        if data:
            body = json.dumps(data)
        
        if signed:
            timestamp = self._get_timestamp()
            signature = self._generate_signature(timestamp, method, endpoint, body)
            passphrase = self._generate_passphrase()
            
            headers.update({
                "KC-API-KEY": self.api_key,
                "KC-API-SIGN": signature,
                "KC-API-TIMESTAMP": timestamp,
                "KC-API-PASSPHRASE": passphrase,
                "KC-API-KEY-VERSION": "2"
            })
        
        try:
            async with self.session.request(
                method, url, params=params, data=body if body else None, headers=headers
            ) as response:
                result = await response.json()
                
                if response.status == 200 and result.get("code") == "200000":
                    return result.get("data", {})
                else:
                    error_msg = result.get("msg", f"HTTP {response.status}")
                    logger.error(f"KuCoin API error: {error_msg}")
                    raise Exception(f"API Error: {error_msg}")
                    
        except Exception as e:
            logger.error(f"Request failed: {e}")
            raise
    
    async def _test_connection(self):
        """اختبار الاتصال"""
        try:
            # Test public endpoint
            await self._make_request("GET", "/api/v1/timestamp")
            
            # Test private endpoint
            await self._make_request("GET", "/api/v1/accounts", signed=True)
            
            logger.info("✅ KuCoin API connection test successful")
            return True
        except Exception as e:
            logger.error(f"❌ KuCoin API connection test failed: {e}")
            raise
    
    async def get_ticker(self, symbol: str) -> Dict:
        """الحصول على إحصائيات الرمز"""
        return await self._make_request("GET", f"/api/v1/market/stats?symbol={symbol}")
    
    async def get_all_tickers(self) -> List[Dict]:
        """الحصول على جميع الإحصائيات"""
        result = await self._make_request("GET", "/api/v1/market/allTickers")
        return result.get("ticker", [])
    
    async def get_klines(self, symbol: str, kline_type: str = "1hour") -> List[List]:
        """الحصول على بيانات الشموع"""
        params = {"symbol": symbol, "type": kline_type}
        return await self._make_request("GET", "/api/v1/market/candles", params=params)
    
    async def get_account_info(self) -> List[Dict]:
        """الحصول على معلومات الحساب"""
        return await self._make_request("GET", "/api/v1/accounts", signed=True)
    
    async def get_balance(self, currency: str = None) -> Dict:
        """الحصول على الرصيد"""
        accounts = await self.get_account_info()
        
        if currency:
            for account in accounts:
                if account["currency"] == currency.upper() and account["type"] == "trade":
                    return {
                        "currency": account["currency"],
                        "balance": float(account["balance"]),
                        "available": float(account["available"]),
                        "holds": float(account["holds"])
                    }
            return {"currency": currency.upper(), "balance": 0.0, "available": 0.0, "holds": 0.0}
        
        balances = {}
        for account in accounts:
            if account["type"] == "trade" and float(account["balance"]) > 0:
                balances[account["currency"]] = {
                    "balance": float(account["balance"]),
                    "available": float(account["available"]),
                    "holds": float(account["holds"])
                }
        
        return balances
    
    async def place_order(self, symbol: str, side: str, order_type: str, size: float, price: float = None) -> Dict:
        """وضع أمر"""
        data = {
            "clientOid": str(uuid.uuid4()),
            "symbol": symbol.upper(),
            "side": side.lower(),
            "type": order_type.lower(),
        }
        
        if order_type.lower() == "market":
            if side.lower() == "buy":
                data["funds"] = str(size)  # USDT amount for market buy
            else:
                data["size"] = str(size)   # Crypto amount for market sell
        else:  # limit order
            data["size"] = str(size)
            data["price"] = str(price)
        
        try:
            result = await self._make_request("POST", "/api/v1/orders", data=data, signed=True)
            logger.info(f"✅ Order placed: {symbol} {side} - Order ID: {result.get('orderId')}")
            return result
        except Exception as e:
            logger.error(f"❌ Order failed: {symbol} {side} - Error: {e}")
            raise


class RealTradingPlatform:
    """منصة التداول الحقيقية"""

    def __init__(self):
        """تهيئة المنصة"""
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'kucoin_real_trading_2025'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")

        # Initialize KuCoin client
        self.kucoin = KuCoinAPI()

        # Initialize database
        self._init_database()

        # Setup routes and socket events
        self._setup_routes()
        self._setup_socket_events()

        # Platform state
        self.running = False
        self.background_tasks = {}

        # Trading data
        self.signals = []
        self.trades = []
        self.portfolio = {}
        self.performance = {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'win_rate': 0.0,
            'total_profit': 0.0,
            'total_fees': 0.0
        }

        # Trading configuration
        self.config = {
            'symbols': ['BTC-USDT', 'ETH-USDT', 'BNB-USDT'],
            'min_trade_amount': 10.0,  # Minimum $10
            'max_trade_amount': 50.0,  # Maximum $50 for safety
            'auto_trading_enabled': False,
            'safety_mode': True  # Safety first!
        }

        logger.info("Real Trading Platform initialized")

    def _init_database(self):
        """تهيئة قاعدة البيانات"""
        self.db_path = "kucoin_real_trading.db"
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS real_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    signal_type TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    current_price REAL NOT NULL,
                    target_price REAL,
                    stop_loss REAL,
                    risk_level TEXT,
                    reasoning TEXT,
                    volume_24h REAL,
                    price_change_24h REAL,
                    timestamp TEXT NOT NULL
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS real_trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT UNIQUE NOT NULL,
                    order_id TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    price REAL NOT NULL,
                    status TEXT NOT NULL,
                    fees REAL NOT NULL,
                    timestamp TEXT NOT NULL
                )
            ''')

            conn.commit()
            conn.close()
            logger.info("Database initialized")

        except Exception as e:
            logger.error(f"Database initialization failed: {e}")

    def _setup_routes(self):
        """إعداد مسارات API"""

        @self.app.route('/')
        def index():
            return self._render_main_page()

        @self.app.route('/api/test_kucoin', methods=['POST'])
        def test_kucoin():
            try:
                self.socketio.start_background_task(self._test_kucoin_async)
                return jsonify({'status': 'started', 'message': 'Testing KuCoin connection...'})
            except Exception as e:
                return jsonify({'status': 'error', 'message': str(e)})

        @self.app.route('/api/get_real_portfolio', methods=['POST'])
        def get_real_portfolio():
            try:
                self.socketio.start_background_task(self._update_portfolio_async)
                return jsonify({'status': 'started', 'message': 'Updating portfolio...'})
            except Exception as e:
                return jsonify({'status': 'error', 'message': str(e)})

        @self.app.route('/api/analyze_real_market', methods=['POST'])
        def analyze_real_market():
            try:
                self.socketio.start_background_task(self._analyze_market_async)
                return jsonify({'status': 'started', 'message': 'Analyzing real market...'})
            except Exception as e:
                return jsonify({'status': 'error', 'message': str(e)})

        @self.app.route('/api/start_real_trading', methods=['POST'])
        def start_real_trading():
            if self.config['safety_mode']:
                return jsonify({'status': 'error', 'message': 'Safety mode is ON. Disable it first!'})

            self.config['auto_trading_enabled'] = True
            return jsonify({'status': 'started', 'message': 'Real trading started'})

        @self.app.route('/api/stop_real_trading', methods=['POST'])
        def stop_real_trading():
            self.config['auto_trading_enabled'] = False
            return jsonify({'status': 'stopped', 'message': 'Real trading stopped'})

        @self.app.route('/api/toggle_safety', methods=['POST'])
        def toggle_safety():
            self.config['safety_mode'] = not self.config['safety_mode']
            status = 'enabled' if self.config['safety_mode'] else 'disabled'
            return jsonify({'status': status, 'message': f'Safety mode {status}'})

        @self.app.route('/api/signals')
        def get_signals():
            return jsonify({
                'signals': [asdict(signal) for signal in self.signals[-20:]],
                'count': len(self.signals)
            })

        @self.app.route('/api/trades')
        def get_trades():
            return jsonify({
                'trades': [asdict(trade) for trade in self.trades[-20:]],
                'count': len(self.trades)
            })

        @self.app.route('/api/portfolio')
        def get_portfolio():
            return jsonify(self.portfolio)

        @self.app.route('/api/performance')
        def get_performance():
            return jsonify(self.performance)

    def _setup_socket_events(self):
        """إعداد أحداث WebSocket"""

        @self.socketio.on('connect')
        def handle_connect():
            logger.info(f"Client connected: {request.sid}")
            emit('connected', {'status': 'connected', 'mode': 'REAL_KUCOIN_TRADING'})

        @self.socketio.on('disconnect')
        def handle_disconnect():
            logger.info(f"Client disconnected: {request.sid}")

    async def _test_kucoin_async(self):
        """اختبار KuCoin API"""
        try:
            self.socketio.emit('kucoin_status', {'status': 'testing', 'message': 'Testing KuCoin API...'})

            async with self.kucoin:
                # Test basic connection
                server_time = await self.kucoin._make_request("GET", "/api/v1/timestamp")

                # Test account access
                accounts = await self.kucoin.get_account_info()

                # Test market data
                btc_ticker = await self.kucoin.get_ticker("BTC-USDT")

                self.socketio.emit('kucoin_status', {
                    'status': 'success',
                    'message': 'KuCoin API connection successful!',
                    'data': {
                        'server_time': server_time,
                        'accounts_count': len(accounts),
                        'btc_price': btc_ticker.get('last', 'N/A')
                    }
                })

                logger.info("✅ KuCoin API test successful")

        except Exception as e:
            logger.error(f"❌ KuCoin API test failed: {e}")
            self.socketio.emit('kucoin_status', {
                'status': 'error',
                'message': f'KuCoin API test failed: {str(e)}'
            })

    async def _update_portfolio_async(self):
        """تحديث المحفظة الحقيقية"""
        try:
            self.socketio.emit('portfolio_status', {'status': 'updating', 'message': 'Updating real portfolio...'})

            async with self.kucoin:
                # Get real balances
                balances = await self.kucoin.get_balance()

                # Get current prices
                tickers = await self.kucoin.get_all_tickers()
                prices = {}
                for ticker in tickers:
                    if ticker['symbol'].endswith('-USDT'):
                        prices[ticker['symbol'].replace('-USDT', '')] = float(ticker['last'])

                # Calculate portfolio value
                total_value = 0.0
                portfolio_details = {}

                for currency, balance_info in balances.items():
                    if balance_info['balance'] > 0:
                        if currency == 'USDT':
                            value = balance_info['balance']
                        else:
                            price = prices.get(currency, 0)
                            value = balance_info['balance'] * price

                        total_value += value
                        portfolio_details[currency] = {
                            'balance': balance_info['balance'],
                            'available': balance_info['available'],
                            'holds': balance_info['holds'],
                            'price': prices.get(currency, 1.0 if currency == 'USDT' else 0),
                            'value': value
                        }

                self.portfolio = {
                    'total_value': total_value,
                    'balances': portfolio_details,
                    'last_updated': datetime.now().isoformat()
                }

                self.socketio.emit('portfolio_updated', {
                    'status': 'success',
                    'portfolio': self.portfolio
                })

                logger.info(f"✅ Portfolio updated - Total: ${total_value:.2f}")

        except Exception as e:
            logger.error(f"❌ Portfolio update failed: {e}")
            self.socketio.emit('portfolio_status', {
                'status': 'error',
                'message': f'Portfolio update failed: {str(e)}'
            })

    async def _analyze_market_async(self):
        """تحليل السوق الحقيقي"""
        try:
            self.socketio.emit('analysis_status', {'status': 'started', 'message': 'Analyzing real market...'})

            async with self.kucoin:
                for symbol in self.config['symbols']:
                    await self._analyze_symbol_async(symbol)
                    await asyncio.sleep(1)  # Rate limiting

            self.socketio.emit('analysis_status', {
                'status': 'completed',
                'message': f'Analysis completed for {len(self.config["symbols"])} symbols'
            })

        except Exception as e:
            logger.error(f"❌ Market analysis failed: {e}")
            self.socketio.emit('analysis_status', {
                'status': 'error',
                'message': f'Analysis failed: {str(e)}'
            })

    async def _analyze_symbol_async(self, symbol: str):
        """تحليل رمز معين"""
        try:
            async with self.kucoin:
                # Get real market data
                ticker = await self.kucoin.get_ticker(symbol)
                klines = await self.kucoin.get_klines(symbol, "1hour")

                if not ticker or not klines:
                    logger.warning(f"No data for {symbol}")
                    return

                # Extract market data
                current_price = float(ticker['last'])
                volume_24h = float(ticker['vol'])
                price_change_24h = float(ticker['changeRate']) * 100

                # Perform technical analysis
                signal = self._perform_technical_analysis(symbol, current_price, klines, volume_24h, price_change_24h)

                if signal:
                    self.signals.append(signal)
                    self._store_signal_in_db(signal)

                    # Execute trade if conditions are met
                    if (self.config['auto_trading_enabled'] and
                        not self.config['safety_mode'] and
                        signal.confidence >= 0.8):
                        await self._execute_trade_async(signal)

                    # Emit to clients
                    self.socketio.emit('analysis_complete', {
                        'symbol': symbol,
                        'signal': asdict(signal)
                    })

                    logger.info(f"Analysis completed for {symbol}: {signal.signal_type} ({signal.confidence:.1%})")

        except Exception as e:
            logger.error(f"Analysis failed for {symbol}: {e}")

    def _perform_technical_analysis(self, symbol: str, current_price: float, klines: List, volume_24h: float, price_change_24h: float) -> Optional[RealSignal]:
        """إجراء تحليل فني حقيقي"""
        try:
            if not klines or len(klines) < 10:
                return None

            # Convert to DataFrame
            df = pd.DataFrame(klines, columns=['timestamp', 'open', 'close', 'high', 'low', 'volume', 'turnover'])
            df = df.astype({'open': float, 'close': float, 'high': float, 'low': float, 'volume': float})
            df = df.sort_values('timestamp')

            # Calculate indicators
            df['sma_10'] = df['close'].rolling(window=10).mean()
            df['sma_20'] = df['close'].rolling(window=min(20, len(df))).mean()

            # RSI calculation
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))

            # Get latest values
            latest = df.iloc[-1]
            sma_10 = latest['sma_10']
            sma_20 = latest['sma_20']
            rsi = latest['rsi']

            # Generate signal
            signal_type = "HOLD"
            confidence = 0.5
            reasoning = "Technical analysis: "

            # Price vs Moving Averages
            if current_price > sma_10 > sma_20:
                signal_type = "BUY"
                confidence += 0.2
                reasoning += "Bullish trend, "
            elif current_price < sma_10 < sma_20:
                signal_type = "SELL"
                confidence += 0.2
                reasoning += "Bearish trend, "

            # RSI analysis
            if rsi < 30:
                if signal_type != "SELL":
                    signal_type = "BUY"
                confidence += 0.15
                reasoning += "RSI oversold, "
            elif rsi > 70:
                if signal_type != "BUY":
                    signal_type = "SELL"
                confidence += 0.15
                reasoning += "RSI overbought, "

            # Volume confirmation
            avg_volume = df['volume'].tail(10).mean()
            if volume_24h > avg_volume * 1.2:
                confidence += 0.1
                reasoning += "High volume, "

            # Price momentum
            if abs(price_change_24h) > 3:
                if price_change_24h > 0 and signal_type == "BUY":
                    confidence += 0.1
                elif price_change_24h < 0 and signal_type == "SELL":
                    confidence += 0.1
                reasoning += f"Strong momentum ({price_change_24h:+.1f}%), "

            # Risk assessment
            volatility = df['close'].pct_change().std() * 100
            if volatility > 4:
                risk_level = "HIGH"
                confidence *= 0.9
            elif volatility > 2:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"

            # Calculate targets
            target_price = None
            stop_loss = None

            if signal_type == "BUY":
                target_price = current_price * 1.02  # 2% profit target
                stop_loss = current_price * 0.99     # 1% stop loss
            elif signal_type == "SELL":
                target_price = current_price * 0.98  # 2% profit target
                stop_loss = current_price * 1.01     # 1% stop loss

            confidence = min(0.95, max(0.1, confidence))

            return RealSignal(
                symbol=symbol,
                signal_type=signal_type,
                confidence=confidence,
                current_price=current_price,
                target_price=target_price,
                stop_loss=stop_loss,
                risk_level=risk_level,
                reasoning=reasoning.rstrip(', '),
                volume_24h=volume_24h,
                price_change_24h=price_change_24h,
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Technical analysis failed for {symbol}: {e}")
            return None

    async def _execute_trade_async(self, signal: RealSignal):
        """تنفيذ صفقة حقيقية"""
        try:
            if signal.signal_type == 'HOLD' or self.config['safety_mode']:
                return

            # Calculate trade size
            trade_amount = self._calculate_trade_size(signal)
            if trade_amount < self.config['min_trade_amount']:
                logger.warning(f"Trade amount too small: ${trade_amount:.2f}")
                return

            async with self.kucoin:
                start_time = time.time()

                try:
                    if signal.signal_type == "BUY":
                        # Market buy order
                        result = await self.kucoin.place_order(
                            symbol=signal.symbol,
                            side="buy",
                            order_type="market",
                            size=trade_amount
                        )
                    else:  # SELL
                        # Get crypto balance
                        crypto_currency = signal.symbol.split('-')[0]
                        balance = await self.kucoin.get_balance(crypto_currency)

                        if balance['available'] < 0.001:
                            logger.warning(f"Insufficient {crypto_currency} balance")
                            return

                        # Market sell order
                        sell_amount = min(balance['available'], trade_amount / signal.current_price)
                        result = await self.kucoin.place_order(
                            symbol=signal.symbol,
                            side="sell",
                            order_type="market",
                            size=sell_amount
                        )

                    execution_time = (time.time() - start_time) * 1000

                    # Create trade record
                    trade = RealTrade(
                        trade_id=str(uuid.uuid4()),
                        order_id=result.get('orderId', ''),
                        symbol=signal.symbol,
                        side=signal.signal_type,
                        quantity=trade_amount,
                        price=signal.current_price,
                        status='FILLED',
                        fees=0.0,  # Will be updated later
                        timestamp=datetime.now().isoformat()
                    )

                    # Store trade
                    self.trades.append(trade)
                    self._store_trade_in_db(trade)

                    # Update performance
                    self.performance['total_trades'] += 1
                    self.performance['successful_trades'] += 1

                    # Emit to clients
                    self.socketio.emit('trade_executed', {
                        'trade': asdict(trade),
                        'performance': self.performance
                    })

                    logger.info(f"✅ Real trade executed: {signal.symbol} {signal.signal_type}")

                except Exception as e:
                    self.performance['failed_trades'] += 1
                    self.performance['total_trades'] += 1

                    logger.error(f"❌ Trade execution failed: {e}")

                    self.socketio.emit('trade_failed', {
                        'symbol': signal.symbol,
                        'side': signal.signal_type,
                        'error': str(e)
                    })

        except Exception as e:
            logger.error(f"Trade execution error: {e}")

    def _calculate_trade_size(self, signal: RealSignal) -> float:
        """حساب حجم الصفقة"""
        try:
            base_amount = self.config['max_trade_amount']
            confidence_factor = signal.confidence
            risk_factor = {'LOW': 1.0, 'MEDIUM': 0.8, 'HIGH': 0.6}.get(signal.risk_level, 0.8)

            trade_amount = base_amount * confidence_factor * risk_factor
            trade_amount = max(self.config['min_trade_amount'],
                             min(self.config['max_trade_amount'], trade_amount))

            return round(trade_amount, 2)

        except Exception as e:
            logger.error(f"Trade size calculation failed: {e}")
            return self.config['min_trade_amount']

    def _store_signal_in_db(self, signal: RealSignal):
        """حفظ الإشارة في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO real_signals
                (symbol, signal_type, confidence, current_price, target_price, stop_loss, risk_level, reasoning, volume_24h, price_change_24h, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                signal.symbol, signal.signal_type, signal.confidence, signal.current_price,
                signal.target_price, signal.stop_loss, signal.risk_level, signal.reasoning,
                signal.volume_24h, signal.price_change_24h, signal.timestamp
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error storing signal: {e}")

    def _store_trade_in_db(self, trade: RealTrade):
        """حفظ الصفقة في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO real_trades
                (trade_id, order_id, symbol, side, quantity, price, status, fees, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade.trade_id, trade.order_id, trade.symbol, trade.side, trade.quantity,
                trade.price, trade.status, trade.fees, trade.timestamp
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error storing trade: {e}")

    def start_background_tasks(self):
        """بدء المهام الخلفية"""
        self.running = True

        def market_monitor():
            """مراقب السوق"""
            while self.running:
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self._analyze_market_async())
                    loop.close()

                    time.sleep(300)  # 5 minutes
                except Exception as e:
                    logger.error(f"Market monitor error: {e}")
                    time.sleep(60)

        self.background_tasks['market_monitor'] = threading.Thread(target=market_monitor, daemon=True)
        self.background_tasks['market_monitor'].start()

        logger.info("Background tasks started")

    def stop_background_tasks(self):
        """إيقاف المهام الخلفية"""
        self.running = False
        for task in self.background_tasks.values():
            if task.is_alive():
                task.join(timeout=5)
        logger.info("Background tasks stopped")

    def _render_main_page(self):
        """عرض الصفحة الرئيسية"""
        html_template = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 منصة التداول الحقيقية - KuCoin Real Trading</title>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --primary: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --success: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            --warning: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            --danger: linear-gradient(135deg, #d63031 0%, #74b9ff 100%);
            --dark: #2d3436;
            --light: #ffffff;
            --border: rgba(255,255,255,0.2);
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            color: var(--light);
            min-height: 100vh;
        }

        .warning-banner {
            background: var(--danger);
            color: white;
            text-align: center;
            padding: 15px;
            font-weight: bold;
            font-size: 1.2em;
            animation: pulse-red 2s infinite;
        }

        @keyframes pulse-red {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .header {
            background: var(--primary);
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .safety-controls {
            background: rgba(214, 48, 49, 0.1);
            border: 2px solid #d63031;
            border-radius: 15px;
            padding: 20px;
            margin: 20px;
            text-align: center;
        }

        .safety-toggle {
            display: inline-flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 25px;
            border-radius: 25px;
            margin: 10px;
            cursor: pointer;
        }

        .toggle-switch {
            width: 60px;
            height: 30px;
            background: #d63031;
            border-radius: 15px;
            position: relative;
            transition: all 0.3s ease;
        }

        .toggle-switch.off {
            background: #00b894;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
        }

        .toggle-switch.off::after {
            transform: translateX(30px);
        }

        .main-container {
            padding: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .grid {
            display: grid;
            gap: 30px;
            margin-bottom: 40px;
        }

        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); }

        .card {
            background: rgba(255,255,255,0.05);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            border: 1px solid var(--border);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }

        .card-title {
            color: #ff6b6b;
            font-size: 1.5em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            margin: 10px 0;
        }

        .metric-value {
            font-weight: bold;
            font-size: 1.1em;
        }

        .btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-success { background: var(--success); }
        .btn-warning { background: var(--warning); }
        .btn-danger { background: var(--danger); }

        .table-container {
            overflow-x: auto;
            border-radius: 15px;
            background: rgba(0,0,0,0.3);
            max-height: 400px;
            overflow-y: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th, .table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid var(--border);
        }

        .table th {
            background: rgba(255, 107, 107, 0.2);
            font-weight: bold;
            color: #ff6b6b;
            position: sticky;
            top: 0;
        }

        .positive { color: #00b894; }
        .negative { color: #d63031; }
        .neutral { color: #fdcb6e; }

        .signal-buy {
            color: #00b894;
            background: rgba(0, 184, 148, 0.1);
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .signal-sell {
            color: #d63031;
            background: rgba(214, 48, 49, 0.1);
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .signal-hold {
            color: #fdcb6e;
            background: rgba(253, 203, 110, 0.1);
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            max-width: 350px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 30px;
            color: #ff6b6b;
        }

        .spinner {
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid #ff6b6b;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .grid-3 { grid-template-columns: 1fr; }
            .header h1 { font-size: 2em; }
        }
    </style>
</head>
<body>
    <!-- Notification -->
    <div id="notification" class="notification">
        <span id="notification-text"></span>
    </div>

    <!-- Warning Banner -->
    <div class="warning-banner">
        ⚠️ تحذير: هذه منصة تداول حقيقية - يتم تنفيذ صفقات حقيقية بأموال حقيقية! ⚠️
    </div>

    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-fire"></i> منصة التداول الحقيقية</h1>
        <p>KuCoin Real Trading Platform - تداول حقيقي مع إدارة مخاطر</p>
    </div>

    <!-- Safety Controls -->
    <div class="safety-controls">
        <h3><i class="fas fa-shield-alt"></i> إعدادات الأمان</h3>
        <div class="safety-toggle" onclick="toggleSafety()">
            <span>وضع الأمان</span>
            <div class="toggle-switch" id="safety-toggle"></div>
            <span id="safety-text">مفعل</span>
        </div>
        <p>⚠️ يجب إيقاف وضع الأمان لتفعيل التداول الآلي</p>
    </div>

    <div class="main-container">
        <!-- Control Panel -->
        <div class="card">
            <div class="card-title">
                <i class="fas fa-control"></i> لوحة التحكم
            </div>
            <button class="btn" onclick="testKuCoin()">
                <i class="fas fa-plug"></i> اختبار اتصال KuCoin
            </button>
            <button class="btn btn-success" onclick="updatePortfolio()">
                <i class="fas fa-wallet"></i> تحديث المحفظة الحقيقية
            </button>
            <button class="btn btn-warning" onclick="analyzeMarket()">
                <i class="fas fa-chart-line"></i> تحليل السوق الحقيقي
            </button>
            <button class="btn btn-danger" onclick="startRealTrading()" id="start-trading-btn" disabled>
                <i class="fas fa-play"></i> بدء التداول الحقيقي
            </button>
            <button class="btn" onclick="stopRealTrading()">
                <i class="fas fa-stop"></i> إيقاف التداول
            </button>
        </div>

        <!-- Real-time Data -->
        <div class="grid grid-3">
            <!-- Portfolio -->
            <div class="card">
                <div class="card-title">
                    <i class="fas fa-wallet"></i> المحفظة الحقيقية
                </div>
                <div class="metric">
                    <span>القيمة الإجمالية</span>
                    <span class="metric-value" id="total-value">$0.00</span>
                </div>
                <div class="metric">
                    <span>رصيد USDT</span>
                    <span class="metric-value" id="usdt-balance">0.00</span>
                </div>
                <div class="metric">
                    <span>رصيد BTC</span>
                    <span class="metric-value" id="btc-balance">0.00000000</span>
                </div>
                <div class="metric">
                    <span>رصيد ETH</span>
                    <span class="metric-value" id="eth-balance">0.00000000</span>
                </div>
            </div>

            <!-- Performance -->
            <div class="card">
                <div class="card-title">
                    <i class="fas fa-chart-bar"></i> أداء التداول
                </div>
                <div class="metric">
                    <span>إجمالي الصفقات</span>
                    <span class="metric-value" id="total-trades">0</span>
                </div>
                <div class="metric">
                    <span>الصفقات الناجحة</span>
                    <span class="metric-value positive" id="successful-trades">0</span>
                </div>
                <div class="metric">
                    <span>الصفقات الفاشلة</span>
                    <span class="metric-value negative" id="failed-trades">0</span>
                </div>
                <div class="metric">
                    <span>معدل النجاح</span>
                    <span class="metric-value" id="win-rate">0%</span>
                </div>
            </div>

            <!-- System Status -->
            <div class="card">
                <div class="card-title">
                    <i class="fas fa-server"></i> حالة النظام
                </div>
                <div class="metric">
                    <span>اتصال KuCoin</span>
                    <span class="metric-value neutral" id="kucoin-status">غير متصل</span>
                </div>
                <div class="metric">
                    <span>التداول الآلي</span>
                    <span class="metric-value negative" id="trading-status">متوقف</span>
                </div>
                <div class="metric">
                    <span>وضع الأمان</span>
                    <span class="metric-value positive" id="safety-status">مفعل</span>
                </div>
                <div class="metric">
                    <span>آخر تحديث</span>
                    <span class="metric-value" id="last-update">لم يتم بعد</span>
                </div>
            </div>
        </div>

        <!-- Signals Table -->
        <div class="card">
            <div class="card-title">
                <i class="fas fa-brain"></i> إشارات التداول الحقيقية
            </div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>العملة</th>
                            <th>الإشارة</th>
                            <th>الثقة</th>
                            <th>السعر</th>
                            <th>الهدف</th>
                            <th>وقف الخسارة</th>
                            <th>المخاطر</th>
                            <th>السبب</th>
                            <th>الوقت</th>
                        </tr>
                    </thead>
                    <tbody id="signals-table">
                        <tr>
                            <td colspan="9" class="loading">
                                <div class="spinner"></div>
                                لا توجد إشارات متاحة
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Trades Table -->
        <div class="card">
            <div class="card-title">
                <i class="fas fa-exchange-alt"></i> الصفقات المنفذة
            </div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>معرف الصفقة</th>
                            <th>معرف الأمر</th>
                            <th>العملة</th>
                            <th>النوع</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الحالة</th>
                            <th>الرسوم</th>
                            <th>الوقت</th>
                        </tr>
                    </thead>
                    <tbody id="trades-table">
                        <tr>
                            <td colspan="9" class="loading">
                                <div class="spinner"></div>
                                لا توجد صفقات منفذة
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Real Trading Platform JavaScript
        let socket;
        let isConnected = false;
        let safetyMode = true;

        // Initialize platform
        function initializePlatform() {
            initializeSocket();
            loadData();
            updateSafetyUI();
        }

        // Initialize Socket.IO
        function initializeSocket() {
            socket = io();

            socket.on('connect', function() {
                isConnected = true;
                showNotification('متصل بمنصة التداول الحقيقية! 🔥', 'success');
            });

            socket.on('disconnect', function() {
                isConnected = false;
                showNotification('انقطع الاتصال', 'error');
            });

            socket.on('kucoin_status', function(data) {
                if (data.status === 'success') {
                    document.getElementById('kucoin-status').textContent = 'متصل';
                    document.getElementById('kucoin-status').className = 'metric-value positive';
                    showNotification('تم الاتصال بـ KuCoin بنجاح!', 'success');
                } else if (data.status === 'error') {
                    document.getElementById('kucoin-status').textContent = 'خطأ';
                    document.getElementById('kucoin-status').className = 'metric-value negative';
                    showNotification('فشل الاتصال بـ KuCoin: ' + data.message, 'error');
                }
            });

            socket.on('portfolio_updated', function(data) {
                updatePortfolioDisplay(data.portfolio);
                showNotification('تم تحديث المحفظة الحقيقية', 'success');
            });

            socket.on('analysis_complete', function(data) {
                updateSignalsTable([data.signal]);
                showNotification(`تم تحليل ${data.symbol}: ${data.signal.signal_type}`, 'success');
            });

            socket.on('trade_executed', function(data) {
                updateTradesTable([data.trade]);
                updatePerformanceDisplay(data.performance);
                showNotification(`تم تنفيذ صفقة حقيقية: ${data.trade.symbol}`, 'success');
            });
        }

        // Action functions
        function testKuCoin() {
            if (!isConnected) return;
            fetch('/api/test_kucoin', {method: 'POST'})
                .then(r => r.json())
                .then(data => showNotification('جاري اختبار KuCoin...', 'info'))
                .catch(e => showNotification('خطأ في الاختبار', 'error'));
        }

        function updatePortfolio() {
            if (!isConnected) return;
            fetch('/api/get_real_portfolio', {method: 'POST'})
                .then(r => r.json())
                .then(data => showNotification('جاري تحديث المحفظة...', 'info'))
                .catch(e => showNotification('خطأ في التحديث', 'error'));
        }

        function analyzeMarket() {
            if (!isConnected) return;
            fetch('/api/analyze_real_market', {method: 'POST'})
                .then(r => r.json())
                .then(data => showNotification('جاري تحليل السوق الحقيقي...', 'info'))
                .catch(e => showNotification('خطأ في التحليل', 'error'));
        }

        function startRealTrading() {
            if (safetyMode) {
                showNotification('يجب إيقاف وضع الأمان أولاً!', 'error');
                return;
            }

            if (confirm('هل أنت متأكد من بدء التداول الحقيقي؟ سيتم تنفيذ صفقات بأموال حقيقية!')) {
                fetch('/api/start_real_trading', {method: 'POST'})
                    .then(r => r.json())
                    .then(data => {
                        if (data.status === 'started') {
                            document.getElementById('trading-status').textContent = 'نشط';
                            document.getElementById('trading-status').className = 'metric-value positive';
                            showNotification('تم بدء التداول الحقيقي!', 'success');
                        } else {
                            showNotification(data.message, 'error');
                        }
                    })
                    .catch(e => showNotification('خطأ في بدء التداول', 'error'));
            }
        }

        function stopRealTrading() {
            fetch('/api/stop_real_trading', {method: 'POST'})
                .then(r => r.json())
                .then(data => {
                    document.getElementById('trading-status').textContent = 'متوقف';
                    document.getElementById('trading-status').className = 'metric-value negative';
                    showNotification('تم إيقاف التداول', 'warning');
                })
                .catch(e => showNotification('خطأ في إيقاف التداول', 'error'));
        }

        function toggleSafety() {
            fetch('/api/toggle_safety', {method: 'POST'})
                .then(r => r.json())
                .then(data => {
                    safetyMode = data.status === 'enabled';
                    updateSafetyUI();
                    showNotification(`وضع الأمان ${data.status === 'enabled' ? 'مفعل' : 'معطل'}`,
                                   data.status === 'enabled' ? 'success' : 'warning');
                })
                .catch(e => showNotification('خطأ في تغيير وضع الأمان', 'error'));
        }

        function updateSafetyUI() {
            const toggle = document.getElementById('safety-toggle');
            const text = document.getElementById('safety-text');
            const startBtn = document.getElementById('start-trading-btn');
            const status = document.getElementById('safety-status');

            if (safetyMode) {
                toggle.classList.remove('off');
                text.textContent = 'مفعل';
                startBtn.disabled = true;
                status.textContent = 'مفعل';
                status.className = 'metric-value positive';
            } else {
                toggle.classList.add('off');
                text.textContent = 'معطل';
                startBtn.disabled = false;
                status.textContent = 'معطل';
                status.className = 'metric-value negative';
            }
        }

        function loadData() {
            // Load signals
            fetch('/api/signals').then(r => r.json()).then(data => {
                if (data.signals && data.signals.length > 0) {
                    updateSignalsTable(data.signals);
                }
            });

            // Load trades
            fetch('/api/trades').then(r => r.json()).then(data => {
                if (data.trades && data.trades.length > 0) {
                    updateTradesTable(data.trades);
                }
            });

            // Load portfolio
            fetch('/api/portfolio').then(r => r.json()).then(data => {
                updatePortfolioDisplay(data);
            });

            // Load performance
            fetch('/api/performance').then(r => r.json()).then(data => {
                updatePerformanceDisplay(data);
            });
        }

        function updatePortfolioDisplay(portfolio) {
            if (portfolio && portfolio.total_value !== undefined) {
                document.getElementById('total-value').textContent = '$' + portfolio.total_value.toFixed(2);

                if (portfolio.balances) {
                    document.getElementById('usdt-balance').textContent =
                        (portfolio.balances.USDT?.balance || 0).toFixed(2);
                    document.getElementById('btc-balance').textContent =
                        (portfolio.balances.BTC?.balance || 0).toFixed(8);
                    document.getElementById('eth-balance').textContent =
                        (portfolio.balances.ETH?.balance || 0).toFixed(8);
                }

                document.getElementById('last-update').textContent =
                    new Date().toLocaleTimeString('ar');
            }
        }

        function updatePerformanceDisplay(performance) {
            if (performance) {
                document.getElementById('total-trades').textContent = performance.total_trades || 0;
                document.getElementById('successful-trades').textContent = performance.successful_trades || 0;
                document.getElementById('failed-trades').textContent = performance.failed_trades || 0;

                const winRate = performance.total_trades > 0 ?
                    (performance.successful_trades / performance.total_trades * 100).toFixed(1) : 0;
                document.getElementById('win-rate').textContent = winRate + '%';
            }
        }

        function updateSignalsTable(signals) {
            const tbody = document.getElementById('signals-table');
            if (!signals || signals.length === 0) return;

            tbody.innerHTML = signals.slice(-10).reverse().map(signal => `
                <tr>
                    <td>${signal.symbol}</td>
                    <td><span class="signal-${signal.signal_type.toLowerCase()}">${signal.signal_type}</span></td>
                    <td>${(signal.confidence * 100).toFixed(1)}%</td>
                    <td>$${signal.current_price.toFixed(2)}</td>
                    <td>${signal.target_price ? '$' + signal.target_price.toFixed(2) : '-'}</td>
                    <td>${signal.stop_loss ? '$' + signal.stop_loss.toFixed(2) : '-'}</td>
                    <td>${signal.risk_level}</td>
                    <td>${signal.reasoning.substring(0, 30)}...</td>
                    <td>${new Date(signal.timestamp).toLocaleTimeString('ar')}</td>
                </tr>
            `).join('');
        }

        function updateTradesTable(trades) {
            const tbody = document.getElementById('trades-table');
            if (!trades || trades.length === 0) return;

            tbody.innerHTML = trades.slice(-10).reverse().map(trade => `
                <tr>
                    <td>${trade.trade_id.substring(0, 8)}...</td>
                    <td>${trade.order_id.substring(0, 8)}...</td>
                    <td>${trade.symbol}</td>
                    <td><span class="signal-${trade.side.toLowerCase()}">${trade.side}</span></td>
                    <td>${trade.quantity.toFixed(6)}</td>
                    <td>$${trade.price.toFixed(2)}</td>
                    <td>${trade.status}</td>
                    <td>$${trade.fees.toFixed(4)}</td>
                    <td>${new Date(trade.timestamp).toLocaleTimeString('ar')}</td>
                </tr>
            `).join('');
        }

        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const text = document.getElementById('notification-text');

            text.textContent = message;
            notification.className = `notification show ${type}`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 4000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializePlatform();
        });
    </script>
</body>
</html>
        """
        return render_template_string(html_template)

    def run(self, host='0.0.0.0', port=5001, debug=False):
        """تشغيل المنصة"""
        print(f"""
        ╔══════════════════════════════════════════════════════════════╗
        ║                                                              ║
        ║        🔥 منصة التداول الحقيقية مع KuCoin API              ║
        ║           Real KuCoin Trading Platform                       ║
        ║                                                              ║
        ╚══════════════════════════════════════════════════════════════╝

        🌐 رابط المنصة الحقيقية: http://{host}:{port}
        🔥 بدء تشغيل التداول الحقيقي...

        ⚠️  تحذير مهم:
        🚨 هذه منصة تداول حقيقية تتصل بـ KuCoin API
        💰 سيتم تنفيذ صفقات حقيقية بأموال حقيقية
        🛡️ تأكد من تفعيل وضع الأمان قبل البدء
        📊 ابدأ بمبالغ صغيرة للاختبار

        ✅ الميزات الحقيقية:
        🔗 اتصال مباشر مع KuCoin API
        💹 تنفيذ صفقات حقيقية
        📈 بيانات السوق الحقيقية
        🤖 تحليل فني متقدم
        🛡️ إدارة مخاطر فعلية
        ⚡ تحديثات فورية
        """)

        # Start background tasks
        self.start_background_tasks()

        try:
            self.socketio.run(self.app, host=host, port=port, debug=debug)
        finally:
            self.stop_background_tasks()


def main():
    """الدالة الرئيسية"""
    try:
        platform = RealTradingPlatform()
        platform.run(host='0.0.0.0', port=5001, debug=False)

    except KeyboardInterrupt:
        print("\n👋 تم إيقاف منصة التداول الحقيقية")
    except Exception as e:
        print(f"❌ خطأ في تشغيل المنصة: {e}")
        print("\n💡 لتثبيت المتطلبات:")
        print("   pip install flask flask-socketio aiohttp pandas numpy")


if __name__ == "__main__":
    main()
