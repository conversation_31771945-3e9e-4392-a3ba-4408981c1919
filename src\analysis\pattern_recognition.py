"""
Pattern Recognition for AI Crypto Trading Bot
Advanced candlestick and chart pattern detection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from scipy.signal import find_peaks, find_peaks_cwt
from scipy.stats import linregress

from ..core.logger import logger


@dataclass
class PatternResult:
    """Result structure for pattern recognition"""
    pattern_name: str
    pattern_type: str  # 'BULLISH', 'BEARISH', 'NEUTRAL'
    confidence: float  # 0-1
    start_index: int
    end_index: int
    description: str
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    
    def to_dict(self) -> Dict:
        return {
            'pattern_name': self.pattern_name,
            'pattern_type': self.pattern_type,
            'confidence': self.confidence,
            'start_index': self.start_index,
            'end_index': self.end_index,
            'description': self.description,
            'target_price': self.target_price,
            'stop_loss': self.stop_loss
        }


class PatternRecognition:
    """
    Advanced pattern recognition for technical analysis
    """
    
    def __init__(self):
        self.patterns_cache = {}
        logger.info("Pattern Recognition system initialized")
    
    # Candlestick Patterns
    def is_doji(self, open_price: float, high: float, low: float, close: float, 
                body_threshold: float = 0.1) -> bool:
        """Detect Doji candlestick pattern"""
        body_size = abs(close - open_price)
        total_range = high - low
        
        if total_range == 0:
            return False
        
        body_ratio = body_size / total_range
        return body_ratio <= body_threshold
    
    def is_hammer(self, open_price: float, high: float, low: float, close: float) -> bool:
        """Detect Hammer candlestick pattern"""
        body_size = abs(close - open_price)
        lower_shadow = min(open_price, close) - low
        upper_shadow = high - max(open_price, close)
        total_range = high - low
        
        if total_range == 0:
            return False
        
        # Hammer criteria
        return (lower_shadow >= 2 * body_size and 
                upper_shadow <= body_size * 0.1 and
                body_size / total_range <= 0.3)
    
    def is_shooting_star(self, open_price: float, high: float, low: float, close: float) -> bool:
        """Detect Shooting Star candlestick pattern"""
        body_size = abs(close - open_price)
        lower_shadow = min(open_price, close) - low
        upper_shadow = high - max(open_price, close)
        total_range = high - low
        
        if total_range == 0:
            return False
        
        # Shooting Star criteria
        return (upper_shadow >= 2 * body_size and 
                lower_shadow <= body_size * 0.1 and
                body_size / total_range <= 0.3)
    
    def is_engulfing_bullish(self, prev_open: float, prev_close: float, 
                           curr_open: float, curr_close: float) -> bool:
        """Detect Bullish Engulfing pattern"""
        # Previous candle is bearish
        prev_bearish = prev_close < prev_open
        # Current candle is bullish
        curr_bullish = curr_close > curr_open
        # Current candle engulfs previous
        engulfs = curr_open < prev_close and curr_close > prev_open
        
        return prev_bearish and curr_bullish and engulfs
    
    def is_engulfing_bearish(self, prev_open: float, prev_close: float, 
                           curr_open: float, curr_close: float) -> bool:
        """Detect Bearish Engulfing pattern"""
        # Previous candle is bullish
        prev_bullish = prev_close > prev_open
        # Current candle is bearish
        curr_bearish = curr_close < curr_open
        # Current candle engulfs previous
        engulfs = curr_open > prev_close and curr_close < prev_open
        
        return prev_bullish and curr_bearish and engulfs
    
    def detect_candlestick_patterns(self, df: pd.DataFrame) -> List[PatternResult]:
        """Detect various candlestick patterns"""
        patterns = []
        
        try:
            for i in range(1, len(df)):
                current = df.iloc[i]
                previous = df.iloc[i-1] if i > 0 else None
                
                # Single candle patterns
                if self.is_doji(current['open'], current['high'], current['low'], current['close']):
                    patterns.append(PatternResult(
                        pattern_name='Doji',
                        pattern_type='NEUTRAL',
                        confidence=0.7,
                        start_index=i,
                        end_index=i,
                        description='Indecision pattern - potential reversal'
                    ))
                
                if self.is_hammer(current['open'], current['high'], current['low'], current['close']):
                    patterns.append(PatternResult(
                        pattern_name='Hammer',
                        pattern_type='BULLISH',
                        confidence=0.8,
                        start_index=i,
                        end_index=i,
                        description='Bullish reversal pattern at support'
                    ))
                
                if self.is_shooting_star(current['open'], current['high'], current['low'], current['close']):
                    patterns.append(PatternResult(
                        pattern_name='Shooting Star',
                        pattern_type='BEARISH',
                        confidence=0.8,
                        start_index=i,
                        end_index=i,
                        description='Bearish reversal pattern at resistance'
                    ))
                
                # Two candle patterns
                if previous is not None:
                    if self.is_engulfing_bullish(previous['open'], previous['close'], 
                                               current['open'], current['close']):
                        patterns.append(PatternResult(
                            pattern_name='Bullish Engulfing',
                            pattern_type='BULLISH',
                            confidence=0.85,
                            start_index=i-1,
                            end_index=i,
                            description='Strong bullish reversal pattern'
                        ))
                    
                    if self.is_engulfing_bearish(previous['open'], previous['close'], 
                                               current['open'], current['close']):
                        patterns.append(PatternResult(
                            pattern_name='Bearish Engulfing',
                            pattern_type='BEARISH',
                            confidence=0.85,
                            start_index=i-1,
                            end_index=i,
                            description='Strong bearish reversal pattern'
                        ))
            
            logger.info(f"Detected {len(patterns)} candlestick patterns")
            
        except Exception as e:
            logger.error(f"Error detecting candlestick patterns: {e}")
        
        return patterns
    
    # Chart Patterns
    def detect_support_resistance(self, df: pd.DataFrame, window: int = 20, 
                                 min_touches: int = 2) -> Dict[str, List[float]]:
        """Detect support and resistance levels"""
        try:
            highs = df['high'].values
            lows = df['low'].values
            
            # Find peaks and troughs
            peak_indices, _ = find_peaks(highs, distance=window//2)
            trough_indices, _ = find_peaks(-lows, distance=window//2)
            
            # Get resistance levels (peaks)
            resistance_levels = []
            for peak_idx in peak_indices:
                level = highs[peak_idx]
                # Count how many times price touched this level
                touches = np.sum(np.abs(highs - level) / level < 0.01)
                if touches >= min_touches:
                    resistance_levels.append(level)
            
            # Get support levels (troughs)
            support_levels = []
            for trough_idx in trough_indices:
                level = lows[trough_idx]
                # Count how many times price touched this level
                touches = np.sum(np.abs(lows - level) / level < 0.01)
                if touches >= min_touches:
                    support_levels.append(level)
            
            return {
                'support': sorted(list(set(support_levels))),
                'resistance': sorted(list(set(resistance_levels)), reverse=True)
            }
            
        except Exception as e:
            logger.error(f"Error detecting support/resistance: {e}")
            return {'support': [], 'resistance': []}
    
    def detect_trend_lines(self, df: pd.DataFrame, window: int = 20) -> Dict[str, Dict]:
        """Detect trend lines"""
        try:
            prices = df['close'].values
            x = np.arange(len(prices))
            
            # Overall trend
            slope, intercept, r_value, _, _ = linregress(x, prices)
            
            # Recent trend (last window periods)
            if len(prices) >= window:
                recent_x = x[-window:]
                recent_prices = prices[-window:]
                recent_slope, recent_intercept, recent_r_value, _, _ = linregress(recent_x, recent_prices)
            else:
                recent_slope, recent_intercept, recent_r_value = slope, intercept, r_value
            
            return {
                'overall_trend': {
                    'slope': slope,
                    'intercept': intercept,
                    'r_squared': r_value**2,
                    'direction': 'BULLISH' if slope > 0 else 'BEARISH' if slope < 0 else 'NEUTRAL'
                },
                'recent_trend': {
                    'slope': recent_slope,
                    'intercept': recent_intercept,
                    'r_squared': recent_r_value**2,
                    'direction': 'BULLISH' if recent_slope > 0 else 'BEARISH' if recent_slope < 0 else 'NEUTRAL'
                }
            }
            
        except Exception as e:
            logger.error(f"Error detecting trend lines: {e}")
            return {'overall_trend': {}, 'recent_trend': {}}
    
    def detect_triangle_pattern(self, df: pd.DataFrame, window: int = 20) -> Optional[PatternResult]:
        """Detect triangle patterns (ascending, descending, symmetrical)"""
        try:
            if len(df) < window:
                return None
            
            recent_df = df.tail(window)
            highs = recent_df['high'].values
            lows = recent_df['low'].values
            
            # Find peaks and troughs
            peak_indices, _ = find_peaks(highs, distance=3)
            trough_indices, _ = find_peaks(-lows, distance=3)
            
            if len(peak_indices) < 2 or len(trough_indices) < 2:
                return None
            
            # Get trend lines for highs and lows
            peak_x = peak_indices
            peak_y = highs[peak_indices]
            trough_x = trough_indices
            trough_y = lows[trough_indices]
            
            # Calculate slopes
            if len(peak_x) >= 2:
                high_slope, _, high_r, _, _ = linregress(peak_x, peak_y)
            else:
                return None
                
            if len(trough_x) >= 2:
                low_slope, _, low_r, _, _ = linregress(trough_x, trough_y)
            else:
                return None
            
            # Determine triangle type
            if abs(high_r) > 0.7 and abs(low_r) > 0.7:  # Strong correlation
                if high_slope < -0.001 and abs(low_slope) < 0.001:
                    pattern_type = 'BEARISH'
                    pattern_name = 'Descending Triangle'
                elif abs(high_slope) < 0.001 and low_slope > 0.001:
                    pattern_type = 'BULLISH'
                    pattern_name = 'Ascending Triangle'
                elif high_slope < -0.001 and low_slope > 0.001:
                    pattern_type = 'NEUTRAL'
                    pattern_name = 'Symmetrical Triangle'
                else:
                    return None
                
                confidence = min(abs(high_r), abs(low_r))
                
                return PatternResult(
                    pattern_name=pattern_name,
                    pattern_type=pattern_type,
                    confidence=confidence,
                    start_index=len(df) - window,
                    end_index=len(df) - 1,
                    description=f'{pattern_name} pattern detected with {confidence:.2f} confidence'
                )
            
        except Exception as e:
            logger.error(f"Error detecting triangle pattern: {e}")
        
        return None
    
    def detect_head_and_shoulders(self, df: pd.DataFrame, window: int = 30) -> Optional[PatternResult]:
        """Detect Head and Shoulders pattern"""
        try:
            if len(df) < window:
                return None
            
            recent_df = df.tail(window)
            highs = recent_df['high'].values
            
            # Find peaks
            peak_indices, properties = find_peaks(highs, distance=window//6, prominence=np.std(highs)*0.5)
            
            if len(peak_indices) < 3:
                return None
            
            # Take the three highest peaks
            peak_heights = highs[peak_indices]
            sorted_indices = np.argsort(peak_heights)[-3:]
            main_peaks = peak_indices[sorted_indices]
            main_peaks = np.sort(main_peaks)  # Sort by time
            
            if len(main_peaks) == 3:
                left_shoulder = highs[main_peaks[0]]
                head = highs[main_peaks[1]]
                right_shoulder = highs[main_peaks[2]]
                
                # Head and Shoulders criteria
                head_higher = head > left_shoulder and head > right_shoulder
                shoulders_similar = abs(left_shoulder - right_shoulder) / max(left_shoulder, right_shoulder) < 0.05
                
                if head_higher and shoulders_similar:
                    confidence = 0.8 if shoulders_similar else 0.6
                    
                    return PatternResult(
                        pattern_name='Head and Shoulders',
                        pattern_type='BEARISH',
                        confidence=confidence,
                        start_index=len(df) - window + main_peaks[0],
                        end_index=len(df) - window + main_peaks[2],
                        description='Bearish reversal pattern - Head and Shoulders'
                    )
            
        except Exception as e:
            logger.error(f"Error detecting Head and Shoulders: {e}")
        
        return None
    
    def analyze_all_patterns(self, df: pd.DataFrame) -> Dict[str, any]:
        """Comprehensive pattern analysis"""
        try:
            results = {
                'candlestick_patterns': self.detect_candlestick_patterns(df),
                'support_resistance': self.detect_support_resistance(df),
                'trend_lines': self.detect_trend_lines(df),
                'triangle_pattern': self.detect_triangle_pattern(df),
                'head_shoulders': self.detect_head_and_shoulders(df),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            # Count patterns by type
            pattern_summary = {'BULLISH': 0, 'BEARISH': 0, 'NEUTRAL': 0}
            for pattern in results['candlestick_patterns']:
                pattern_summary[pattern.pattern_type] += 1
            
            if results['triangle_pattern']:
                pattern_summary[results['triangle_pattern'].pattern_type] += 1
            
            if results['head_shoulders']:
                pattern_summary[results['head_shoulders'].pattern_type] += 1
            
            results['pattern_summary'] = pattern_summary
            
            logger.info(f"Pattern analysis complete: {pattern_summary}")
            return results
            
        except Exception as e:
            logger.error(f"Error in comprehensive pattern analysis: {e}")
            return {}
