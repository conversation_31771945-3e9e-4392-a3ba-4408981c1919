#!/usr/bin/env python3
"""
Advanced AI Crypto Trading Bot Dashboard - Enhanced Version
"""

import asyncio
import sys
from pathlib import Path
import json
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import uuid
import threading
import time

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Web framework
try:
    from flask import Flask, render_template, jsonify, request, send_file
    from flask_socketio import SocketIO, emit
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

from src.data.kucoin_client import KuCoinClient
from src.analysis import MarketAnalyzer
from src.ai.feature_engineer import FeatureEngineer
from src.trading.order_manager import OrderManager
from src.core.logger import logger


class AdvancedTradingDashboard:
    """
    Advanced AI Crypto Trading Bot Dashboard with enhanced features
    """

    def __init__(self):
        if not FLASK_AVAILABLE:
            print("❌ Flask not available. Install with: pip install flask flask-socketio plotly")
            sys.exit(1)

        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'advanced_crypto_trading_bot_secret'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")

        # Trading components
        self.kucoin_client = None
        self.market_analyzer = MarketAnalyzer()
        self.feature_engineer = FeatureEngineer()
        self.order_manager = None

        # Data storage
        self.portfolio_symbols = ['BTC-USDT', 'ETH-USDT', 'PIXEL-USDT', 'HIGH-USDT', 'STG-USDT', 'CELR-USDT', 'VOXEL-USDT', 'PORTAL-USDT', 'LTO-USDT']
        self.market_data = {}
        self.analysis_results = {}
        self.portfolio_data = {}
        self.recommendations = {}
        self.trade_history = []
        self.performance_metrics = {}
        self.alerts = []

        # Background tasks
        self.update_thread = None
        self.running = False

        # Setup routes and socketio
        self._setup_routes()
        self._setup_socketio()

        logger.info("Advanced Trading Dashboard initialized")

    def _setup_routes(self):
        """Setup Flask routes"""

        @self.app.route('/')
        def index():
            return self._render_advanced_dashboard()

        @self.app.route('/api/portfolio')
        def get_portfolio():
            return jsonify(self.portfolio_data)

        @self.app.route('/api/market_data')
        def get_all_market_data():
            return jsonify(self.market_data)

        @self.app.route('/api/market_data/<symbol>')
        def get_market_data(symbol):
            return jsonify(self.market_data.get(symbol, {}))

        @self.app.route('/api/analysis')
        def get_all_analysis():
            return jsonify(self.analysis_results)

        @self.app.route('/api/analysis/<symbol>')
        def get_analysis(symbol):
            return jsonify(self.analysis_results.get(symbol, {}))

        @self.app.route('/api/recommendations')
        def get_recommendations():
            return jsonify(self.recommendations)

        @self.app.route('/api/trade_history')
        def get_trade_history():
            return jsonify(self.trade_history[-100:])  # Last 100 trades

        @self.app.route('/api/performance')
        def get_performance():
            return jsonify(self.performance_metrics)

        @self.app.route('/api/alerts')
        def get_alerts():
            return jsonify(self.alerts[-50:])  # Last 50 alerts

        @self.app.route('/api/export/<format>')
        def export_data(format):
            return self._export_data(format)

    def _setup_socketio(self):
        """Setup SocketIO events"""

        @self.socketio.on('connect')
        def handle_connect():
            print(f"Client connected: {request.sid}")
            emit('status', {'message': 'Connected to Advanced Trading Bot'})
            # Send initial data
            emit('portfolio_update', self.portfolio_data)
            emit('market_update', self.market_data)
            emit('analysis_update', self.analysis_results)

        @self.socketio.on('disconnect')
        def handle_disconnect():
            print(f"Client disconnected: {request.sid}")

        @self.socketio.on('request_analysis')
        def handle_analysis_request(data):
            symbol = data.get('symbol', 'BTC-USDT')
            asyncio.create_task(self._analyze_symbol_async(symbol))

        @self.socketio.on('request_portfolio_update')
        def handle_portfolio_update():
            asyncio.create_task(self._update_portfolio_async())

        @self.socketio.on('request_recommendations')
        def handle_recommendations_request():
            asyncio.create_task(self._generate_recommendations_async())

        @self.socketio.on('set_alert')
        def handle_set_alert(data):
            self._add_alert(data)

    def _render_advanced_dashboard(self):
        """Render the advanced dashboard HTML"""
        html_template = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 لوحة التحكم المتقدمة - بوت التداول الذكي</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0e27 0%, #1a1a2e 50%, #16213e 100%);
            color: #fff;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.2em; opacity: 0.9; }

        /* Navigation */
        .nav-tabs {
            display: flex;
            justify-content: center;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            margin: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .nav-tab {
            padding: 12px 24px;
            margin: 0 5px;
            background: transparent;
            border: 2px solid rgba(255,255,255,0.2);
            color: #fff;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .nav-tab.active, .nav-tab:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            transform: translateY(-2px);
        }

        /* Container */
        .container { max-width: 1600px; margin: 0 auto; padding: 20px; }

        /* Grid layouts */
        .grid { display: grid; gap: 20px; margin-bottom: 30px; }
        .grid-2 { grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .grid-4 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }

        /* Cards */
        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .card h3 {
            color: #64b5f6;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Status indicators */
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        .status-online { background: #4caf50; }
        .status-offline { background: #f44336; }
        .status-warning { background: #ff9800; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Metrics */
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            transition: background 0.3s ease;
        }
        .metric:hover { background: rgba(255,255,255,0.1); }
        .metric-value { font-weight: bold; }
        .metric-positive { color: #4caf50; }
        .metric-negative { color: #f44336; }
        .metric-neutral { color: #ff9800; }

        /* Signals */
        .signal-buy { color: #4caf50; font-weight: bold; }
        .signal-sell { color: #f44336; font-weight: bold; }
        .signal-hold { color: #ff9800; font-weight: bold; }

        /* Buttons */
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .btn-success { background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); }
        .btn-danger { background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%); }
        .btn-warning { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }

        /* Tables */
        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            background: rgba(255,255,255,0.05);
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        .table th, .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .table th {
            background: rgba(255,255,255,0.1);
            font-weight: bold;
            color: #64b5f6;
        }
        .table tr:hover {
            background: rgba(255,255,255,0.05);
        }

        /* Charts */
        .chart-container {
            height: 300px;
            margin: 20px 0;
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            padding: 10px;
        }

        /* Log */
        .log {
            background: #1a1a2e;
            border-radius: 8px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
            border: 1px solid rgba(255,255,255,0.1);
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-right: 3px solid #64b5f6;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* Tab content */
        .tab-content { display: none; }
        .tab-content.active { display: block; }

        /* Responsive */
        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4 { grid-template-columns: 1fr; }
            .nav-tabs { flex-direction: column; }
            .header h1 { font-size: 2em; }
        }

        /* Dark/Light mode toggle */
        .theme-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.1);
            border: none;
            color: #fff;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        .notification.show {
            transform: translateX(0);
        }

        /* Loading spinner */
        .spinner {
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 10px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 20px;
            color: #888;
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <!-- Theme Toggle -->
    <button class="theme-toggle" onclick="toggleTheme()">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Notification -->
    <div id="notification" class="notification">
        <span id="notification-text"></span>
    </div>

    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-robot"></i> لوحة التحكم المتقدمة - بوت التداول الذكي</h1>
        <p>نظام تداول متقدم مدعوم بالذكاء الاصطناعي وتحليل السوق الشامل</p>
    </div>

    <!-- Navigation -->
    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showTab('overview')">
            <i class="fas fa-chart-line"></i> نظرة عامة
        </button>
        <button class="nav-tab" onclick="showTab('portfolio')">
            <i class="fas fa-wallet"></i> المحفظة
        </button>
        <button class="nav-tab" onclick="showTab('analysis')">
            <i class="fas fa-brain"></i> التحليل
        </button>
        <button class="nav-tab" onclick="showTab('recommendations')">
            <i class="fas fa-lightbulb"></i> التوصيات
        </button>
        <button class="nav-tab" onclick="showTab('history')">
            <i class="fas fa-history"></i> السجل
        </button>
        <button class="nav-tab" onclick="showTab('settings')">
            <i class="fas fa-cog"></i> الإعدادات
        </button>
    </div>

    <div class="container">
        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <!-- System Status Cards -->
            <div class="grid grid-4">
                <div class="card">
                    <h3><i class="fas fa-server"></i> حالة النظام</h3>
                    <div class="metric">
                        <span>KuCoin API</span>
                        <span><span class="status-indicator status-online"></span><span id="api-status">متصل</span></span>
                    </div>
                    <div class="metric">
                        <span>محرك الذكاء الاصطناعي</span>
                        <span><span class="status-indicator status-online"></span><span id="ai-status">يعمل</span></span>
                    </div>
                    <div class="metric">
                        <span>تحليل السوق</span>
                        <span><span class="status-indicator status-online"></span><span id="analysis-status">نشط</span></span>
                    </div>
                    <div class="metric">
                        <span>آخر تحديث</span>
                        <span class="metric-value" id="last-update">الآن</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-chart-bar"></i> ملخص السوق</h3>
                    <div class="metric">
                        <span>BTC-USDT</span>
                        <span class="metric-value" id="btc-price">$108,000</span>
                    </div>
                    <div class="metric">
                        <span>الإشارة</span>
                        <span class="signal-sell" id="btc-signal">بيع (60.3%)</span>
                    </div>
                    <div class="metric">
                        <span>مستوى المخاطر</span>
                        <span class="metric-value" id="btc-risk">منخفض</span>
                    </div>
                    <div class="metric">
                        <span>الأنماط</span>
                        <span class="metric-value" id="btc-patterns">3 صاعدة، 8 هابطة</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-wallet"></i> حالة المحفظة</h3>
                    <div class="metric">
                        <span>القيمة الإجمالية</span>
                        <span class="metric-value metric-positive" id="portfolio-value">$64.96 USDT</span>
                    </div>
                    <div class="metric">
                        <span>عدد الأصول</span>
                        <span class="metric-value" id="portfolio-holdings">8 عملات</span>
                    </div>
                    <div class="metric">
                        <span>أفضل أداء</span>
                        <span class="metric-value metric-positive" id="best-performer">PIXEL (+90%)</span>
                    </div>
                    <div class="metric">
                        <span>التغيير اليومي</span>
                        <span class="metric-value metric-positive" id="portfolio-change">+2.3%</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-brain"></i> تنبؤات الذكاء الاصطناعي</h3>
                    <div class="metric">
                        <span>دقة النموذج</span>
                        <span class="metric-value metric-positive" id="model-accuracy">87.3%</span>
                    </div>
                    <div class="metric">
                        <span>مستوى الثقة</span>
                        <span class="metric-value" id="prediction-confidence">عالي</span>
                    </div>
                    <div class="metric">
                        <span>الساعة القادمة</span>
                        <span class="metric-value" id="next-prediction">-0.2% (احتفظ)</span>
                    </div>
                    <div class="metric">
                        <span>الميزات النشطة</span>
                        <span class="metric-value" id="feature-count">104</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h3><i class="fas fa-bolt"></i> إجراءات سريعة</h3>
                <button class="btn" onclick="analyzeMarket()">
                    <i class="fas fa-search"></i> تحليل السوق
                </button>
                <button class="btn btn-success" onclick="updatePortfolio()">
                    <i class="fas fa-sync"></i> تحديث المحفظة
                </button>
                <button class="btn btn-warning" onclick="runPrediction()">
                    <i class="fas fa-crystal-ball"></i> تنبؤ ذكي
                </button>
                <button class="btn" onclick="generateSignals()">
                    <i class="fas fa-signal"></i> توليد الإشارات
                </button>
                <button class="btn" onclick="exportData()">
                    <i class="fas fa-download"></i> تصدير البيانات
                </button>
            </div>

            <!-- Live Activity Log -->
            <div class="card">
                <h3><i class="fas fa-list"></i> سجل النشاط المباشر</h3>
                <div class="log" id="activity-log">
                    <div class="log-entry">🚀 تم تشغيل بوت التداول الذكي بنجاح</div>
                    <div class="log-entry">🔗 تم الاتصال بـ KuCoin API</div>
                    <div class="log-entry">🧠 تم تحميل نماذج الذكاء الاصطناعي</div>
                    <div class="log-entry">📊 محرك تحليل السوق نشط</div>
                    <div class="log-entry">💼 تم مزامنة بيانات المحفظة</div>
                    <div class="log-entry">🎯 جاهز للتداول التلقائي</div>
                </div>
            </div>
        </div>

        <!-- Portfolio Tab -->
        <div id="portfolio" class="tab-content">
            <div class="card">
                <h3><i class="fas fa-table"></i> جدول العملات الرقمية</h3>
                <div class="table-container">
                    <table class="table" id="portfolio-table">
                        <thead>
                            <tr>
                                <th>العملة</th>
                                <th>السعر الحالي</th>
                                <th>التغيير 24س</th>
                                <th>الحجم</th>
                                <th>RSI</th>
                                <th>MACD</th>
                                <th>الإشارة</th>
                                <th>الثقة</th>
                                <th>المخاطر</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody id="portfolio-table-body">
                            <!-- Data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Analysis Tab -->
        <div id="analysis" class="tab-content">
            <div class="grid grid-2">
                <div class="card">
                    <h3><i class="fas fa-chart-line"></i> الرسوم البيانية</h3>
                    <div class="chart-container" id="price-chart"></div>
                </div>
                <div class="card">
                    <h3><i class="fas fa-cogs"></i> المؤشرات التقنية</h3>
                    <div class="chart-container" id="indicators-chart"></div>
                </div>
            </div>
        </div>

        <!-- Recommendations Tab -->
        <div id="recommendations" class="tab-content">
            <div class="grid grid-3">
                <div class="card">
                    <h3><i class="fas fa-arrow-up"></i> توصيات الشراء</h3>
                    <div id="buy-recommendations"></div>
                </div>
                <div class="card">
                    <h3><i class="fas fa-arrow-down"></i> توصيات البيع</h3>
                    <div id="sell-recommendations"></div>
                </div>
                <div class="card">
                    <h3><i class="fas fa-hand-paper"></i> توصيات الاحتفاظ</h3>
                    <div id="hold-recommendations"></div>
                </div>
            </div>
        </div>

        <!-- History Tab -->
        <div id="history" class="tab-content">
            <div class="card">
                <h3><i class="fas fa-history"></i> سجل العمليات</h3>
                <div class="table-container">
                    <table class="table" id="history-table">
                        <thead>
                            <tr>
                                <th>الوقت</th>
                                <th>العملة</th>
                                <th>النوع</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>الربح/الخسارة</th>
                            </tr>
                        </thead>
                        <tbody id="history-table-body">
                            <!-- Data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <div class="grid grid-2">
                <div class="card">
                    <h3><i class="fas fa-sliders-h"></i> إعدادات التداول</h3>
                    <div class="metric">
                        <span>وضع التداول</span>
                        <select id="trading-mode">
                            <option value="dry">تجريبي</option>
                            <option value="live">حقيقي</option>
                        </select>
                    </div>
                    <div class="metric">
                        <span>مستوى المخاطر</span>
                        <select id="risk-level">
                            <option value="low">منخفض</option>
                            <option value="medium">متوسط</option>
                            <option value="high">عالي</option>
                        </select>
                    </div>
                    <div class="metric">
                        <span>حجم المركز (%)</span>
                        <input type="range" id="position-size" min="1" max="10" value="2">
                        <span id="position-size-value">2%</span>
                    </div>
                </div>

                <div class="card">
                    <h3><i class="fas fa-bell"></i> إعدادات التنبيهات</h3>
                    <div class="metric">
                        <span>تنبيهات الأسعار</span>
                        <input type="checkbox" id="price-alerts" checked>
                    </div>
                    <div class="metric">
                        <span>تنبيهات الإشارات</span>
                        <input type="checkbox" id="signal-alerts" checked>
                    </div>
                    <div class="metric">
                        <span>تنبيهات المخاطر</span>
                        <input type="checkbox" id="risk-alerts" checked>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p><i class="fas fa-robot"></i> بوت التداول الذكي v2.0 | مدعوم بالذكاء الاصطناعي المتقدم وتحليل السوق الشامل</p>
    </div>

    <script>
        // Initialize Socket.IO
        const socket = io();

        // Global variables
        let currentTheme = 'dark';
        let portfolioData = {};
        let marketData = {};
        let analysisData = {};

        // Socket events
        socket.on('connect', function() {
            addLogEntry('🔗 تم الاتصال بخادم بوت التداول');
        });

        socket.on('status', function(data) {
            addLogEntry('📡 ' + data.message);
        });

        socket.on('portfolio_update', function(data) {
            portfolioData = data;
            updatePortfolioTable();
            showNotification('تم تحديث بيانات المحفظة', 'success');
        });

        socket.on('market_update', function(data) {
            marketData = data;
            updateMarketData();
        });

        socket.on('analysis_update', function(data) {
            analysisData = data;
            updateAnalysisData();
        });

        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');
        }

        // Theme toggle
        function toggleTheme() {
            currentTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.body.classList.toggle('light-theme');

            const icon = document.querySelector('.theme-toggle i');
            icon.className = currentTheme === 'dark' ? 'fas fa-moon' : 'fas fa-sun';
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const text = document.getElementById('notification-text');

            text.textContent = message;
            notification.className = `notification show ${type}`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Log management
        function addLogEntry(message) {
            const log = document.getElementById('activity-log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = new Date().toLocaleTimeString('ar-SA') + ' - ' + message;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;

            // Keep only last 50 entries
            while (log.children.length > 50) {
                log.removeChild(log.firstChild);
            }
        }

        // Portfolio table update
        function updatePortfolioTable() {
            const tbody = document.getElementById('portfolio-table-body');
            tbody.innerHTML = '';

            // Sample data - replace with real data
            const symbols = ['BTC-USDT', 'ETH-USDT', 'PIXEL-USDT', 'HIGH-USDT', 'STG-USDT'];

            symbols.forEach(symbol => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${symbol}</td>
                    <td>$${(Math.random() * 1000 + 100).toFixed(2)}</td>
                    <td class="${Math.random() > 0.5 ? 'metric-positive' : 'metric-negative'}">
                        ${(Math.random() * 10 - 5).toFixed(2)}%
                    </td>
                    <td>${(Math.random() * 1000000).toFixed(0)}</td>
                    <td>${(Math.random() * 100).toFixed(1)}</td>
                    <td>${(Math.random() * 2 - 1).toFixed(3)}</td>
                    <td class="signal-${['buy', 'sell', 'hold'][Math.floor(Math.random() * 3)]}">
                        ${['شراء', 'بيع', 'احتفظ'][Math.floor(Math.random() * 3)]}
                    </td>
                    <td>${(Math.random() * 100).toFixed(1)}%</td>
                    <td>${['منخفض', 'متوسط', 'عالي'][Math.floor(Math.random() * 3)]}</td>
                    <td>
                        <button class="btn" onclick="analyzeSymbol('${symbol}')">تحليل</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Market data update
        function updateMarketData() {
            // Update market overview cards
            addLogEntry('📊 تم تحديث بيانات السوق');
        }

        // Analysis data update
        function updateAnalysisData() {
            addLogEntry('🧠 تم تحديث التحليل');
        }

        // Action functions
        function analyzeMarket() {
            addLogEntry('🔍 بدء تحليل السوق...');
            socket.emit('request_analysis', {symbol: 'BTC-USDT'});
            showNotification('جاري تحليل السوق...', 'info');
        }

        function updatePortfolio() {
            addLogEntry('💼 تحديث بيانات المحفظة...');
            socket.emit('request_portfolio_update');
            showNotification('جاري تحديث المحفظة...', 'info');
        }

        function runPrediction() {
            addLogEntry('🔮 تشغيل تنبؤ الذكاء الاصطناعي...');
            setTimeout(() => {
                addLogEntry('🎯 تم إكمال التنبؤ: إشارة احتفظ');
                showNotification('تم إكمال التنبؤ بنجاح', 'success');
            }, 2000);
        }

        function generateSignals() {
            addLogEntry('📈 توليد إشارات التداول...');
            socket.emit('request_recommendations');
            setTimeout(() => {
                addLogEntry('✅ تم توليد إشارات التداول لـ 8 عملات');
                showNotification('تم توليد الإشارات بنجاح', 'success');
            }, 1500);
        }

        function exportData() {
            addLogEntry('📊 تصدير بيانات التحليل...');
            setTimeout(() => {
                addLogEntry('💾 تم تصدير البيانات بنجاح');
                showNotification('تم تصدير البيانات', 'success');
            }, 1000);
        }

        function analyzeSymbol(symbol) {
            addLogEntry(`🔍 تحليل ${symbol}...`);
            socket.emit('request_analysis', {symbol: symbol});
        }

        // Auto-update functions
        setInterval(() => {
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString('ar-SA');
        }, 1000);

        // Simulate real-time updates
        setInterval(() => {
            const messages = [
                '📊 تم تحديث بيانات السوق',
                '🔍 تم اكتشاف نمط في ETH-USDT',
                '💡 ثقة نموذج الذكاء الاصطناعي: 89.2%',
                '📈 تم تحديد فرصة تداول جديدة',
                '⚡ تم إكمال التحليل الفوري'
            ];
            const randomMessage = messages[Math.floor(Math.random() * messages.length)];
            addLogEntry(randomMessage);
        }, 15000);

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updatePortfolioTable();
            addLogEntry('🚀 تم تحميل لوحة التحكم المتقدمة');
        });

        // Settings handlers
        document.getElementById('position-size').addEventListener('input', function() {
            document.getElementById('position-size-value').textContent = this.value + '%';
        });
    </script>
</body>
</html>
        """
        return html_template

    async def _analyze_symbol_async(self, symbol):
        """Analyze symbol asynchronously with enhanced features"""
        try:
            if not self.kucoin_client:
                self.kucoin_client = KuCoinClient()

            async with self.kucoin_client:
                # Get market data
                klines = await self.kucoin_client.get_klines(symbol, '1hour')
                ticker = await self.kucoin_client.get_ticker(symbol)

                if klines and len(klines) >= 50:
                    # Convert to DataFrame
                    data = []
                    for kline in reversed(klines[:100]):
                        data.append({
                            'open': float(kline[1]),
                            'high': float(kline[3]),
                            'low': float(kline[4]),
                            'close': float(kline[2]),
                            'volume': float(kline[5]),
                            'quote_volume': float(kline[6]),
                            'trades_count': 100
                        })

                    df = pd.DataFrame(data)
                    df.index = pd.date_range(end=datetime.now(), periods=len(df), freq='h')

                    # Enhanced analysis
                    signal = await self.market_analyzer.analyze_symbol(symbol, df)

                    # Feature engineering
                    features_df = self.feature_engineer.create_technical_features(df)
                    selected_features = self.feature_engineer.select_features(features_df, 'close', 'correlation', 20)

                    # Calculate additional metrics
                    current_price = float(ticker.get('last', 0))
                    price_change_24h = float(ticker.get('changeRate', 0)) * 100
                    volume_24h = float(ticker.get('vol', 0))

                    # Store enhanced results
                    self.analysis_results[symbol] = {
                        'signal': signal.signal_type,
                        'confidence': signal.confidence,
                        'risk_level': signal.risk_level,
                        'current_price': current_price,
                        'price_change_24h': price_change_24h,
                        'volume_24h': volume_24h,
                        'target_price': signal.target_price,
                        'stop_loss': signal.stop_loss,
                        'reasoning': signal.reasoning,
                        'features_count': len(selected_features),
                        'technical_indicators': {
                            'rsi': features_df['rsi_14'].iloc[-1] if 'rsi_14' in features_df.columns else None,
                            'macd': features_df['macd'].iloc[-1] if 'macd' in features_df.columns else None,
                            'bb_position': features_df['bb_position'].iloc[-1] if 'bb_position' in features_df.columns else None
                        },
                        'timestamp': datetime.now().isoformat()
                    }

                    # Update market data
                    self.market_data[symbol] = {
                        'price': current_price,
                        'change_24h': price_change_24h,
                        'volume': volume_24h,
                        'timestamp': datetime.now().isoformat()
                    }

                    # Emit to clients
                    self.socketio.emit('analysis_complete', {
                        'symbol': symbol,
                        'data': self.analysis_results[symbol]
                    })

                    self.socketio.emit('market_update', {
                        'symbol': symbol,
                        'data': self.market_data[symbol]
                    })

                    # Add to trade history if signal is strong
                    if signal.confidence > 0.7:
                        self._add_trade_record(symbol, signal)

                    # Check for alerts
                    self._check_alerts(symbol, current_price, signal)

        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}")

    async def _update_portfolio_async(self):
        """Update portfolio data asynchronously"""
        try:
            if not self.kucoin_client:
                self.kucoin_client = KuCoinClient()

            async with self.kucoin_client:
                # Get account info
                accounts = await self.kucoin_client.get_account_info()

                portfolio_summary = {
                    'total_value': 0.0,
                    'holdings': {},
                    'performance': {},
                    'allocation': {}
                }

                # Process trading accounts
                trading_accounts = [acc for acc in accounts if acc['type'] == 'trade']

                for account in trading_accounts:
                    currency = account['currency']
                    balance = float(account['balance'])
                    available = float(account['available'])

                    if balance > 0:
                        portfolio_summary['holdings'][currency] = {
                            'balance': balance,
                            'available': available
                        }

                        # Get current value in USDT
                        if currency != 'USDT':
                            try:
                                ticker = await self.kucoin_client.get_ticker(f"{currency}-USDT")
                                price = float(ticker.get('last', 0))
                                value_usdt = balance * price
                                portfolio_summary['total_value'] += value_usdt

                                portfolio_summary['holdings'][currency]['price'] = price
                                portfolio_summary['holdings'][currency]['value_usdt'] = value_usdt
                            except:
                                pass
                        else:
                            portfolio_summary['total_value'] += balance

                self.portfolio_data = portfolio_summary

                # Emit to clients
                self.socketio.emit('portfolio_update', self.portfolio_data)

        except Exception as e:
            logger.error(f"Error updating portfolio: {e}")

    async def _generate_recommendations_async(self):
        """Generate trading recommendations"""
        try:
            recommendations = {
                'buy': [],
                'sell': [],
                'hold': []
            }

            # Analyze each symbol in portfolio
            for symbol in self.portfolio_symbols:
                if symbol in self.analysis_results:
                    analysis = self.analysis_results[symbol]

                    recommendation = {
                        'symbol': symbol,
                        'signal': analysis['signal'],
                        'confidence': analysis['confidence'],
                        'current_price': analysis['current_price'],
                        'target_price': analysis.get('target_price'),
                        'stop_loss': analysis.get('stop_loss'),
                        'reasoning': analysis['reasoning'],
                        'risk_level': analysis['risk_level']
                    }

                    if analysis['signal'] == 'BUY' and analysis['confidence'] > 0.6:
                        recommendations['buy'].append(recommendation)
                    elif analysis['signal'] == 'SELL' and analysis['confidence'] > 0.6:
                        recommendations['sell'].append(recommendation)
                    else:
                        recommendations['hold'].append(recommendation)

            # Sort by confidence
            for category in recommendations:
                recommendations[category].sort(key=lambda x: x['confidence'], reverse=True)

            self.recommendations = recommendations

            # Emit to clients
            self.socketio.emit('recommendations_update', self.recommendations)

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")

    def _add_trade_record(self, symbol, signal):
        """Add trade record to history"""
        trade_record = {
            'id': str(uuid.uuid4()),
            'timestamp': datetime.now().isoformat(),
            'symbol': symbol,
            'signal': signal.signal_type,
            'confidence': signal.confidence,
            'entry_price': signal.entry_price,
            'target_price': signal.target_price,
            'stop_loss': signal.stop_loss,
            'status': 'PENDING',
            'pnl': 0.0
        }

        self.trade_history.append(trade_record)

        # Keep only last 1000 records
        self.trade_history = self.trade_history[-1000:]

    def _check_alerts(self, symbol, current_price, signal):
        """Check for price and signal alerts"""
        # Price movement alerts
        if symbol in self.market_data:
            prev_data = self.market_data[symbol]
            if abs(current_price - prev_data['price']) / prev_data['price'] > 0.05:  # 5% change
                alert = {
                    'id': str(uuid.uuid4()),
                    'timestamp': datetime.now().isoformat(),
                    'type': 'PRICE_MOVEMENT',
                    'symbol': symbol,
                    'message': f"{symbol} تغير بنسبة {((current_price - prev_data['price']) / prev_data['price'] * 100):+.2f}%",
                    'severity': 'HIGH' if abs((current_price - prev_data['price']) / prev_data['price']) > 0.1 else 'MEDIUM'
                }
                self.alerts.append(alert)

        # Signal alerts
        if signal.confidence > 0.8:
            alert = {
                'id': str(uuid.uuid4()),
                'timestamp': datetime.now().isoformat(),
                'type': 'STRONG_SIGNAL',
                'symbol': symbol,
                'message': f"إشارة {signal.signal_type} قوية لـ {symbol} بثقة {signal.confidence:.1%}",
                'severity': 'HIGH'
            }
            self.alerts.append(alert)

        # Keep only last 100 alerts
        self.alerts = self.alerts[-100:]

    def _add_alert(self, alert_data):
        """Add custom alert"""
        alert = {
            'id': str(uuid.uuid4()),
            'timestamp': datetime.now().isoformat(),
            'type': 'CUSTOM',
            'symbol': alert_data.get('symbol', ''),
            'message': alert_data.get('message', ''),
            'severity': alert_data.get('severity', 'MEDIUM')
        }
        self.alerts.append(alert)

    def _export_data(self, format_type):
        """Export data in various formats"""
        try:
            if format_type == 'json':
                export_data = {
                    'portfolio': self.portfolio_data,
                    'market_data': self.market_data,
                    'analysis': self.analysis_results,
                    'recommendations': self.recommendations,
                    'trade_history': self.trade_history[-50:],
                    'alerts': self.alerts[-20:],
                    'timestamp': datetime.now().isoformat()
                }

                filename = f"trading_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                return jsonify({'success': True, 'filename': filename})

            else:
                return jsonify({'error': 'Unsupported format'})

        except Exception as e:
            return jsonify({'error': str(e)})

    def start_background_updates(self):
        """Start background data updates"""
        self.running = True

        def update_loop():
            while self.running:
                try:
                    # Update market data every 30 seconds
                    asyncio.run(self._update_market_data_background())
                    time.sleep(30)
                except Exception as e:
                    logger.error(f"Background update error: {e}")
                    time.sleep(60)

        self.update_thread = threading.Thread(target=update_loop, daemon=True)
        self.update_thread.start()

    async def _update_market_data_background(self):
        """Background market data update"""
        try:
            if not self.kucoin_client:
                self.kucoin_client = KuCoinClient()

            async with self.kucoin_client:
                for symbol in self.portfolio_symbols[:3]:  # Update top 3 symbols
                    try:
                        ticker = await self.kucoin_client.get_ticker(symbol)
                        current_price = float(ticker.get('last', 0))
                        price_change_24h = float(ticker.get('changeRate', 0)) * 100

                        self.market_data[symbol] = {
                            'price': current_price,
                            'change_24h': price_change_24h,
                            'volume': float(ticker.get('vol', 0)),
                            'timestamp': datetime.now().isoformat()
                        }

                        # Emit update
                        self.socketio.emit('market_update', {
                            'symbol': symbol,
                            'data': self.market_data[symbol]
                        })

                    except Exception as e:
                        logger.warning(f"Failed to update {symbol}: {e}")

        except Exception as e:
            logger.error(f"Background market update failed: {e}")

    def stop_background_updates(self):
        """Stop background updates"""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)

    def run(self, host='127.0.0.1', port=5001, debug=False):
        """Run the advanced dashboard"""
        print(f"""
        ╔══════════════════════════════════════════════════════════════╗
        ║                                                              ║
        ║        🤖 لوحة التحكم المتقدمة - بوت التداول الذكي        ║
        ║                                                              ║
        ╚══════════════════════════════════════════════════════════════╝

        🌐 رابط لوحة التحكم: http://{host}:{port}
        🚀 بدء تشغيل الخادم المتقدم...

        ✨ الميزات الجديدة:
        📊 جداول تفاعلية للعملات
        🎯 نظام التوصيات المتقدم
        📈 رسوم بيانية تفاعلية
        🔔 نظام التنبيهات الذكي
        📱 تصميم متجاوب
        🌙 وضع ليلي/نهاري
        """)

        # Start background updates
        self.start_background_updates()

        try:
            self.socketio.run(self.app, host=host, port=port, debug=debug)
        finally:
            self.stop_background_updates()


def main():
    """Main function"""
    try:
        dashboard = AdvancedTradingDashboard()
        dashboard.run(host='0.0.0.0', port=5001, debug=False)

    except KeyboardInterrupt:
        print("\n👋 تم إيقاف لوحة التحكم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل لوحة التحكم: {e}")
        if not FLASK_AVAILABLE:
            print("\n💡 لتثبيت المتطلبات:")
            print("   pip install flask flask-socketio plotly")


if __name__ == "__main__":
    main()