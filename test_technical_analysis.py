#!/usr/bin/env python3
"""
Test script for Technical Analysis system
"""

import asyncio
import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.analysis import TechnicalIndicators, PatternRecognition, MarketAnalyzer
from src.data import BinanceClient, DataCollector
from src.core.logger import logger


def generate_sample_data(periods: int = 100) -> pd.DataFrame:
    """Generate sample OHLCV data for testing"""
    np.random.seed(42)
    
    # Generate realistic price data
    base_price = 50000
    dates = pd.date_range(start=datetime.now() - timedelta(hours=periods), periods=periods, freq='H')
    
    # Random walk with trend
    returns = np.random.normal(0.001, 0.02, periods)
    prices = [base_price]
    
    for i in range(1, periods):
        new_price = prices[-1] * (1 + returns[i])
        prices.append(new_price)
    
    # Generate OHLCV data
    data = []
    for i, price in enumerate(prices):
        volatility = np.random.uniform(0.005, 0.02)
        high = price * (1 + volatility)
        low = price * (1 - volatility)
        open_price = prices[i-1] if i > 0 else price
        close = price
        volume = np.random.uniform(100, 1000)
        
        data.append({
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume,
            'quote_volume': volume * price,
            'trades_count': int(np.random.uniform(50, 200))
        })
    
    df = pd.DataFrame(data, index=dates)
    return df


async def test_technical_indicators():
    """Test technical indicators functionality"""
    print("🔍 Testing Technical Indicators...")
    print("=" * 50)
    
    try:
        indicators = TechnicalIndicators()
        df = generate_sample_data(100)
        
        print(f"✅ Generated sample data: {len(df)} periods")
        print(f"   Price range: ${df['low'].min():.2f} - ${df['high'].max():.2f}")
        
        # Test individual indicators
        print("\n📊 Testing individual indicators:")
        
        # Moving averages
        sma_20 = indicators.sma(df['close'], 20)
        ema_20 = indicators.ema(df['close'], 20)
        print(f"   SMA(20): ${sma_20.iloc[-1]:.2f}")
        print(f"   EMA(20): ${ema_20.iloc[-1]:.2f}")
        
        # Oscillators
        rsi = indicators.rsi(df['close'])
        print(f"   RSI(14): {rsi.iloc[-1]:.2f}")
        
        # MACD
        macd_line, signal_line, histogram = indicators.macd(df['close'])
        print(f"   MACD: {macd_line.iloc[-1]:.4f}")
        print(f"   Signal: {signal_line.iloc[-1]:.4f}")
        print(f"   Histogram: {histogram.iloc[-1]:.4f}")
        
        # Bollinger Bands
        upper_bb, sma_bb, lower_bb = indicators.bollinger_bands(df['close'])
        print(f"   BB Upper: ${upper_bb.iloc[-1]:.2f}")
        print(f"   BB Lower: ${lower_bb.iloc[-1]:.2f}")
        
        # Stochastic
        k_percent, d_percent = indicators.stochastic(df['high'], df['low'], df['close'])
        print(f"   Stochastic %K: {k_percent.iloc[-1]:.2f}")
        print(f"   Stochastic %D: {d_percent.iloc[-1]:.2f}")
        
        # Volume indicators
        obv = indicators.obv(df['close'], df['volume'])
        print(f"   OBV: {obv.iloc[-1]:.0f}")
        
        # Volatility
        atr = indicators.atr(df['high'], df['low'], df['close'])
        print(f"   ATR(14): {atr.iloc[-1]:.2f}")
        
        print("\n📈 Testing signal generation:")
        
        # Test comprehensive analysis
        signals = indicators.analyze_all_indicators(df)
        print(f"✅ Generated signals for {len(signals)} indicators:")
        
        for name, signal in signals.items():
            print(f"   {name}: {signal.signal} (strength: {signal.strength:.2f})")
        
        # Test trend analysis
        trend_analysis = indicators.get_trend_strength(df)
        print(f"\n📊 Trend Analysis:")
        print(f"   Direction: {trend_analysis['trend_direction']}")
        print(f"   Strength: {trend_analysis['trend_strength']:.4f}")
        
        print("\n✅ Technical indicators test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Technical indicators test failed: {e}")
        return False


async def test_pattern_recognition():
    """Test pattern recognition functionality"""
    print("\n🔍 Testing Pattern Recognition...")
    print("=" * 50)
    
    try:
        pattern_rec = PatternRecognition()
        df = generate_sample_data(100)
        
        print("📊 Testing candlestick patterns:")
        
        # Test individual pattern detection
        current = df.iloc[-1]
        
        # Test Doji
        is_doji = pattern_rec.is_doji(current['open'], current['high'], current['low'], current['close'])
        print(f"   Current candle is Doji: {is_doji}")
        
        # Test Hammer
        is_hammer = pattern_rec.is_hammer(current['open'], current['high'], current['low'], current['close'])
        print(f"   Current candle is Hammer: {is_hammer}")
        
        # Detect all candlestick patterns
        candlestick_patterns = pattern_rec.detect_candlestick_patterns(df)
        print(f"✅ Detected {len(candlestick_patterns)} candlestick patterns:")
        
        pattern_counts = {}
        for pattern in candlestick_patterns:
            pattern_counts[pattern.pattern_name] = pattern_counts.get(pattern.pattern_name, 0) + 1
        
        for pattern_name, count in pattern_counts.items():
            print(f"   {pattern_name}: {count}")
        
        print("\n📈 Testing chart patterns:")
        
        # Support and resistance
        support_resistance = pattern_rec.detect_support_resistance(df)
        print(f"   Support levels: {len(support_resistance['support'])}")
        print(f"   Resistance levels: {len(support_resistance['resistance'])}")
        
        if support_resistance['support']:
            print(f"   Nearest support: ${support_resistance['support'][-1]:.2f}")
        if support_resistance['resistance']:
            print(f"   Nearest resistance: ${support_resistance['resistance'][0]:.2f}")
        
        # Trend lines
        trend_lines = pattern_rec.detect_trend_lines(df)
        overall_trend = trend_lines.get('overall_trend', {})
        print(f"   Overall trend: {overall_trend.get('direction', 'NEUTRAL')}")
        print(f"   Trend R²: {overall_trend.get('r_squared', 0):.3f}")
        
        # Triangle pattern
        triangle = pattern_rec.detect_triangle_pattern(df)
        if triangle:
            print(f"   Triangle pattern: {triangle.pattern_name} ({triangle.confidence:.2f})")
        else:
            print("   No triangle pattern detected")
        
        # Head and shoulders
        head_shoulders = pattern_rec.detect_head_and_shoulders(df)
        if head_shoulders:
            print(f"   Head & Shoulders: {head_shoulders.pattern_type} ({head_shoulders.confidence:.2f})")
        else:
            print("   No Head & Shoulders pattern detected")
        
        # Comprehensive analysis
        all_patterns = pattern_rec.analyze_all_patterns(df)
        pattern_summary = all_patterns.get('pattern_summary', {})
        print(f"\n📊 Pattern Summary:")
        print(f"   Bullish: {pattern_summary.get('BULLISH', 0)}")
        print(f"   Bearish: {pattern_summary.get('BEARISH', 0)}")
        print(f"   Neutral: {pattern_summary.get('NEUTRAL', 0)}")
        
        print("\n✅ Pattern recognition test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Pattern recognition test failed: {e}")
        return False


async def test_market_analyzer():
    """Test comprehensive market analyzer"""
    print("\n🔍 Testing Market Analyzer...")
    print("=" * 50)
    
    try:
        analyzer = MarketAnalyzer()
        
        # Test with sample data
        df = generate_sample_data(100)
        
        print("📊 Testing comprehensive analysis:")
        
        # Analyze single symbol
        signal = await analyzer.analyze_symbol("TESTUSDT", df)
        
        print(f"✅ Generated comprehensive signal:")
        print(f"   Symbol: {signal.symbol}")
        print(f"   Signal: {signal.signal_type}")
        print(f"   Strength: {signal.strength:.3f}")
        print(f"   Confidence: {signal.confidence:.3f}")
        print(f"   Risk Level: {signal.risk_level}")
        print(f"   Entry Price: ${signal.entry_price:.2f}")
        
        if signal.target_price:
            print(f"   Target Price: ${signal.target_price:.2f}")
        if signal.stop_loss:
            print(f"   Stop Loss: ${signal.stop_loss:.2f}")
        
        print(f"   Technical Signals: {len(signal.technical_signals)}")
        print(f"   Pattern Signals: {len(signal.pattern_signals)}")
        print(f"   Reasoning: {signal.reasoning}")
        
        # Test multiple symbols
        print("\n📈 Testing multiple symbol analysis:")
        
        symbols_data = {
            'BTCUSDT': generate_sample_data(100),
            'ETHUSDT': generate_sample_data(100),
            'ADAUSDT': generate_sample_data(100)
        }
        
        signals = await analyzer.analyze_multiple_symbols(symbols_data)
        print(f"✅ Analyzed {len(signals)} symbols:")
        
        for symbol, signal in signals.items():
            print(f"   {symbol}: {signal.signal_type} (conf: {signal.confidence:.2f})")
        
        # Market overview
        overview = analyzer.get_market_overview(signals)
        print(f"\n📊 Market Overview:")
        print(f"   Total symbols: {overview['total_symbols']}")
        print(f"   Buy signals: {overview['buy_signals']}")
        print(f"   Sell signals: {overview['sell_signals']}")
        print(f"   Hold signals: {overview['hold_signals']}")
        print(f"   Market sentiment: {overview['market_sentiment']}")
        print(f"   Average confidence: {overview['average_confidence']:.3f}")
        print(f"   High confidence signals: {overview['high_confidence_signals']}")
        
        if overview['top_opportunities']:
            print(f"   Top opportunities:")
            for opp in overview['top_opportunities']:
                print(f"     {opp['symbol']}: {opp['signal']} ({opp['confidence']:.2f})")
        
        print("\n✅ Market analyzer test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Market analyzer test failed: {e}")
        return False


async def test_with_real_data():
    """Test with real market data"""
    print("\n🔍 Testing with Real Market Data...")
    print("=" * 50)
    
    try:
        # Get real data
        binance_client = BinanceClient(testnet=True)
        collector = DataCollector(binance_client)
        collector.use_database = False
        collector.use_redis = False
        
        await binance_client.connect()
        
        # Get historical data
        df = await collector.collect_historical_data("BTCUSDT", "1h", days=7)
        
        if df.empty:
            print("❌ No real data available")
            return False
        
        print(f"✅ Retrieved real data: {len(df)} periods")
        print(f"   Date range: {df.index.min()} to {df.index.max()}")
        print(f"   Price range: ${df['low'].min():.2f} - ${df['high'].max():.2f}")
        
        # Analyze with real data
        analyzer = MarketAnalyzer()
        signal = await analyzer.analyze_symbol("BTCUSDT", df)
        
        print(f"\n📊 Real Data Analysis Results:")
        print(f"   Signal: {signal.signal_type}")
        print(f"   Strength: {signal.strength:.3f}")
        print(f"   Confidence: {signal.confidence:.3f}")
        print(f"   Risk Level: {signal.risk_level}")
        print(f"   Current Price: ${signal.entry_price:.2f}")
        
        if signal.target_price:
            potential_gain = ((signal.target_price - signal.entry_price) / signal.entry_price) * 100
            print(f"   Target: ${signal.target_price:.2f} ({potential_gain:+.2f}%)")
        
        if signal.stop_loss:
            potential_loss = ((signal.stop_loss - signal.entry_price) / signal.entry_price) * 100
            print(f"   Stop Loss: ${signal.stop_loss:.2f} ({potential_loss:+.2f}%)")
        
        print(f"   Reasoning: {signal.reasoning}")
        
        # Show technical signals
        print(f"\n📈 Technical Signals:")
        for name, tech_signal in signal.technical_signals.items():
            print(f"   {name}: {tech_signal.signal} (strength: {tech_signal.strength:.2f})")
        
        # Show patterns
        if signal.pattern_signals:
            print(f"\n🔍 Detected Patterns:")
            for pattern in signal.pattern_signals[-5:]:  # Show last 5
                print(f"   {pattern.pattern_name}: {pattern.pattern_type} (conf: {pattern.confidence:.2f})")
        
        await binance_client.disconnect()
        
        print("\n✅ Real data analysis test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Real data analysis test failed: {e}")
        return False


async def main():
    """Main test function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🤖 AI Crypto Trading Bot - Technical Analysis Test   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Technical Indicators
    if await test_technical_indicators():
        tests_passed += 1
    
    # Test 2: Pattern Recognition
    if await test_pattern_recognition():
        tests_passed += 1
    
    # Test 3: Market Analyzer
    if await test_market_analyzer():
        tests_passed += 1
    
    # Test 4: Real Data Analysis
    if await test_with_real_data():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"📊 Technical Analysis Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All technical analysis tests passed!")
        print("\n✨ Your Technical Analysis system is working perfectly!")
        print("\n💡 Features confirmed:")
        print("   ✅ 15+ Technical Indicators (RSI, MACD, Bollinger Bands, etc.)")
        print("   ✅ Candlestick Pattern Recognition")
        print("   ✅ Chart Pattern Detection")
        print("   ✅ Support/Resistance Identification")
        print("   ✅ Trend Analysis")
        print("   ✅ Comprehensive Signal Generation")
        print("   ✅ Risk Assessment")
        print("   ✅ Price Target Calculation")
        print("   ✅ Real-time Market Analysis")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return tests_passed == total_tests


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
