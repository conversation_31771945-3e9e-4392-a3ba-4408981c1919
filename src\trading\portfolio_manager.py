"""
Portfolio Management System for AI Crypto Trading Bot
"""

import asyncio
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..data.kucoin_client import KuCoinClient, KuCoinAPIError
from ..core.config import settings
from ..core.logger import logger


@dataclass
class Position:
    """Trading position structure"""
    symbol: str
    quantity: float
    average_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_percent: float
    side: str  # 'long' or 'short'
    entry_time: datetime
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'quantity': self.quantity,
            'average_price': self.average_price,
            'current_price': self.current_price,
            'market_value': self.market_value,
            'unrealized_pnl': self.unrealized_pnl,
            'unrealized_pnl_percent': self.unrealized_pnl_percent,
            'side': self.side,
            'entry_time': self.entry_time.isoformat()
        }


@dataclass
class PortfolioSummary:
    """Portfolio summary structure"""
    total_value: float
    available_balance: float
    total_pnl: float
    total_pnl_percent: float
    positions_count: int
    largest_position: Optional[str]
    best_performer: Optional[str]
    worst_performer: Optional[str]
    
    def to_dict(self) -> Dict:
        return {
            'total_value': self.total_value,
            'available_balance': self.available_balance,
            'total_pnl': self.total_pnl,
            'total_pnl_percent': self.total_pnl_percent,
            'positions_count': self.positions_count,
            'largest_position': self.largest_position,
            'best_performer': self.best_performer,
            'worst_performer': self.worst_performer
        }


class PortfolioManager:
    """
    Advanced portfolio management system
    """
    
    def __init__(self, kucoin_client: KuCoinClient = None):
        self.kucoin_client = kucoin_client or KuCoinClient()
        self.positions: Dict[str, Position] = {}
        self.balances: Dict[str, float] = {}
        self.base_currency = "USDT"
        
        # Portfolio tracking
        self.initial_balance = 0.0
        self.last_update = None
        
        logger.info("Portfolio Manager initialized")
    
    async def update_portfolio(self):
        """Update portfolio data from exchange"""
        try:
            # Get account balances
            accounts = await self.kucoin_client.get_account_info()
            
            # Update balances
            self.balances = {}
            for account in accounts:
                if account['type'] == 'trade' and float(account['balance']) > 0:
                    currency = account['currency']
                    balance = float(account['balance'])
                    self.balances[currency] = balance
            
            # Update positions
            await self._update_positions()
            
            self.last_update = datetime.now()
            logger.info(f"Portfolio updated: {len(self.balances)} currencies, {len(self.positions)} positions")
            
        except Exception as e:
            logger.error(f"Failed to update portfolio: {e}")
    
    async def _update_positions(self):
        """Update position data with current market prices"""
        self.positions = {}
        
        for currency, balance in self.balances.items():
            if currency == self.base_currency:
                continue  # Skip base currency
            
            if balance > 0:
                symbol = f"{currency}-{self.base_currency}"
                
                try:
                    # Get current market price
                    ticker = await self.kucoin_client.get_ticker(symbol)
                    current_price = float(ticker.get('last', 0))
                    
                    if current_price > 0:
                        # Calculate position metrics
                        market_value = balance * current_price
                        
                        # For now, assume average price equals current price
                        # In a real system, you'd track this from trade history
                        average_price = current_price
                        unrealized_pnl = 0.0  # Would be calculated from entry price
                        unrealized_pnl_percent = 0.0
                        
                        position = Position(
                            symbol=symbol,
                            quantity=balance,
                            average_price=average_price,
                            current_price=current_price,
                            market_value=market_value,
                            unrealized_pnl=unrealized_pnl,
                            unrealized_pnl_percent=unrealized_pnl_percent,
                            side='long',
                            entry_time=datetime.now()  # Would be actual entry time
                        )
                        
                        self.positions[symbol] = position
                
                except Exception as e:
                    logger.warning(f"Failed to update position for {symbol}: {e}")
    
    async def get_portfolio_summary(self) -> PortfolioSummary:
        """Get comprehensive portfolio summary"""
        await self.update_portfolio()
        
        # Calculate total values
        total_value = 0.0
        available_balance = self.balances.get(self.base_currency, 0.0)
        total_pnl = 0.0
        
        # Add base currency balance
        total_value += available_balance
        
        # Add position values
        best_performer = None
        worst_performer = None
        largest_position = None
        max_position_value = 0.0
        best_pnl = float('-inf')
        worst_pnl = float('inf')
        
        for position in self.positions.values():
            total_value += position.market_value
            total_pnl += position.unrealized_pnl
            
            # Track best/worst performers
            if position.unrealized_pnl_percent > best_pnl:
                best_pnl = position.unrealized_pnl_percent
                best_performer = position.symbol
            
            if position.unrealized_pnl_percent < worst_pnl:
                worst_pnl = position.unrealized_pnl_percent
                worst_performer = position.symbol
            
            # Track largest position
            if position.market_value > max_position_value:
                max_position_value = position.market_value
                largest_position = position.symbol
        
        # Calculate total PnL percentage
        if self.initial_balance > 0:
            total_pnl_percent = (total_pnl / self.initial_balance) * 100
        else:
            total_pnl_percent = 0.0
        
        return PortfolioSummary(
            total_value=total_value,
            available_balance=available_balance,
            total_pnl=total_pnl,
            total_pnl_percent=total_pnl_percent,
            positions_count=len(self.positions),
            largest_position=largest_position,
            best_performer=best_performer,
            worst_performer=worst_performer
        )
    
    async def get_position(self, symbol: str) -> Optional[Position]:
        """Get position for a specific symbol"""
        await self.update_portfolio()
        return self.positions.get(symbol)
    
    async def get_all_positions(self) -> List[Position]:
        """Get all current positions"""
        await self.update_portfolio()
        return list(self.positions.values())
    
    async def get_balance(self, currency: str) -> float:
        """Get balance for a specific currency"""
        await self.update_portfolio()
        return self.balances.get(currency, 0.0)
    
    async def get_available_balance(self, currency: str = None) -> float:
        """Get available balance for trading"""
        currency = currency or self.base_currency
        return await self.get_balance(currency)
    
    async def calculate_position_size(
        self,
        symbol: str,
        risk_percent: float,
        entry_price: float,
        stop_loss_price: float
    ) -> float:
        """Calculate optimal position size based on risk management"""
        try:
            # Get available balance
            available_balance = await self.get_available_balance()
            
            if available_balance <= 0:
                return 0.0
            
            # Calculate risk amount
            risk_amount = available_balance * (risk_percent / 100)
            
            # Calculate risk per unit
            risk_per_unit = abs(entry_price - stop_loss_price)
            
            if risk_per_unit <= 0:
                return 0.0
            
            # Calculate position size
            position_size = risk_amount / risk_per_unit
            
            # Get symbol info for minimum size validation
            symbol_info = await self.kucoin_client.get_symbol_info(symbol)
            if symbol_info:
                min_size = float(symbol_info.get('baseMinSize', 0))
                max_size = float(symbol_info.get('baseMaxSize', float('inf')))
                
                # Apply constraints
                position_size = max(position_size, min_size)
                position_size = min(position_size, max_size)
                
                # Ensure we don't exceed available balance
                max_affordable = available_balance / entry_price
                position_size = min(position_size, max_affordable)
            
            return position_size
            
        except Exception as e:
            logger.error(f"Failed to calculate position size: {e}")
            return 0.0
    
    async def check_margin_requirements(self, symbol: str, quantity: float, price: float) -> bool:
        """Check if we have sufficient balance for the trade"""
        try:
            required_balance = quantity * price
            available_balance = await self.get_available_balance()
            
            # Add some buffer for fees (0.1%)
            required_balance *= 1.001
            
            return available_balance >= required_balance
            
        except Exception as e:
            logger.error(f"Failed to check margin requirements: {e}")
            return False
    
    async def get_portfolio_allocation(self) -> Dict[str, float]:
        """Get portfolio allocation by currency"""
        await self.update_portfolio()
        
        total_value = 0.0
        allocation = {}
        
        # Calculate total portfolio value
        for currency, balance in self.balances.items():
            if currency == self.base_currency:
                total_value += balance
            else:
                symbol = f"{currency}-{self.base_currency}"
                if symbol in self.positions:
                    total_value += self.positions[symbol].market_value
        
        if total_value <= 0:
            return {}
        
        # Calculate allocation percentages
        for currency, balance in self.balances.items():
            if currency == self.base_currency:
                allocation[currency] = (balance / total_value) * 100
            else:
                symbol = f"{currency}-{self.base_currency}"
                if symbol in self.positions:
                    allocation[currency] = (self.positions[symbol].market_value / total_value) * 100
        
        return allocation
    
    async def get_portfolio_performance(self, days: int = 30) -> Dict[str, float]:
        """Get portfolio performance metrics"""
        # This would require historical data tracking
        # For now, return basic metrics
        summary = await self.get_portfolio_summary()
        
        return {
            'total_return': summary.total_pnl,
            'total_return_percent': summary.total_pnl_percent,
            'current_value': summary.total_value,
            'positions_count': summary.positions_count,
            'cash_percentage': (summary.available_balance / summary.total_value * 100) if summary.total_value > 0 else 0
        }
    
    def set_initial_balance(self, balance: float):
        """Set initial balance for PnL calculations"""
        self.initial_balance = balance
        logger.info(f"Initial balance set to: ${balance:,.2f}")
    
    async def export_portfolio_data(self) -> Dict:
        """Export complete portfolio data"""
        summary = await self.get_portfolio_summary()
        positions = await self.get_all_positions()
        allocation = await self.get_portfolio_allocation()
        performance = await self.get_portfolio_performance()
        
        return {
            'summary': summary.to_dict(),
            'positions': [pos.to_dict() for pos in positions],
            'balances': self.balances,
            'allocation': allocation,
            'performance': performance,
            'last_update': self.last_update.isoformat() if self.last_update else None
        }
