#!/usr/bin/env python3
"""
KuCoin Integration Test - Real API Testing
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.kucoin_client import KuCoinClient, KuCoinAPIError
from src.analysis import MarketAnalyzer
from src.core.config import settings
from src.core.logger import logger


async def test_kucoin_connection():
    """Test KuCoin API connection and basic functionality"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🤖 KuCoin API Integration Test - Real Trading        ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    print(f"🔧 Configuration:")
    print(f"   API Key: {settings.kucoin_api_key[:10]}..." if settings.kucoin_api_key else "   API Key: Not configured")
    print(f"   Sandbox Mode: {settings.kucoin_sandbox}")
    print(f"   Base URL: {'Sandbox' if settings.kucoin_sandbox else 'Production'}")
    
    # Initialize KuCoin client
    client = KuCoinClient()
    
    try:
        async with client:
            print("\n✅ Connected to KuCoin API successfully")
            
            # Test 1: Server Time
            print("\n🔍 Testing Server Time...")
            server_time = await client.get_server_time()
            server_timestamp = server_time
            current_time = datetime.fromtimestamp(server_timestamp / 1000)
            print(f"   ✅ Server time: {current_time}")
            
            # Test 2: Get All Symbols
            print("\n🔍 Testing Symbol Information...")
            symbols = await client.get_symbols()
            print(f"   ✅ Available symbols: {len(symbols)}")
            
            # Find popular trading pairs
            popular_pairs = []
            for symbol in symbols:
                if symbol['symbol'] in ['BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'DOT-USDT']:
                    popular_pairs.append(symbol)
            
            print(f"   ✅ Popular pairs found: {len(popular_pairs)}")
            for pair in popular_pairs[:3]:
                print(f"      {pair['symbol']}: {pair['baseCurrency']}/{pair['quoteCurrency']}")
            
            # Test 3: Get Ticker Data
            print("\n🔍 Testing Ticker Data...")
            if popular_pairs:
                test_symbol = popular_pairs[0]['symbol']
                ticker = await client.get_ticker(test_symbol)
                
                print(f"   ✅ {test_symbol} Ticker:")
                print(f"      Last Price: ${float(ticker.get('last', 0)):,.2f}")
                print(f"      24h Change: {float(ticker.get('changeRate', 0)) * 100:+.2f}%")
                print(f"      24h Volume: {float(ticker.get('vol', 0)):,.2f}")
                print(f"      24h High: ${float(ticker.get('high', 0)):,.2f}")
                print(f"      24h Low: ${float(ticker.get('low', 0)):,.2f}")
            
            # Test 4: Get All Tickers
            print("\n🔍 Testing All Tickers...")
            all_tickers = await client.get_all_tickers()
            tickers_data = all_tickers.get('ticker', [])
            print(f"   ✅ Retrieved {len(tickers_data)} ticker data")
            
            # Show top gainers
            if tickers_data:
                gainers = sorted(
                    [t for t in tickers_data if t.get('symbol', '').endswith('-USDT')],
                    key=lambda x: float(x.get('changeRate', 0)),
                    reverse=True
                )[:5]
                
                print(f"   📈 Top 5 Gainers (USDT pairs):")
                for gainer in gainers:
                    change_rate = float(gainer.get('changeRate', 0)) * 100
                    print(f"      {gainer['symbol']}: {change_rate:+.2f}%")
            
            # Test 5: Get Order Book
            print("\n🔍 Testing Order Book...")
            if popular_pairs:
                orderbook = await client.get_orderbook(test_symbol)
                
                bids = orderbook.get('bids', [])
                asks = orderbook.get('asks', [])
                
                print(f"   ✅ {test_symbol} Order Book:")
                print(f"      Bids: {len(bids)} levels")
                print(f"      Asks: {len(asks)} levels")
                
                if bids and asks:
                    best_bid = float(bids[0][0])
                    best_ask = float(asks[0][0])
                    spread = best_ask - best_bid
                    spread_percent = (spread / best_ask) * 100
                    
                    print(f"      Best Bid: ${best_bid:,.2f}")
                    print(f"      Best Ask: ${best_ask:,.2f}")
                    print(f"      Spread: ${spread:.2f} ({spread_percent:.3f}%)")
            
            # Test 6: Get Klines Data
            print("\n🔍 Testing Klines Data...")
            if popular_pairs:
                klines = await client.get_klines(test_symbol, "1hour")
                
                print(f"   ✅ {test_symbol} Klines (1 hour):")
                print(f"      Retrieved: {len(klines)} candles")
                
                if klines:
                    # KuCoin klines format: [time, open, close, high, low, volume, turnover]
                    latest = klines[0]  # Most recent first
                    candle_time = datetime.fromtimestamp(int(latest[0]))
                    
                    print(f"      Latest candle ({candle_time}):")
                    print(f"        Open: ${float(latest[1]):,.2f}")
                    print(f"        High: ${float(latest[3]):,.2f}")
                    print(f"        Low: ${float(latest[4]):,.2f}")
                    print(f"        Close: ${float(latest[2]):,.2f}")
                    print(f"        Volume: {float(latest[5]):,.2f}")
            
            print("\n✅ All public API tests passed!")
            return True
            
    except KuCoinAPIError as e:
        print(f"❌ KuCoin API Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


async def test_kucoin_authentication():
    """Test KuCoin authenticated API calls"""
    print(f"\n🔍 Testing KuCoin Authentication...")
    print("=" * 60)
    
    if not settings.kucoin_api_key or not settings.kucoin_secret_key:
        print("⚠️  No KuCoin API credentials found")
        print("   Please set KUCOIN_API_KEY, KUCOIN_SECRET_KEY, and KUCOIN_PASSPHRASE in .env")
        return False
    
    client = KuCoinClient()
    
    try:
        async with client:
            # Test Account Info
            print("📊 Testing Account Information...")
            accounts = await client.get_account_info()
            
            print(f"   ✅ Account information retrieved")
            print(f"   📈 Total accounts: {len(accounts)}")
            
            # Show trading accounts with balance
            trading_accounts = [acc for acc in accounts if acc['type'] == 'trade' and float(acc['balance']) > 0]
            
            if trading_accounts:
                print(f"   💰 Trading accounts with balance:")
                total_value_usdt = 0
                
                for account in trading_accounts:
                    balance = float(account['balance'])
                    available = float(account['available'])
                    currency = account['currency']
                    
                    print(f"      {currency}: {balance:.8f} (Available: {available:.8f})")
                    
                    # Estimate USDT value for major currencies
                    if currency == 'USDT':
                        total_value_usdt += balance
                    elif currency in ['BTC', 'ETH', 'BNB']:
                        # Get current price to estimate value
                        try:
                            ticker = await client.get_ticker(f"{currency}-USDT")
                            price = float(ticker.get('last', 0))
                            value_usdt = balance * price
                            total_value_usdt += value_usdt
                            print(f"         ≈ ${value_usdt:.2f} USDT")
                        except:
                            pass
                
                print(f"   💵 Estimated total value: ≈ ${total_value_usdt:.2f} USDT")
            else:
                print(f"   ℹ️  No trading accounts with balance found")
            
            # Test specific currency balance
            print("\n💰 Testing Specific Currency Balance...")
            usdt_balance = await client.get_account_balance('USDT')
            
            if usdt_balance:
                balance = float(usdt_balance.get('balance', 0))
                available = float(usdt_balance.get('available', 0))
                holds = float(usdt_balance.get('holds', 0))
                
                print(f"   ✅ USDT Balance:")
                print(f"      Total: {balance:.2f} USDT")
                print(f"      Available: {available:.2f} USDT")
                print(f"      On Hold: {holds:.2f} USDT")
            else:
                print(f"   ℹ️  No USDT balance found")
            
            # Test Orders (if any)
            print("\n📋 Testing Orders...")
            try:
                active_orders = await client.get_orders(status="active")
                print(f"   ✅ Active orders: {len(active_orders)}")
                
                if active_orders:
                    for order in active_orders[:3]:  # Show first 3
                        print(f"      {order['symbol']}: {order['side']} {order['size']} @ {order['price']}")
                else:
                    print(f"   ℹ️  No active orders")
                
            except Exception as e:
                print(f"   ⚠️  Could not retrieve orders: {e}")
            
            print("\n✅ Authentication tests passed!")
            return True
            
    except KuCoinAPIError as e:
        print(f"❌ Authentication failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


async def test_kucoin_market_analysis():
    """Test market analysis with KuCoin data"""
    print(f"\n🔍 Testing Market Analysis with KuCoin Data...")
    print("=" * 60)
    
    client = KuCoinClient()
    analyzer = MarketAnalyzer()
    
    try:
        async with client:
            # Get popular symbols
            symbols = await client.get_symbols()
            popular_symbols = [s['symbol'] for s in symbols if s['symbol'] in ['BTC-USDT', 'ETH-USDT', 'ADA-USDT']]
            
            print(f"📊 Analyzing {len(popular_symbols)} popular symbols...")
            
            for symbol in popular_symbols:
                try:
                    print(f"\n📈 Analyzing {symbol}...")
                    
                    # Get historical data
                    klines = await client.get_klines(symbol, "1hour")
                    
                    if not klines or len(klines) < 50:
                        print(f"   ⚠️  Insufficient data for {symbol}")
                        continue
                    
                    # Convert KuCoin klines to DataFrame
                    # KuCoin format: [time, open, close, high, low, volume, turnover]
                    data = []
                    for kline in reversed(klines):  # Reverse to get chronological order
                        data.append({
                            'open': float(kline[1]),
                            'high': float(kline[3]),
                            'low': float(kline[4]),
                            'close': float(kline[2]),
                            'volume': float(kline[5]),
                            'quote_volume': float(kline[6]),
                            'trades_count': 100  # KuCoin doesn't provide this, use default
                        })
                    
                    import pandas as pd
                    df = pd.DataFrame(data)
                    df.index = pd.date_range(end=datetime.now(), periods=len(df), freq='H')
                    
                    print(f"   ✅ Data collected: {len(df)} periods")
                    print(f"   📊 Price range: ${df['low'].min():,.2f} - ${df['high'].max():,.2f}")
                    
                    # Analyze with our technical analysis system
                    signal = await analyzer.analyze_symbol(symbol, df)
                    
                    print(f"   🎯 Analysis Result:")
                    print(f"      Signal: {signal.signal_type}")
                    print(f"      Confidence: {signal.confidence:.1%}")
                    print(f"      Risk Level: {signal.risk_level}")
                    print(f"      Current Price: ${signal.entry_price:,.2f}")
                    
                    if signal.target_price:
                        potential_return = ((signal.target_price - signal.entry_price) / signal.entry_price) * 100
                        print(f"      Target: ${signal.target_price:,.2f} ({potential_return:+.2f}%)")
                    
                    if signal.stop_loss:
                        risk_percent = ((signal.stop_loss - signal.entry_price) / signal.entry_price) * 100
                        print(f"      Stop Loss: ${signal.stop_loss:,.2f} ({risk_percent:+.2f}%)")
                    
                    # Show active technical indicators
                    active_indicators = [name for name, tech_signal in signal.technical_signals.items() 
                                       if tech_signal.signal != 'HOLD']
                    if active_indicators:
                        print(f"      Active Indicators: {', '.join(active_indicators)}")
                    
                    print(f"      Reasoning: {signal.reasoning}")
                    
                except Exception as e:
                    print(f"   ❌ Error analyzing {symbol}: {e}")
            
            print("\n✅ Market analysis with KuCoin data completed!")
            return True
            
    except Exception as e:
        print(f"❌ Market analysis failed: {e}")
        return False


async def main():
    """Main test function"""
    try:
        # Test 1: Basic connection and public API
        success1 = await test_kucoin_connection()
        
        # Test 2: Authentication (if credentials available)
        success2 = await test_kucoin_authentication()
        
        # Test 3: Market analysis integration
        success3 = await test_kucoin_market_analysis()
        
        # Summary
        total_tests = 3
        passed_tests = sum([success1, success2, success3])
        
        print(f"\n" + "=" * 70)
        print(f"📊 KuCoin Integration Test Results: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 All KuCoin integration tests passed!")
            print("\n✨ Your KuCoin integration is working perfectly!")
            print("\n💡 You can now:")
            print("   ✅ Access real KuCoin market data")
            print("   ✅ Perform technical analysis on KuCoin pairs")
            print("   ✅ Monitor your KuCoin account")
            print("   ✅ Execute trades (if credentials provided)")
        else:
            print("⚠️  Some tests failed. Check the errors above.")
            
            if not success2:
                print("\n🔧 To enable full functionality:")
                print("   1. Add KUCOIN_PASSPHRASE to your .env file")
                print("   2. Ensure API keys have proper permissions")
                print("   3. Check if API keys are for sandbox or production")
        
        return passed_tests == total_tests
        
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        return False


if __name__ == "__main__":
    asyncio.run(main())
