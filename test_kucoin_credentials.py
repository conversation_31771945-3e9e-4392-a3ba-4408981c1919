#!/usr/bin/env python3
"""
KuCoin Credentials Test - Debug API Authentication
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.kucoin_client import KuCoinClient, KuCoinAPIError
from src.core.config import settings


async def test_credentials_step_by_step():
    """Test KuCoin credentials step by step"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🔧 KuCoin Credentials Debug Test                     ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    print("🔍 Current Configuration:")
    print(f"   API Key: {settings.kucoin_api_key}")
    print(f"   Secret Key: {settings.kucoin_secret_key}")
    print(f"   Passphrase: {settings.kucoin_passphrase}")
    print(f"   Sandbox: {settings.kucoin_sandbox}")
    
    # Test 1: Public API (no authentication)
    print("\n📊 Step 1: Testing Public API (no authentication)...")
    
    client = KuCoinClient(api_key="", secret_key="", passphrase="", sandbox=False)
    
    try:
        async with client:
            server_time = await client.get_server_time()
            print(f"   ✅ Public API works - Server time: {server_time}")
    except Exception as e:
        print(f"   ❌ Public API failed: {e}")
        return False
    
    # Test 2: Try different passphrase combinations
    print("\n🔐 Step 2: Testing different passphrase combinations...")
    
    # Common passphrase patterns
    test_passphrases = [
        settings.kucoin_api_key,  # Same as API key
        settings.kucoin_secret_key,  # Same as secret key
        "686a4e782301b10001e7457c",  # API key value
        "61718954-dc69-4b89-b21c-dff5b80fff15",  # Secret key value
        "password",  # Common default
        "123456",  # Common default
        "",  # Empty
    ]
    
    for i, passphrase in enumerate(test_passphrases, 1):
        print(f"\n   🔍 Test {i}: Trying passphrase: {passphrase[:10]}...")
        
        test_client = KuCoinClient(
            api_key=settings.kucoin_api_key,
            secret_key=settings.kucoin_secret_key,
            passphrase=passphrase,
            sandbox=False
        )
        
        try:
            async with test_client:
                # Try to get account info
                accounts = await test_client.get_account_info()
                print(f"   ✅ SUCCESS! Passphrase works: {passphrase}")
                print(f"   📊 Found {len(accounts)} accounts")
                return True, passphrase
                
        except KuCoinAPIError as e:
            if "Invalid KC-API-PASSPHRASE" in str(e):
                print(f"   ❌ Invalid passphrase")
            elif "Invalid KC-API-KEY" in str(e):
                print(f"   ❌ Invalid API key")
            elif "Invalid KC-API-SIGN" in str(e):
                print(f"   ❌ Invalid signature (secret key issue)")
            else:
                print(f"   ❌ Other error: {e}")
        except Exception as e:
            print(f"   ❌ Unexpected error: {e}")
    
    print("\n❌ None of the tested passphrases worked")
    return False, None


async def test_manual_passphrase():
    """Allow manual passphrase input"""
    print("\n🔧 Manual Passphrase Test")
    print("=" * 50)
    
    print("KuCoin API requires a PASSPHRASE that you created when generating the API keys.")
    print("This is NOT the same as your API key or secret key.")
    print("It's a custom password you set during API key creation.")
    
    # For now, let's try some common patterns
    print("\n💡 Common passphrase patterns to try:")
    print("1. A simple word like 'password', 'trading', 'bot'")
    print("2. A number sequence like '123456', '000000'")
    print("3. The same value as your API key")
    print("4. A combination of letters and numbers")
    
    # Test with empty passphrase (some exchanges allow this)
    print("\n🔍 Testing with empty passphrase...")
    
    test_client = KuCoinClient(
        api_key=settings.kucoin_api_key,
        secret_key=settings.kucoin_secret_key,
        passphrase="",
        sandbox=False
    )
    
    try:
        async with test_client:
            accounts = await test_client.get_account_info()
            print(f"   ✅ Empty passphrase works!")
            return True, ""
    except Exception as e:
        print(f"   ❌ Empty passphrase failed: {e}")
    
    return False, None


async def main():
    """Main test function"""
    try:
        # Test step by step
        success, working_passphrase = await test_credentials_step_by_step()
        
        if not success:
            success, working_passphrase = await test_manual_passphrase()
        
        if success:
            print(f"\n🎉 SUCCESS!")
            print(f"✅ Working passphrase found: {working_passphrase}")
            print(f"\n🔧 Update your .env file:")
            print(f"KUCOIN_PASSPHRASE={working_passphrase}")
        else:
            print(f"\n❌ Could not find working passphrase")
            print(f"\n🔧 Next steps:")
            print("1. Check your KuCoin account API settings")
            print("2. Verify the passphrase you set when creating API keys")
            print("3. Make sure API keys have trading permissions")
            print("4. Consider regenerating API keys if needed")
            
            print(f"\n📋 Current credentials being tested:")
            print(f"   API Key: {settings.kucoin_api_key}")
            print(f"   Secret Key: {settings.kucoin_secret_key}")
            print(f"   Passphrase: {settings.kucoin_passphrase}")
        
        return success
        
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
