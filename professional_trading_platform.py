#!/usr/bin/env python3
"""
Professional Trading Platform - Complete Trading Dashboard
Mimics TradingView, Binance, and CoinMarketCap functionality
"""

import asyncio
import sys
import os
from pathlib import Path
import json
import sqlite3
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import uuid
import threading
import time
import weakref
from collections import deque, defaultdict
import gzip
import pickle
import logging
from dataclasses import dataclass, asdict
import hashlib

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Web framework and extensions
try:
    from flask import Flask, render_template, jsonify, request, send_file, session
    from flask_socketio import SocketIO, emit, join_room, leave_room
    from flask_caching import Cache
    import redis
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

# Trading components
from src.data.kucoin_client import KuCoinClient
from src.analysis import MarketAnalyzer
from src.ai.feature_engineer import FeatureEngineer
from src.trading.order_manager import OrderManager
from src.core.logger import logger

# Data models
@dataclass
class MarketData:
    symbol: str
    price: float
    change_24h: float
    volume_24h: float
    market_cap: float
    high_24h: float
    low_24h: float
    timestamp: str

@dataclass
class TechnicalIndicators:
    rsi: float
    macd: float
    macd_signal: float
    bb_upper: float
    bb_lower: float
    sma_20: float
    sma_50: float
    ema_12: float
    ema_26: float
    volume_sma: float

@dataclass
class TradingSignal:
    symbol: str
    signal_type: str
    confidence: float
    entry_price: float
    target_price: Optional[float]
    stop_loss: Optional[float]
    risk_level: str
    reasoning: str
    timestamp: str

@dataclass
class BacktestResult:
    strategy_name: str
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    avg_trade_duration: float
    profit_factor: float


class ProfessionalTradingPlatform:
    """
    Professional Trading Platform with advanced features
    """
    
    def __init__(self):
        if not FLASK_AVAILABLE:
            print("❌ Required packages not available. Install with:")
            print("   pip install flask flask-socketio flask-caching redis scikit-learn")
            sys.exit(1)
        
        # Initialize Flask app with advanced configuration
        self.app = Flask(__name__)
        self.app.config.update({
            'SECRET_KEY': 'professional_trading_platform_secret_key',
            'CACHE_TYPE': 'redis',
            'CACHE_REDIS_URL': 'redis://localhost:6379/0',
            'CACHE_DEFAULT_TIMEOUT': 300,
            'SESSION_PERMANENT': False,
            'SESSION_TYPE': 'filesystem'
        })
        
        # Initialize cache with fallback
        try:
            self.cache = Cache(self.app)
            self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
            self.redis_available = True
        except:
            self.app.config['CACHE_TYPE'] = 'simple'
            self.cache = Cache(self.app)
            self.redis_available = False
            logger.warning("Redis not available, using simple cache")
        
        # Initialize SocketIO with advanced settings
        self.socketio = SocketIO(
            self.app,
            cors_allowed_origins="*",
            async_mode='threading',
            ping_timeout=60,
            ping_interval=25,
            logger=False,
            engineio_logger=False
        )
        
        # Initialize trading components
        self.kucoin_client = KuCoinClient()
        self.market_analyzer = MarketAnalyzer()
        self.feature_engineer = FeatureEngineer()
        self.order_manager = OrderManager(self.kucoin_client)
        
        # Initialize database
        self.db_path = "trading_platform.db"
        self._init_database()
        
        # Data storage with advanced structures
        self.market_data: Dict[str, MarketData] = {}
        self.technical_indicators: Dict[str, TechnicalIndicators] = {}
        self.trading_signals: Dict[str, TradingSignal] = {}
        self.chart_data: Dict[str, Dict] = {}
        self.portfolio_data: Dict = {}
        self.watchlist: List[str] = []
        self.alerts: deque = deque(maxlen=1000)
        self.trade_history: deque = deque(maxlen=5000)
        self.backtest_results: Dict[str, BacktestResult] = {}
        
        # Performance monitoring
        self.performance_metrics = {
            'api_calls': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'active_connections': 0,
            'data_updates': 0,
            'errors': 0,
            'uptime_start': datetime.now()
        }
        
        # WebSocket rooms for different data streams
        self.active_rooms = {
            'market_data': set(),
            'charts': set(),
            'portfolio': set(),
            'alerts': set()
        }
        
        # Background tasks
        self.background_tasks = {}
        self.running = False
        
        # Supported symbols (expandable)
        self.supported_symbols = [
            'BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'XRP-USDT',
            'SOL-USDT', 'DOT-USDT', 'DOGE-USDT', 'AVAX-USDT', 'LUNA-USDT',
            'PIXEL-USDT', 'HIGH-USDT', 'STG-USDT', 'CELR-USDT', 'VOXEL-USDT',
            'PORTAL-USDT', 'LTO-USDT', 'MATIC-USDT', 'LINK-USDT', 'UNI-USDT'
        ]
        
        # Trading strategies
        self.trading_strategies = {
            'momentum': {'enabled': False, 'params': {}},
            'mean_reversion': {'enabled': False, 'params': {}},
            'breakout': {'enabled': False, 'params': {}},
            'ai_prediction': {'enabled': False, 'params': {}}
        }
        
        # Setup routes and events
        self._setup_routes()
        self._setup_socketio_events()
        
        logger.info("Professional Trading Platform initialized successfully")
    
    def _init_database(self):
        """Initialize SQLite database for persistent storage"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create tables
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    price REAL NOT NULL,
                    change_24h REAL,
                    volume_24h REAL,
                    market_cap REAL,
                    high_24h REAL,
                    low_24h REAL,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trading_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    signal_type TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    entry_price REAL NOT NULL,
                    target_price REAL,
                    stop_loss REAL,
                    risk_level TEXT,
                    reasoning TEXT,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trade_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    price REAL NOT NULL,
                    status TEXT NOT NULL,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS backtest_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    total_return REAL NOT NULL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    win_rate REAL,
                    total_trades INTEGER,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
    
    def _setup_routes(self):
        """Setup Flask routes for the platform"""
        
        @self.app.route('/')
        def index():
            return self._render_professional_platform()
        
        @self.app.route('/api/market_data')
        @self.cache.cached(timeout=10)
        def get_market_data():
            return jsonify({
                'data': [asdict(data) for data in self.market_data.values()],
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/market_data/<symbol>')
        @self.cache.cached(timeout=5)
        def get_symbol_data(symbol):
            if symbol in self.market_data:
                return jsonify(asdict(self.market_data[symbol]))
            return jsonify({'error': 'Symbol not found'}), 404
        
        @self.app.route('/api/chart_data/<symbol>/<timeframe>')
        def get_chart_data(symbol, timeframe):
            cache_key = f"chart_{symbol}_{timeframe}"
            cached_data = self.cache.get(cache_key)
            
            if cached_data:
                self.performance_metrics['cache_hits'] += 1
                return jsonify(cached_data)
            
            self.performance_metrics['cache_misses'] += 1
            # Will be populated by background task
            return jsonify({'status': 'loading'})
        
        @self.app.route('/api/technical_indicators/<symbol>')
        @self.cache.cached(timeout=30)
        def get_technical_indicators(symbol):
            if symbol in self.technical_indicators:
                return jsonify(asdict(self.technical_indicators[symbol]))
            return jsonify({'error': 'Indicators not available'}), 404
        
        @self.app.route('/api/trading_signals')
        @self.cache.cached(timeout=60)
        def get_trading_signals():
            return jsonify({
                'signals': [asdict(signal) for signal in self.trading_signals.values()],
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/portfolio')
        @self.cache.cached(timeout=30)
        def get_portfolio():
            return jsonify(self.portfolio_data)
        
        @self.app.route('/api/performance')
        def get_performance():
            uptime = datetime.now() - self.performance_metrics['uptime_start']
            return jsonify({
                **self.performance_metrics,
                'uptime_seconds': int(uptime.total_seconds()),
                'cache_hit_rate': (
                    self.performance_metrics['cache_hits'] / 
                    max(1, self.performance_metrics['cache_hits'] + self.performance_metrics['cache_misses'])
                ) * 100
            })
        
        @self.app.route('/api/supported_symbols')
        @self.cache.cached(timeout=3600)
        def get_supported_symbols():
            return jsonify(self.supported_symbols)
        
        @self.app.route('/api/backtest/<strategy>/<symbol>')
        def run_backtest(strategy, symbol):
            # Will trigger background backtesting
            self.socketio.start_background_task(self._run_backtest_async, strategy, symbol)
            return jsonify({'status': 'started', 'message': 'Backtesting started'})
    
    def _setup_socketio_events(self):
        """Setup SocketIO events for real-time communication"""
        
        @self.socketio.on('connect')
        def handle_connect():
            session_id = request.sid
            self.performance_metrics['active_connections'] += 1
            
            emit('connected', {
                'session_id': session_id,
                'timestamp': datetime.now().isoformat(),
                'platform_status': 'operational'
            })
            
            logger.info(f"Client connected: {session_id}")
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            session_id = request.sid
            self.performance_metrics['active_connections'] -= 1
            
            # Remove from all rooms
            for room_name, room_members in self.active_rooms.items():
                room_members.discard(session_id)
            
            logger.info(f"Client disconnected: {session_id}")
        
        @self.socketio.on('join_room')
        def handle_join_room(data):
            room = data.get('room')
            if room in self.active_rooms:
                join_room(room)
                self.active_rooms[room].add(request.sid)
                emit('room_joined', {'room': room})
        
        @self.socketio.on('leave_room')
        def handle_leave_room(data):
            room = data.get('room')
            if room in self.active_rooms:
                leave_room(room)
                self.active_rooms[room].discard(request.sid)
                emit('room_left', {'room': room})
        
        @self.socketio.on('request_market_data')
        def handle_market_data_request(data):
            symbols = data.get('symbols', self.supported_symbols[:10])
            self.socketio.start_background_task(self._fetch_market_data_async, symbols)
        
        @self.socketio.on('request_chart_data')
        def handle_chart_data_request(data):
            symbol = data.get('symbol', 'BTC-USDT')
            timeframe = data.get('timeframe', '1hour')
            self.socketio.start_background_task(self._fetch_chart_data_async, symbol, timeframe)
        
        @self.socketio.on('request_technical_analysis')
        def handle_technical_analysis_request(data):
            symbol = data.get('symbol', 'BTC-USDT')
            self.socketio.start_background_task(self._analyze_symbol_async, symbol)
        
        @self.socketio.on('request_portfolio_update')
        def handle_portfolio_update_request():
            self.socketio.start_background_task(self._update_portfolio_async)
        
        @self.socketio.on('place_order')
        def handle_place_order(data):
            # Handle order placement (will be implemented in trading module)
            emit('order_status', {'status': 'received', 'data': data})
        
        @self.socketio.on('update_strategy')
        def handle_strategy_update(data):
            strategy = data.get('strategy')
            params = data.get('params', {})
            enabled = data.get('enabled', False)
            
            if strategy in self.trading_strategies:
                self.trading_strategies[strategy]['enabled'] = enabled
                self.trading_strategies[strategy]['params'] = params
                emit('strategy_updated', {'strategy': strategy, 'status': 'updated'})
        
        @self.socketio.on('add_to_watchlist')
        def handle_add_to_watchlist(data):
            symbol = data.get('symbol')
            if symbol and symbol not in self.watchlist:
                self.watchlist.append(symbol)
                emit('watchlist_updated', {'action': 'added', 'symbol': symbol})
        
        @self.socketio.on('remove_from_watchlist')
        def handle_remove_from_watchlist(data):
            symbol = data.get('symbol')
            if symbol in self.watchlist:
                self.watchlist.remove(symbol)
                emit('watchlist_updated', {'action': 'removed', 'symbol': symbol})

    async def _fetch_market_data_async(self, symbols: List[str]):
        """Fetch market data for multiple symbols asynchronously"""
        try:
            async with self.kucoin_client:
                market_data_list = []

                for symbol in symbols:
                    try:
                        self.performance_metrics['api_calls'] += 1
                        ticker = await self.kucoin_client.get_ticker(symbol)

                        if ticker:
                            market_data = MarketData(
                                symbol=symbol,
                                price=float(ticker.get('last', 0)),
                                change_24h=float(ticker.get('changeRate', 0)) * 100,
                                volume_24h=float(ticker.get('vol', 0)),
                                market_cap=0,  # Will be calculated separately
                                high_24h=float(ticker.get('high', 0)),
                                low_24h=float(ticker.get('low', 0)),
                                timestamp=datetime.now().isoformat()
                            )

                            self.market_data[symbol] = market_data
                            market_data_list.append(asdict(market_data))

                            # Cache the data
                            self.cache.set(f"market_{symbol}", asdict(market_data), timeout=10)

                        # Small delay to prevent rate limiting
                        await asyncio.sleep(0.1)

                    except Exception as e:
                        logger.error(f"Error fetching market data for {symbol}: {e}")
                        self.performance_metrics['errors'] += 1

                # Emit to all clients in market_data room
                self.socketio.emit('market_data_update', {
                    'data': market_data_list,
                    'timestamp': datetime.now().isoformat()
                }, room='market_data')

                self.performance_metrics['data_updates'] += 1
                logger.info(f"Market data updated for {len(market_data_list)} symbols")

        except Exception as e:
            logger.error(f"Market data fetch failed: {e}")
            self.performance_metrics['errors'] += 1

    async def _fetch_chart_data_async(self, symbol: str, timeframe: str):
        """Fetch chart data with technical indicators"""
        try:
            async with self.kucoin_client:
                self.performance_metrics['api_calls'] += 1

                # Determine the number of candles based on timeframe
                candle_limit = {
                    '1min': 500, '5min': 500, '15min': 500,
                    '1hour': 500, '4hour': 300, '1day': 200, '1week': 100
                }.get(timeframe, 500)

                klines = await self.kucoin_client.get_klines(symbol, timeframe, candle_limit)

                if klines and len(klines) >= 50:
                    # Convert to chart format
                    chart_data = []
                    for kline in reversed(klines):
                        chart_data.append({
                            'time': int(kline[0]) // 1000,  # Convert to seconds
                            'open': float(kline[1]),
                            'high': float(kline[3]),
                            'low': float(kline[4]),
                            'close': float(kline[2]),
                            'volume': float(kline[5])
                        })

                    # Calculate technical indicators
                    df = pd.DataFrame(chart_data)
                    df.set_index('time', inplace=True)

                    indicators = self._calculate_advanced_indicators(df)

                    # Store technical indicators
                    if indicators:
                        self.technical_indicators[symbol] = TechnicalIndicators(**indicators)

                    # Prepare response
                    response_data = {
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'candles': chart_data,
                        'indicators': indicators,
                        'timestamp': datetime.now().isoformat()
                    }

                    # Cache the data
                    cache_key = f"chart_{symbol}_{timeframe}"
                    self.cache.set(cache_key, response_data, timeout=60)

                    # Emit to clients
                    self.socketio.emit('chart_data_update', response_data, room='charts')

                    logger.info(f"Chart data updated for {symbol} ({timeframe})")

        except Exception as e:
            logger.error(f"Chart data fetch failed for {symbol}: {e}")
            self.performance_metrics['errors'] += 1

    def _calculate_advanced_indicators(self, df: pd.DataFrame) -> Dict:
        """Calculate comprehensive technical indicators"""
        try:
            indicators = {}

            # Price data
            close = df['close']
            high = df['high']
            low = df['low']
            volume = df['volume']

            # Moving Averages
            indicators['sma_20'] = close.rolling(20).mean().fillna(0).tolist()
            indicators['sma_50'] = close.rolling(50).mean().fillna(0).tolist()
            indicators['ema_12'] = close.ewm(span=12).mean().fillna(0).tolist()
            indicators['ema_26'] = close.ewm(span=26).mean().fillna(0).tolist()

            # RSI
            delta = close.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            indicators['rsi'] = rsi.fillna(50).tolist()

            # MACD
            ema_12 = close.ewm(span=12).mean()
            ema_26 = close.ewm(span=26).mean()
            macd_line = ema_12 - ema_26
            signal_line = macd_line.ewm(span=9).mean()
            histogram = macd_line - signal_line

            indicators['macd'] = macd_line.fillna(0).tolist()
            indicators['macd_signal'] = signal_line.fillna(0).tolist()
            indicators['macd_histogram'] = histogram.fillna(0).tolist()

            # Bollinger Bands
            sma_20 = close.rolling(20).mean()
            std_20 = close.rolling(20).std()
            indicators['bb_upper'] = (sma_20 + (std_20 * 2)).fillna(0).tolist()
            indicators['bb_lower'] = (sma_20 - (std_20 * 2)).fillna(0).tolist()
            indicators['bb_middle'] = sma_20.fillna(0).tolist()

            # Volume indicators
            indicators['volume_sma'] = volume.rolling(20).mean().fillna(0).tolist()

            # Stochastic Oscillator
            lowest_low = low.rolling(14).min()
            highest_high = high.rolling(14).max()
            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            indicators['stoch_k'] = k_percent.fillna(50).tolist()
            indicators['stoch_d'] = k_percent.rolling(3).mean().fillna(50).tolist()

            # Williams %R
            williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
            indicators['williams_r'] = williams_r.fillna(-50).tolist()

            # Average True Range (ATR)
            tr1 = high - low
            tr2 = abs(high - close.shift())
            tr3 = abs(low - close.shift())
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = true_range.rolling(14).mean()
            indicators['atr'] = atr.fillna(0).tolist()

            # Return latest values for current indicators
            latest_indicators = {
                'rsi': float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else 50.0,
                'macd': float(macd_line.iloc[-1]) if not pd.isna(macd_line.iloc[-1]) else 0.0,
                'macd_signal': float(signal_line.iloc[-1]) if not pd.isna(signal_line.iloc[-1]) else 0.0,
                'bb_upper': float(indicators['bb_upper'][-1]) if indicators['bb_upper'][-1] > 0 else 0.0,
                'bb_lower': float(indicators['bb_lower'][-1]) if indicators['bb_lower'][-1] > 0 else 0.0,
                'sma_20': float(indicators['sma_20'][-1]) if indicators['sma_20'][-1] > 0 else 0.0,
                'sma_50': float(indicators['sma_50'][-1]) if indicators['sma_50'][-1] > 0 else 0.0,
                'ema_12': float(indicators['ema_12'][-1]) if indicators['ema_12'][-1] > 0 else 0.0,
                'ema_26': float(indicators['ema_26'][-1]) if indicators['ema_26'][-1] > 0 else 0.0,
                'volume_sma': float(indicators['volume_sma'][-1]) if indicators['volume_sma'][-1] > 0 else 0.0
            }

            # Add chart indicators to response
            indicators['latest'] = latest_indicators

            return indicators

        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return {}

    async def _analyze_symbol_async(self, symbol: str):
        """Perform comprehensive technical analysis"""
        try:
            async with self.kucoin_client:
                # Get market data
                klines = await self.kucoin_client.get_klines(symbol, '1hour', 100)
                ticker = await self.kucoin_client.get_ticker(symbol)

                if klines and len(klines) >= 50:
                    # Convert to DataFrame
                    data = []
                    for kline in reversed(klines):
                        data.append({
                            'open': float(kline[1]),
                            'high': float(kline[3]),
                            'low': float(kline[4]),
                            'close': float(kline[2]),
                            'volume': float(kline[5])
                        })

                    df = pd.DataFrame(data)
                    df.index = pd.date_range(end=datetime.now(), periods=len(df), freq='h')

                    # Perform analysis
                    signal = await self.market_analyzer.analyze_symbol(symbol, df)

                    # Create trading signal
                    trading_signal = TradingSignal(
                        symbol=symbol,
                        signal_type=signal.signal_type,
                        confidence=signal.confidence,
                        entry_price=float(ticker.get('last', 0)),
                        target_price=signal.target_price,
                        stop_loss=signal.stop_loss,
                        risk_level=signal.risk_level,
                        reasoning=signal.reasoning,
                        timestamp=datetime.now().isoformat()
                    )

                    self.trading_signals[symbol] = trading_signal

                    # Store in database
                    self._store_trading_signal(trading_signal)

                    # Emit to clients
                    self.socketio.emit('trading_signal_update', {
                        'symbol': symbol,
                        'signal': asdict(trading_signal)
                    })

                    logger.info(f"Technical analysis completed for {symbol}: {signal.signal_type}")

        except Exception as e:
            logger.error(f"Technical analysis failed for {symbol}: {e}")
            self.performance_metrics['errors'] += 1

    async def _update_portfolio_async(self):
        """Update portfolio data with real account information"""
        try:
            async with self.kucoin_client:
                self.performance_metrics['api_calls'] += 1
                accounts = await self.kucoin_client.get_account_info()

                portfolio_summary = {
                    'total_value_usdt': 0.0,
                    'holdings': {},
                    'allocation': {},
                    'performance': {
                        'daily_pnl': 0.0,
                        'total_pnl': 0.0,
                        'best_performer': '',
                        'worst_performer': ''
                    },
                    'last_update': datetime.now().isoformat()
                }

                trading_accounts = [acc for acc in accounts if acc['type'] == 'trade' and float(acc['balance']) > 0.001]

                for account in trading_accounts:
                    currency = account['currency']
                    balance = float(account['balance'])
                    available = float(account['available'])

                    holding_data = {
                        'balance': balance,
                        'available': available,
                        'currency': currency,
                        'value_usdt': 0.0,
                        'allocation_percent': 0.0,
                        'price': 0.0,
                        'change_24h': 0.0
                    }

                    if currency == 'USDT':
                        holding_data['value_usdt'] = balance
                        holding_data['price'] = 1.0
                    else:
                        try:
                            symbol = f"{currency}-USDT"
                            ticker = await self.kucoin_client.get_ticker(symbol)
                            price = float(ticker.get('last', 0))
                            change_24h = float(ticker.get('changeRate', 0)) * 100

                            holding_data['price'] = price
                            holding_data['value_usdt'] = balance * price
                            holding_data['change_24h'] = change_24h

                        except:
                            holding_data['value_usdt'] = 0.0

                    portfolio_summary['holdings'][currency] = holding_data
                    portfolio_summary['total_value_usdt'] += holding_data['value_usdt']

                # Calculate allocations
                if portfolio_summary['total_value_usdt'] > 0:
                    for currency, holding in portfolio_summary['holdings'].items():
                        allocation = (holding['value_usdt'] / portfolio_summary['total_value_usdt']) * 100
                        holding['allocation_percent'] = allocation
                        portfolio_summary['allocation'][currency] = allocation

                self.portfolio_data = portfolio_summary

                # Cache portfolio data
                self.cache.set('portfolio_data', portfolio_summary, timeout=30)

                # Emit to clients
                self.socketio.emit('portfolio_update', portfolio_summary, room='portfolio')

                logger.info(f"Portfolio updated: ${portfolio_summary['total_value_usdt']:.2f} USDT")

        except Exception as e:
            logger.error(f"Portfolio update failed: {e}")
            self.performance_metrics['errors'] += 1

    def _store_trading_signal(self, signal: TradingSignal):
        """Store trading signal in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO trading_signals
                (symbol, signal_type, confidence, entry_price, target_price, stop_loss, risk_level, reasoning, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                signal.symbol, signal.signal_type, signal.confidence, signal.entry_price,
                signal.target_price, signal.stop_loss, signal.risk_level, signal.reasoning, signal.timestamp
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error storing trading signal: {e}")

    async def _run_backtest_async(self, strategy: str, symbol: str):
        """Run backtesting for a strategy"""
        try:
            # Get historical data (last 30 days)
            async with self.kucoin_client:
                klines = await self.kucoin_client.get_klines(symbol, '1hour', 720)  # 30 days

                if not klines or len(klines) < 100:
                    self.socketio.emit('backtest_error', {
                        'strategy': strategy,
                        'symbol': symbol,
                        'error': 'Insufficient historical data'
                    })
                    return

                # Convert to DataFrame
                data = []
                for kline in reversed(klines):
                    data.append({
                        'timestamp': datetime.fromtimestamp(int(kline[0]) / 1000),
                        'open': float(kline[1]),
                        'high': float(kline[3]),
                        'low': float(kline[4]),
                        'close': float(kline[2]),
                        'volume': float(kline[5])
                    })

                df = pd.DataFrame(data)
                df.set_index('timestamp', inplace=True)

                # Run backtest simulation
                backtest_result = self._simulate_strategy(strategy, df, symbol)

                # Store result
                self.backtest_results[f"{strategy}_{symbol}"] = backtest_result

                # Emit result to clients
                self.socketio.emit('backtest_complete', {
                    'strategy': strategy,
                    'symbol': symbol,
                    'result': asdict(backtest_result)
                })

                logger.info(f"Backtest completed for {strategy} on {symbol}")

        except Exception as e:
            logger.error(f"Backtest failed for {strategy} on {symbol}: {e}")
            self.socketio.emit('backtest_error', {
                'strategy': strategy,
                'symbol': symbol,
                'error': str(e)
            })

    def _simulate_strategy(self, strategy: str, df: pd.DataFrame, symbol: str) -> BacktestResult:
        """Simulate trading strategy on historical data"""
        try:
            # Initialize simulation variables
            initial_capital = 10000  # $10,000 starting capital
            capital = initial_capital
            position = 0  # 0 = no position, 1 = long, -1 = short
            entry_price = 0
            trades = []
            equity_curve = []

            # Calculate indicators for strategy
            df['sma_20'] = df['close'].rolling(20).mean()
            df['sma_50'] = df['close'].rolling(50).mean()
            df['rsi'] = self._calculate_rsi(df['close'])

            for i in range(50, len(df)):  # Start after indicators are calculated
                current_price = df['close'].iloc[i]
                current_time = df.index[i]

                # Strategy logic
                if strategy == 'momentum':
                    signal = self._momentum_strategy_signal(df, i)
                elif strategy == 'mean_reversion':
                    signal = self._mean_reversion_strategy_signal(df, i)
                elif strategy == 'breakout':
                    signal = self._breakout_strategy_signal(df, i)
                else:
                    signal = 0  # No signal

                # Execute trades based on signal
                if signal == 1 and position <= 0:  # Buy signal
                    if position == -1:  # Close short position
                        pnl = (entry_price - current_price) * abs(position) * (capital / entry_price)
                        capital += pnl
                        trades.append({
                            'type': 'close_short',
                            'price': current_price,
                            'time': current_time,
                            'pnl': pnl
                        })

                    # Open long position
                    position = capital / current_price
                    entry_price = current_price
                    trades.append({
                        'type': 'buy',
                        'price': current_price,
                        'time': current_time,
                        'quantity': position
                    })

                elif signal == -1 and position >= 0:  # Sell signal
                    if position > 0:  # Close long position
                        pnl = (current_price - entry_price) * position
                        capital = position * current_price
                        trades.append({
                            'type': 'sell',
                            'price': current_price,
                            'time': current_time,
                            'pnl': pnl
                        })
                        position = 0

                # Calculate current equity
                if position > 0:
                    current_equity = position * current_price
                elif position < 0:
                    current_equity = capital + (entry_price - current_price) * abs(position)
                else:
                    current_equity = capital

                equity_curve.append(current_equity)

            # Calculate performance metrics
            final_equity = equity_curve[-1] if equity_curve else initial_capital
            total_return = (final_equity - initial_capital) / initial_capital * 100

            # Calculate Sharpe ratio (simplified)
            returns = pd.Series(equity_curve).pct_change().dropna()
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0

            # Calculate max drawdown
            peak = np.maximum.accumulate(equity_curve)
            drawdown = (peak - equity_curve) / peak
            max_drawdown = np.max(drawdown) * 100

            # Calculate win rate
            profitable_trades = [t for t in trades if t.get('pnl', 0) > 0]
            win_rate = len(profitable_trades) / len(trades) * 100 if trades else 0

            # Calculate average trade duration (simplified)
            avg_trade_duration = len(df) / max(1, len(trades)) * 1  # hours

            # Calculate profit factor
            gross_profit = sum(t.get('pnl', 0) for t in trades if t.get('pnl', 0) > 0)
            gross_loss = abs(sum(t.get('pnl', 0) for t in trades if t.get('pnl', 0) < 0))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

            return BacktestResult(
                strategy_name=strategy,
                total_return=total_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                total_trades=len(trades),
                avg_trade_duration=avg_trade_duration,
                profit_factor=profit_factor
            )

        except Exception as e:
            logger.error(f"Strategy simulation failed: {e}")
            return BacktestResult(
                strategy_name=strategy,
                total_return=0.0,
                sharpe_ratio=0.0,
                max_drawdown=0.0,
                win_rate=0.0,
                total_trades=0,
                avg_trade_duration=0.0,
                profit_factor=0.0
            )

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def _momentum_strategy_signal(self, df: pd.DataFrame, i: int) -> int:
        """Momentum strategy signal generation"""
        if i < 50:
            return 0

        current_price = df['close'].iloc[i]
        sma_20 = df['sma_20'].iloc[i]
        sma_50 = df['sma_50'].iloc[i]
        rsi = df['rsi'].iloc[i]

        # Buy signal: price above SMA20, SMA20 above SMA50, RSI not overbought
        if current_price > sma_20 and sma_20 > sma_50 and rsi < 70:
            return 1

        # Sell signal: price below SMA20, SMA20 below SMA50, RSI not oversold
        elif current_price < sma_20 and sma_20 < sma_50 and rsi > 30:
            return -1

        return 0

    def _mean_reversion_strategy_signal(self, df: pd.DataFrame, i: int) -> int:
        """Mean reversion strategy signal generation"""
        if i < 50:
            return 0

        rsi = df['rsi'].iloc[i]

        # Buy signal: RSI oversold
        if rsi < 30:
            return 1

        # Sell signal: RSI overbought
        elif rsi > 70:
            return -1

        return 0

    def _breakout_strategy_signal(self, df: pd.DataFrame, i: int) -> int:
        """Breakout strategy signal generation"""
        if i < 20:
            return 0

        current_price = df['close'].iloc[i]
        high_20 = df['high'].iloc[i-20:i].max()
        low_20 = df['low'].iloc[i-20:i].min()

        # Buy signal: price breaks above 20-period high
        if current_price > high_20:
            return 1

        # Sell signal: price breaks below 20-period low
        elif current_price < low_20:
            return -1

        return 0

    def start_background_tasks(self):
        """Start all background tasks"""
        self.running = True

        # Market data update task
        def market_data_loop():
            while self.running:
                try:
                    asyncio.run(self._fetch_market_data_async(self.supported_symbols[:10]))
                    time.sleep(30)  # Update every 30 seconds
                except Exception as e:
                    logger.error(f"Market data loop error: {e}")
                    time.sleep(60)

        # Portfolio update task
        def portfolio_loop():
            while self.running:
                try:
                    asyncio.run(self._update_portfolio_async())
                    time.sleep(60)  # Update every minute
                except Exception as e:
                    logger.error(f"Portfolio loop error: {e}")
                    time.sleep(120)

        # Technical analysis task
        def analysis_loop():
            while self.running:
                try:
                    for symbol in self.supported_symbols[:5]:  # Analyze top 5 symbols
                        asyncio.run(self._analyze_symbol_async(symbol))
                        time.sleep(10)  # 10 seconds between symbols
                    time.sleep(300)  # 5 minutes between full cycles
                except Exception as e:
                    logger.error(f"Analysis loop error: {e}")
                    time.sleep(300)

        # Start background threads
        self.background_tasks['market_data'] = threading.Thread(target=market_data_loop, daemon=True)
        self.background_tasks['portfolio'] = threading.Thread(target=portfolio_loop, daemon=True)
        self.background_tasks['analysis'] = threading.Thread(target=analysis_loop, daemon=True)

        for task in self.background_tasks.values():
            task.start()

        logger.info("Background tasks started")

    def stop_background_tasks(self):
        """Stop all background tasks"""
        self.running = False
        for task in self.background_tasks.values():
            if task.is_alive():
                task.join(timeout=5)
        logger.info("Background tasks stopped")

    def _render_professional_platform(self):
        """Render the professional trading platform HTML"""
        html_template = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏆 منصة التداول الاحترافية - بوت التداول الذكي</title>

    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.min.js" as="script">
    <link rel="preload" href="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js" as="script">

    <!-- Core libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.min.js"></script>
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        /* Professional Trading Platform Styles */
        :root {
            --primary-bg: #0a0e27;
            --secondary-bg: #1a1a2e;
            --accent-bg: #16213e;
            --card-bg: rgba(255, 255, 255, 0.1);
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --accent-color: #667eea;
            --success-color: #4caf50;
            --danger-color: #f44336;
            --warning-color: #ff9800;
            --border-color: rgba(255, 255, 255, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--accent-bg) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--accent-color) 0%, #764ba2 100%);
            padding: 15px 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-left p {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.1);
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Navigation */
        .nav-container {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 70px;
            z-index: 999;
        }

        .nav-tabs {
            display: flex;
            justify-content: center;
            padding: 10px;
            gap: 5px;
            flex-wrap: wrap;
        }

        .nav-tab {
            padding: 10px 20px;
            background: transparent;
            border: 2px solid var(--border-color);
            color: var(--text-primary);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-tab.active,
        .nav-tab:hover {
            background: linear-gradient(135deg, var(--accent-color) 0%, #764ba2 100%);
            border-color: var(--accent-color);
            transform: translateY(-2px);
        }

        /* Main container */
        .main-container {
            display: flex;
            height: calc(100vh - 140px);
        }

        /* Sidebar */
        .sidebar {
            width: 300px;
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border-right: 1px solid var(--border-color);
            overflow-y: auto;
            padding: 20px;
        }

        .sidebar-section {
            margin-bottom: 25px;
        }

        .sidebar-section h3 {
            color: var(--accent-color);
            margin-bottom: 15px;
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Content area */
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .tab-content {
            display: none;
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .tab-content.active {
            display: flex;
            flex-direction: column;
        }

        /* Cards */
        .card {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            margin-bottom: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            color: var(--accent-color);
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Grid layouts */
        .grid {
            display: grid;
            gap: 20px;
        }

        .grid-2 { grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .grid-4 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }

        /* Chart container */
        .chart-container {
            height: 500px;
            background: rgba(0,0,0,0.3);
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
        }

        .chart-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .timeframe-buttons {
            display: flex;
            gap: 5px;
        }

        .timeframe-btn {
            padding: 6px 12px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.2s ease;
        }

        .timeframe-btn.active,
        .timeframe-btn:hover {
            background: var(--accent-color);
            border-color: var(--accent-color);
        }

        /* Tables */
        .table-container {
            overflow-x: auto;
            border-radius: 8px;
            background: rgba(0,0,0,0.2);
            max-height: 600px;
            overflow-y: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.9em;
        }

        .table th {
            background: var(--card-bg);
            font-weight: bold;
            color: var(--accent-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tr:hover {
            background: rgba(255,255,255,0.05);
        }

        /* Buttons */
        .btn {
            background: linear-gradient(135deg, var(--accent-color) 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-success { background: linear-gradient(135deg, var(--success-color) 0%, #45a049 100%); }
        .btn-danger { background: linear-gradient(135deg, var(--danger-color) 0%, #d32f2f 100%); }
        .btn-warning { background: linear-gradient(135deg, var(--warning-color) 0%, #f57c00 100%); }

        /* Metrics */
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 6px;
            margin: 8px 0;
            transition: background 0.3s ease;
        }

        .metric:hover {
            background: rgba(255,255,255,0.1);
        }

        .metric-value {
            font-weight: bold;
        }

        .metric-positive { color: var(--success-color); }
        .metric-negative { color: var(--danger-color); }
        .metric-neutral { color: var(--warning-color); }

        /* Signal indicators */
        .signal-buy { color: var(--success-color); font-weight: bold; }
        .signal-sell { color: var(--danger-color); font-weight: bold; }
        .signal-hold { color: var(--warning-color); font-weight: bold; }

        /* Watchlist */
        .watchlist-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 6px;
            margin: 5px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .watchlist-item:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }

        /* Search */
        .search-container {
            position: relative;
            margin-bottom: 15px;
        }

        .search-input {
            width: 100%;
            padding: 10px 15px;
            background: rgba(255,255,255,0.1);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 0.9em;
        }

        .search-input::placeholder {
            color: var(--text-secondary);
        }

        /* Responsive design */
        @media (max-width: 1024px) {
            .main-container {
                flex-direction: column;
                height: auto;
            }

            .sidebar {
                width: 100%;
                height: auto;
            }

            .grid-2, .grid-3, .grid-4 {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 10px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .chart-container {
                height: 300px;
            }
        }

        /* Loading states */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px;
            color: var(--text-secondary);
        }

        .spinner {
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid var(--accent-color);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, var(--success-color) 0%, #45a049 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            max-width: 350px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background: linear-gradient(135deg, var(--danger-color) 0%, #d32f2f 100%);
        }

        .notification.warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #f57c00 100%);
        }
    </style>
</head>
<body>
    <!-- Notification -->
    <div id="notification" class="notification">
        <span id="notification-text"></span>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <h1><i class="fas fa-trophy"></i> منصة التداول الاحترافية</h1>
            <p>نظام تداول متقدم يحاكي TradingView و Binance مع ذكاء اصطناعي</p>
        </div>
        <div class="header-right">
            <div class="connection-status">
                <div class="status-dot" id="connection-dot"></div>
                <span id="connection-text">متصل</span>
            </div>
            <div class="connection-status">
                <i class="fas fa-chart-line"></i>
                <span id="active-symbols">0 رموز نشطة</span>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">
                <i class="fas fa-tachometer-alt"></i> نظرة عامة
            </button>
            <button class="nav-tab" onclick="showTab('charts')">
                <i class="fas fa-chart-area"></i> الرسوم البيانية
            </button>
            <button class="nav-tab" onclick="showTab('market')">
                <i class="fas fa-coins"></i> السوق
            </button>
            <button class="nav-tab" onclick="showTab('portfolio')">
                <i class="fas fa-wallet"></i> المحفظة
            </button>
            <button class="nav-tab" onclick="showTab('trading')">
                <i class="fas fa-robot"></i> التداول الآلي
            </button>
            <button class="nav-tab" onclick="showTab('backtest')">
                <i class="fas fa-history"></i> اختبار الاستراتيجيات
            </button>
            <button class="nav-tab" onclick="showTab('analysis')">
                <i class="fas fa-brain"></i> التحليل المتقدم
            </button>
        </div>
    </div>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Watchlist -->
            <div class="sidebar-section">
                <h3><i class="fas fa-star"></i> قائمة المراقبة</h3>
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="البحث عن عملة..." id="symbol-search">
                </div>
                <div id="watchlist-container">
                    <div class="loading">
                        <div class="spinner"></div>
                        جاري التحميل...
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="sidebar-section">
                <h3><i class="fas fa-chart-bar"></i> إحصائيات سريعة</h3>
                <div class="metric">
                    <span>BTC-USDT</span>
                    <span class="metric-value" id="btc-quick">$0</span>
                </div>
                <div class="metric">
                    <span>ETH-USDT</span>
                    <span class="metric-value" id="eth-quick">$0</span>
                </div>
                <div class="metric">
                    <span>إجمالي المحفظة</span>
                    <span class="metric-value metric-positive" id="portfolio-quick">$0</span>
                </div>
            </div>

            <!-- Trading Controls -->
            <div class="sidebar-section">
                <h3><i class="fas fa-cogs"></i> التحكم السريع</h3>
                <button class="btn" onclick="updateAllData()">
                    <i class="fas fa-sync"></i> تحديث البيانات
                </button>
                <button class="btn btn-success" onclick="startAutoTrading()">
                    <i class="fas fa-play"></i> تشغيل التداول
                </button>
                <button class="btn btn-danger" onclick="stopAutoTrading()">
                    <i class="fas fa-stop"></i> إيقاف التداول
                </button>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <div class="grid grid-4">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-server"></i> حالة النظام
                            </div>
                        </div>
                        <div class="metric">
                            <span>KuCoin API</span>
                            <span class="metric-value metric-positive" id="api-status">متصل</span>
                        </div>
                        <div class="metric">
                            <span>WebSocket</span>
                            <span class="metric-value metric-positive" id="ws-status">نشط</span>
                        </div>
                        <div class="metric">
                            <span>استعلامات API</span>
                            <span class="metric-value" id="api-calls">0</span>
                        </div>
                        <div class="metric">
                            <span>Cache Hit Rate</span>
                            <span class="metric-value metric-positive" id="cache-rate">0%</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-chart-line"></i> أداء السوق
                            </div>
                        </div>
                        <div class="metric">
                            <span>الرابحون اليوم</span>
                            <span class="metric-value metric-positive" id="gainers-count">0</span>
                        </div>
                        <div class="metric">
                            <span>الخاسرون اليوم</span>
                            <span class="metric-value metric-negative" id="losers-count">0</span>
                        </div>
                        <div class="metric">
                            <span>أعلى حجم</span>
                            <span class="metric-value" id="highest-volume">-</span>
                        </div>
                        <div class="metric">
                            <span>أكبر تغيير</span>
                            <span class="metric-value" id="biggest-change">-</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-wallet"></i> المحفظة
                            </div>
                        </div>
                        <div class="metric">
                            <span>القيمة الإجمالية</span>
                            <span class="metric-value metric-positive" id="total-value">$0</span>
                        </div>
                        <div class="metric">
                            <span>عدد الأصول</span>
                            <span class="metric-value" id="assets-count">0</span>
                        </div>
                        <div class="metric">
                            <span>أفضل أداء</span>
                            <span class="metric-value metric-positive" id="best-performer">-</span>
                        </div>
                        <div class="metric">
                            <span>P&L اليومي</span>
                            <span class="metric-value" id="daily-pnl">$0</span>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-robot"></i> التداول الآلي
                            </div>
                        </div>
                        <div class="metric">
                            <span>حالة النظام</span>
                            <span class="metric-value metric-warning" id="auto-trading-status">متوقف</span>
                        </div>
                        <div class="metric">
                            <span>الاستراتيجيات النشطة</span>
                            <span class="metric-value" id="active-strategies">0</span>
                        </div>
                        <div class="metric">
                            <span>الصفقات اليوم</span>
                            <span class="metric-value" id="trades-today">0</span>
                        </div>
                        <div class="metric">
                            <span>معدل النجاح</span>
                            <span class="metric-value metric-positive" id="success-rate">0%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Tab -->
            <div id="charts" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-chart-area"></i> الرسوم البيانية المتقدمة
                        </div>
                        <div class="chart-controls">
                            <select id="chart-symbol" onchange="changeChartSymbol()">
                                <option value="BTC-USDT">BTC-USDT</option>
                                <option value="ETH-USDT">ETH-USDT</option>
                                <option value="PIXEL-USDT">PIXEL-USDT</option>
                            </select>

                            <div class="timeframe-buttons">
                                <button class="timeframe-btn" onclick="changeTimeframe('1min')">1م</button>
                                <button class="timeframe-btn" onclick="changeTimeframe('5min')">5م</button>
                                <button class="timeframe-btn" onclick="changeTimeframe('15min')">15م</button>
                                <button class="timeframe-btn active" onclick="changeTimeframe('1hour')">1س</button>
                                <button class="timeframe-btn" onclick="changeTimeframe('4hour')">4س</button>
                                <button class="timeframe-btn" onclick="changeTimeframe('1day')">1ي</button>
                            </div>

                            <div>
                                <button class="btn" onclick="toggleIndicators()">
                                    <i class="fas fa-cogs"></i> المؤشرات
                                </button>
                                <button class="btn" onclick="resetChart()">
                                    <i class="fas fa-redo"></i> إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="chart-container" id="main-chart"></div>
                    <div class="chart-container" id="volume-chart" style="height: 150px;"></div>
                </div>
            </div>

            <!-- Market Tab -->
            <div id="market" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-coins"></i> جدول العملات الشامل
                        </div>
                        <div>
                            <button class="btn" onclick="refreshMarketData()">
                                <i class="fas fa-sync"></i> تحديث
                            </button>
                            <button class="btn" onclick="exportMarketData()">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="table" id="market-table">
                            <thead>
                                <tr>
                                    <th>العملة</th>
                                    <th>السعر</th>
                                    <th>التغيير 24س</th>
                                    <th>الحجم 24س</th>
                                    <th>القيمة السوقية</th>
                                    <th>RSI</th>
                                    <th>MACD</th>
                                    <th>الإشارة</th>
                                    <th>الثقة</th>
                                    <th>الإجراء</th>
                                </tr>
                            </thead>
                            <tbody id="market-table-body">
                                <tr>
                                    <td colspan="10" class="loading">
                                        <div class="spinner"></div>
                                        جاري تحميل بيانات السوق...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Portfolio Tab -->
            <div id="portfolio" class="tab-content">
                <div class="grid grid-2">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-pie-chart"></i> توزيع المحفظة
                            </div>
                        </div>
                        <div class="chart-container" id="portfolio-chart" style="height: 300px;"></div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-chart-line"></i> أداء المحفظة
                            </div>
                        </div>
                        <div class="chart-container" id="performance-chart" style="height: 300px;"></div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-list"></i> تفاصيل المحفظة
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="table" id="portfolio-table">
                            <thead>
                                <tr>
                                    <th>العملة</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>القيمة</th>
                                    <th>التوزيع</th>
                                    <th>P&L</th>
                                    <th>التغيير 24س</th>
                                    <th>الإجراء</th>
                                </tr>
                            </thead>
                            <tbody id="portfolio-table-body">
                                <tr>
                                    <td colspan="8" class="loading">
                                        <div class="spinner"></div>
                                        جاري تحميل بيانات المحفظة...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Professional Trading Platform JavaScript
        let socket;
        let chart;
        let volumeChart;
        let currentSymbol = 'BTC-USDT';
        let currentTimeframe = '1hour';
        let isConnected = false;
        let marketData = {};
        let portfolioData = {};

        // Initialize the platform
        function initializePlatform() {
            initializeSocket();
            initializeCharts();
            setupEventListeners();
            requestInitialData();
        }

        // Initialize Socket.IO connection
        function initializeSocket() {
            socket = io({
                transports: ['websocket'],
                upgrade: true,
                timeout: 20000
            });

            socket.on('connect', function() {
                isConnected = true;
                updateConnectionStatus(true);
                showNotification('تم الاتصال بالمنصة الاحترافية', 'success');

                // Join rooms
                socket.emit('join_room', {room: 'market_data'});
                socket.emit('join_room', {room: 'charts'});
                socket.emit('join_room', {room: 'portfolio'});
            });

            socket.on('disconnect', function() {
                isConnected = false;
                updateConnectionStatus(false);
                showNotification('انقطع الاتصال بالمنصة', 'error');
            });

            socket.on('market_data_update', function(data) {
                updateMarketData(data);
            });

            socket.on('chart_data_update', function(data) {
                updateChart(data);
            });

            socket.on('portfolio_update', function(data) {
                updatePortfolioData(data);
            });

            socket.on('trading_signal_update', function(data) {
                updateTradingSignal(data);
            });
        }

        // Initialize charts
        function initializeCharts() {
            // Main price chart
            chart = LightweightCharts.createChart(document.getElementById('main-chart'), {
                width: document.getElementById('main-chart').clientWidth,
                height: 500,
                layout: {
                    backgroundColor: 'rgba(0, 0, 0, 0)',
                    textColor: '#ffffff',
                },
                grid: {
                    vertLines: { color: 'rgba(255, 255, 255, 0.1)' },
                    horzLines: { color: 'rgba(255, 255, 255, 0.1)' },
                },
                crosshair: { mode: LightweightCharts.CrosshairMode.Normal },
                rightPriceScale: { borderColor: 'rgba(255, 255, 255, 0.2)' },
                timeScale: { borderColor: 'rgba(255, 255, 255, 0.2)' },
            });

            // Volume chart
            volumeChart = LightweightCharts.createChart(document.getElementById('volume-chart'), {
                width: document.getElementById('volume-chart').clientWidth,
                height: 150,
                layout: {
                    backgroundColor: 'rgba(0, 0, 0, 0)',
                    textColor: '#ffffff',
                },
                grid: {
                    vertLines: { color: 'rgba(255, 255, 255, 0.1)' },
                    horzLines: { color: 'rgba(255, 255, 255, 0.1)' },
                },
                rightPriceScale: { borderColor: 'rgba(255, 255, 255, 0.2)' },
                timeScale: { borderColor: 'rgba(255, 255, 255, 0.2)', visible: false },
            });

            // Add series
            window.candlestickSeries = chart.addCandlestickSeries({
                upColor: '#4caf50',
                downColor: '#f44336',
                borderDownColor: '#f44336',
                borderUpColor: '#4caf50',
                wickDownColor: '#f44336',
                wickUpColor: '#4caf50',
            });

            window.volumeSeries = volumeChart.addHistogramSeries({
                color: '#26a69a',
                priceFormat: { type: 'volume' },
            });

            // Add indicators
            window.sma20Series = chart.addLineSeries({
                color: '#ff9800',
                lineWidth: 2,
                title: 'SMA 20',
            });

            window.sma50Series = chart.addLineSeries({
                color: '#2196f3',
                lineWidth: 2,
                title: 'SMA 50',
            });
        }

        // Tab management
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            if (tabName === 'charts' && chart) {
                setTimeout(() => {
                    chart.resize(document.getElementById('main-chart').clientWidth, 500);
                    volumeChart.resize(document.getElementById('volume-chart').clientWidth, 150);
                }, 100);
            }
        }

        // Request initial data
        function requestInitialData() {
            if (socket && isConnected) {
                socket.emit('request_market_data', {symbols: ['BTC-USDT', 'ETH-USDT', 'PIXEL-USDT', 'HIGH-USDT', 'STG-USDT']});
                socket.emit('request_portfolio_update');
                socket.emit('request_chart_data', {symbol: currentSymbol, timeframe: currentTimeframe});
            }
        }

        // Utility functions
        function updateConnectionStatus(connected) {
            const dot = document.getElementById('connection-dot');
            const text = document.getElementById('connection-text');

            if (connected) {
                dot.style.background = '#4caf50';
                text.textContent = 'متصل';
            } else {
                dot.style.background = '#f44336';
                text.textContent = 'منقطع';
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const text = document.getElementById('notification-text');

            text.textContent = message;
            notification.className = `notification show ${type}`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 4000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializePlatform();
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (chart && volumeChart) {
                chart.resize(document.getElementById('main-chart').clientWidth, 500);
                volumeChart.resize(document.getElementById('volume-chart').clientWidth, 150);
            }
        });
    </script>
</body>
</html>
        """
        return html_template

    def run(self, host='127.0.0.1', port=5004, debug=False):
        """Run the professional trading platform"""
        print(f"""
        ╔══════════════════════════════════════════════════════════════╗
        ║                                                              ║
        ║        🏆 منصة التداول الاحترافية - بوت التداول الذكي      ║
        ║                                                              ║
        ╚══════════════════════════════════════════════════════════════╝

        🌐 رابط المنصة الاحترافية: http://{host}:{port}
        🚀 بدء تشغيل المنصة المتقدمة...

        🏆 الميزات الاحترافية:
        📊 رسوم بيانية تفاعلية (TradingView Style)
        💹 جداول عملات شاملة (CoinMarketCap Style)
        🤖 نظام تداول آلي متقدم
        📈 اختبار الاستراتيجيات (Backtesting)
        🧠 ذكاء اصطناعي وتحليل متقدم
        🔄 تحديثات فورية بـ WebSocket
        💼 إدارة محفظة احترافية
        📱 تصميم متجاوب ومتقدم
        """)

        # Start background tasks
        self.start_background_tasks()

        try:
            self.socketio.run(self.app, host=host, port=port, debug=debug)
        finally:
            self.stop_background_tasks()


def main():
    """Main function"""
    try:
        platform = ProfessionalTradingPlatform()
        platform.run(host='0.0.0.0', port=5004, debug=False)

    except KeyboardInterrupt:
        print("\n👋 تم إيقاف المنصة الاحترافية بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل المنصة الاحترافية: {e}")
        print("\n💡 لتثبيت المتطلبات:")
        print("   pip install flask flask-socketio flask-caching redis scikit-learn")


if __name__ == "__main__":
    main()
