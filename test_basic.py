#!/usr/bin/env python3
"""
Basic test script to verify core functionality
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from src.data.binance_client import BinanceClient, BinanceAPIError
    from src.core.config import settings
    from src.core.logger import logger
    print("✅ All imports successful")
except Exception as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


async def test_configuration():
    """Test configuration loading"""
    print("🔍 Testing Configuration...")
    print("=" * 40)
    
    try:
        print(f"✅ App Name: {settings.app_name}")
        print(f"✅ Debug Mode: {settings.debug}")
        print(f"✅ Testnet Mode: {settings.binance_testnet}")
        print(f"✅ Trading Pairs: {len(settings.trading_pairs_list)} configured")
        print(f"   Sample pairs: {', '.join(settings.trading_pairs_list[:3])}")
        print(f"✅ Database URL: {settings.database_url}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


async def test_binance_public_api():
    """Test Binance public API without authentication"""
    print("\n🔍 Testing Binance Public API...")
    print("=" * 40)
    
    try:
        client = BinanceClient(testnet=True)
        
        async with client:
            print("✅ Connection established")
            
            # Test server time
            server_time = await client.get_server_time()
            print(f"✅ Server time: {server_time['serverTime']}")
            
            # Test exchange info
            exchange_info = await client.get_exchange_info()
            symbols_count = len(exchange_info.get('symbols', []))
            print(f"✅ Exchange info: {symbols_count} symbols available")
            
            # Test ticker for BTCUSDT
            ticker = await client.get_symbol_ticker("BTCUSDT")
            price = float(ticker['lastPrice'])
            change = float(ticker['priceChangePercent'])
            print(f"✅ BTCUSDT: ${price:,.2f} ({change:+.2f}%)")
            
            # Test klines
            klines = await client.get_klines("BTCUSDT", "1h", limit=5)
            print(f"✅ Klines data: {len(klines)} candles retrieved")
            
            if klines:
                latest = klines[-1]
                ohlc = f"O:{float(latest[1]):.0f} H:{float(latest[2]):.0f} L:{float(latest[3]):.0f} C:{float(latest[4]):.0f}"
                print(f"   Latest candle: {ohlc}")
            
            return True
            
    except BinanceAPIError as e:
        print(f"❌ Binance API Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


async def test_data_collector_basic():
    """Test basic data collector functionality"""
    print("\n🔍 Testing Data Collector (Basic)...")
    print("=" * 40)
    
    try:
        from src.data.data_collector import DataCollector
        
        binance_client = BinanceClient(testnet=True)
        collector = DataCollector(binance_client)
        # Disable database and Redis for basic test
        collector.use_database = False
        collector.use_redis = False

        print("✅ Data collector initialized")

        # Connect the client first
        await binance_client.connect()

        # Test ticker collection
        tickers = await collector.collect_all_tickers()
        if tickers:
            print(f"✅ Collected ticker data for {len(tickers)} symbols")
            
            # Show some examples
            sample_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
            for symbol in sample_symbols:
                if symbol in tickers:
                    ticker = tickers[symbol]
                    print(f"   {symbol}: ${ticker.last_price:.2f} ({ticker.price_change_percent:+.2f}%)")
        else:
            print("❌ No ticker data collected")
            return False
        
        # Test klines collection
        klines_data = await collector.collect_klines_batch('1h')
        if klines_data:
            print(f"✅ Collected klines for {len(klines_data)} symbols")
            
            # Show sample
            for symbol, df in list(klines_data.items())[:2]:
                if not df.empty:
                    latest_close = df['close'].iloc[-1]
                    print(f"   {symbol}: {len(df)} candles, latest close ${latest_close:.2f}")
        else:
            print("❌ No klines data collected")
            return False
        
        # Test cached data retrieval
        cached_price = await collector.get_latest_price("BTCUSDT")
        if cached_price:
            print(f"✅ Cached BTC price: ${cached_price:.2f}")

        # Disconnect the client
        await binance_client.disconnect()

        return True
        
    except Exception as e:
        print(f"❌ Data collector error: {e}")
        return False


async def test_logging_system():
    """Test logging system"""
    print("\n🔍 Testing Logging System...")
    print("=" * 40)
    
    try:
        from src.core.logger import logger, trading_logger
        
        # Test basic logging
        logger.info("Test info message")
        logger.warning("Test warning message")
        logger.success("Test success message")
        
        # Test trading logger
        trading_logger.signal_generated(
            symbol="TESTUSDT",
            signal="BUY",
            confidence=0.85,
            indicators={"rsi": 30, "macd": 0.5}
        )
        
        print("✅ Logging system working")
        return True
        
    except Exception as e:
        print(f"❌ Logging system error: {e}")
        return False


async def main():
    """Main test function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🤖 AI Crypto Trading Bot - Basic Test                ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Configuration
    if await test_configuration():
        tests_passed += 1
    
    # Test 2: Binance API
    if await test_binance_public_api():
        tests_passed += 1
    
    # Test 3: Data Collector
    if await test_data_collector_basic():
        tests_passed += 1
    
    # Test 4: Logging
    if await test_logging_system():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All basic tests passed!")
        print("\n✨ Your AI Crypto Trading Bot core system is working!")
        print("\n💡 Next steps:")
        print("   1. Add your Binance API keys to .env for authenticated features")
        print("   2. Install Redis and PostgreSQL for full functionality")
        print("   3. Run the complete system: python main.py")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return tests_passed == total_tests


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
