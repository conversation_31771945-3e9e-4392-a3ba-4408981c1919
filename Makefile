# AI Crypto Trading Bot - Makefile

.PHONY: help install install-dev setup test test-unit test-integration lint format clean run docker-build docker-run docker-stop logs

# Default target
help:
	@echo "AI Crypto Trading Bot - Available Commands:"
	@echo ""
	@echo "Setup & Installation:"
	@echo "  install          Install production dependencies"
	@echo "  install-dev      Install development dependencies"
	@echo "  setup            Setup project (create .env, install deps)"
	@echo ""
	@echo "Development:"
	@echo "  run              Run the trading bot"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  lint             Run code linting"
	@echo "  format           Format code with black"
	@echo "  clean            Clean temporary files"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build     Build Docker image"
	@echo "  docker-run       Run with Docker Compose"
	@echo "  docker-stop      Stop Docker containers"
	@echo "  logs             View Docker logs"
	@echo ""

# Installation
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements.txt
	pip install -e .[dev]

setup:
	@echo "Setting up AI Crypto Trading Bot..."
	@if [ ! -f .env ]; then cp .env.example .env; echo "Created .env file from template"; fi
	@mkdir -p logs data config
	$(MAKE) install-dev
	@echo "Setup complete! Please edit .env file with your API keys."

# Testing
test:
	pytest

test-unit:
	pytest -m "unit"

test-integration:
	pytest -m "integration"

test-coverage:
	pytest --cov=src --cov-report=html --cov-report=term

# Code Quality
lint:
	flake8 src tests
	mypy src

format:
	black src tests
	isort src tests

format-check:
	black --check src tests
	isort --check-only src tests

# Cleaning
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

# Running
run:
	python main.py

run-dev:
	python main.py --debug

# Docker
docker-build:
	docker build -t ai-crypto-trading-bot .

docker-run:
	docker-compose up -d

docker-run-dev:
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

docker-stop:
	docker-compose down

docker-clean:
	docker-compose down -v
	docker system prune -f

logs:
	docker-compose logs -f trading-bot

# Database
db-migrate:
	alembic upgrade head

db-revision:
	alembic revision --autogenerate -m "$(msg)"

db-reset:
	alembic downgrade base
	alembic upgrade head

# Monitoring
monitor:
	docker-compose --profile monitoring up -d

# Security
security-check:
	safety check
	bandit -r src/

# Documentation
docs:
	cd docs && make html

docs-serve:
	cd docs/_build/html && python -m http.server 8080

# Release
release-patch:
	bump2version patch

release-minor:
	bump2version minor

release-major:
	bump2version major

# Backup
backup-data:
	@echo "Creating backup of data directory..."
	tar -czf backup-$(shell date +%Y%m%d-%H%M%S).tar.gz data/ logs/ config/

# Environment
check-env:
	@echo "Checking environment variables..."
	@python -c "from src.core.config import settings; print('✅ Configuration loaded successfully')"

# Pre-commit hooks
install-hooks:
	pre-commit install

run-hooks:
	pre-commit run --all-files
