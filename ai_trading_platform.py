#!/usr/bin/env python3
"""
AI-Powered Smart Trading Platform
Advanced Automated Trading System with Machine Learning
"""

import asyncio
import sys
import os
from pathlib import Path
import json
import sqlite3
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import uuid
import threading
import time
import logging
from dataclasses import dataclass, asdict
import pickle
import joblib
from collections import deque, defaultdict
import warnings
warnings.filterwarnings('ignore')

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Machine Learning Libraries
try:
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.model_selection import train_test_split, GridSearchCV
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.metrics import classification_report, accuracy_score
    import xgboost as xgb
    ML_AVAILABLE = True
    TENSORFLOW_AVAILABLE = False
except ImportError:
    ML_AVAILABLE = False
    TENSORFLOW_AVAILABLE = False

# Web framework
try:
    from flask import Flask, render_template, jsonify, request
    from flask_socketio import SocketIO, emit
    from flask_caching import Cache
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

# Trading components
from src.data.kucoin_client import KuCoinClient
from src.analysis import MarketAnalyzer
from src.ai.feature_engineer import FeatureEngineer
from src.trading.order_manager import OrderManager
from src.core.logger import logger

# AI Trading Models
@dataclass
class TradingSignal:
    symbol: str
    signal_type: str  # BUY, SELL, HOLD
    confidence: float
    entry_price: float
    target_price: Optional[float]
    stop_loss: Optional[float]
    risk_level: str
    reasoning: str
    model_used: str
    timestamp: str

@dataclass
class MarketCondition:
    trend: str  # BULLISH, BEARISH, SIDEWAYS
    volatility: float
    volume_trend: str
    sentiment: float
    confidence: float
    timestamp: str

@dataclass
class TradeExecution:
    trade_id: str
    symbol: str
    side: str  # BUY, SELL
    quantity: float
    price: float
    status: str  # PENDING, FILLED, CANCELLED
    execution_time: float  # milliseconds
    slippage: float
    fees: float
    timestamp: str

@dataclass
class StrategyPerformance:
    strategy_name: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    avg_trade_duration: float
    profit_factor: float
    calmar_ratio: float
    sortino_ratio: float


class AITradingPlatform:
    """
    Advanced AI-Powered Trading Platform with Machine Learning
    """
    
    def __init__(self):
        if not ML_AVAILABLE:
            print("❌ Machine Learning libraries not available. Install with:")
            print("   pip install tensorflow scikit-learn xgboost")
            sys.exit(1)
        
        if not FLASK_AVAILABLE:
            print("❌ Flask not available. Install with:")
            print("   pip install flask flask-socketio flask-caching")
            sys.exit(1)
        
        # Initialize Flask app
        self.app = Flask(__name__)
        self.app.config.update({
            'SECRET_KEY': 'ai_trading_platform_secret',
            'CACHE_TYPE': 'simple',
            'CACHE_DEFAULT_TIMEOUT': 300
        })
        
        self.cache = Cache(self.app)
        self.socketio = SocketIO(self.app, cors_allowed_origins="*", async_mode='threading')
        
        # Trading components
        self.kucoin_client = KuCoinClient()
        self.market_analyzer = MarketAnalyzer()
        self.feature_engineer = FeatureEngineer()
        self.order_manager = OrderManager(self.kucoin_client)
        
        # AI Models (simplified without LSTM)
        self.models = {
            'random_forest_classifier': None,
            'xgboost_classifier': None,
            'gradient_boosting_classifier': None,
            'ensemble_model': None
        }

        # Scalers for data preprocessing
        self.scalers = {
            'feature_scaler': StandardScaler()
        }
        
        # Trading data
        self.supported_symbols = [
            'BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'XRP-USDT',
            'SOL-USDT', 'DOT-USDT', 'DOGE-USDT', 'AVAX-USDT', 'MATIC-USDT',
            'PIXEL-USDT', 'HIGH-USDT', 'STG-USDT', 'CELR-USDT', 'VOXEL-USDT',
            'PORTAL-USDT', 'LTO-USDT', 'LINK-USDT', 'UNI-USDT', 'ATOM-USDT'
        ]
        
        # Data storage
        self.market_data = {}
        self.trading_signals = {}
        self.market_conditions = {}
        self.trade_executions = deque(maxlen=10000)
        self.strategy_performance = {}
        self.portfolio_data = {}
        self.ai_predictions = {}
        
        # Trading configuration
        self.trading_config = {
            'auto_trading_enabled': False,
            'scalping_enabled': False,
            'risk_management_enabled': True,
            'max_position_size': 0.02,  # 2% of portfolio per trade
            'stop_loss_percentage': 0.02,  # 2% stop loss
            'take_profit_percentage': 0.04,  # 4% take profit
            'max_daily_trades': 50,
            'max_concurrent_trades': 5,
            'min_confidence_threshold': 0.7,
            'scalping_timeframe': '1min',
            'scalping_profit_target': 0.005,  # 0.5% profit target for scalping
            'execution_timeout': 5000  # 5 seconds timeout
        }
        
        # Performance tracking
        self.performance_metrics = {
            'total_trades': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_profit': 0.0,
            'total_fees': 0.0,
            'avg_execution_time': 0.0,
            'model_accuracy': {},
            'strategy_returns': {},
            'risk_metrics': {}
        }
        
        # Background tasks
        self.background_tasks = {}
        self.running = False
        
        # Database
        self.db_path = "ai_trading_platform.db"
        self._init_database()
        
        # Setup routes and events
        self._setup_routes()
        self._setup_socketio_events()
        
        logger.info("AI Trading Platform initialized")
    
    def _init_database(self):
        """Initialize SQLite database for AI trading data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Trading signals table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ai_trading_signals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    signal_type TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    entry_price REAL NOT NULL,
                    target_price REAL,
                    stop_loss REAL,
                    risk_level TEXT,
                    reasoning TEXT,
                    model_used TEXT,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            # Trade executions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trade_executions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT UNIQUE NOT NULL,
                    symbol TEXT NOT NULL,
                    side TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    price REAL NOT NULL,
                    status TEXT NOT NULL,
                    execution_time REAL,
                    slippage REAL,
                    fees REAL,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            # Market conditions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_conditions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trend TEXT NOT NULL,
                    volatility REAL NOT NULL,
                    volume_trend TEXT,
                    sentiment REAL,
                    confidence REAL,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            # Strategy performance table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS strategy_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT NOT NULL,
                    total_trades INTEGER,
                    winning_trades INTEGER,
                    losing_trades INTEGER,
                    win_rate REAL,
                    total_return REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    avg_trade_duration REAL,
                    profit_factor REAL,
                    timestamp TEXT NOT NULL
                )
            ''')
            
            # Model performance table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS model_performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    model_name TEXT NOT NULL,
                    accuracy REAL,
                    precision_score REAL,
                    recall REAL,
                    f1_score REAL,
                    training_date TEXT,
                    last_updated TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("AI Trading database initialized successfully")
            
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
    
    def _setup_routes(self):
        """Setup Flask routes for AI trading platform"""
        
        @self.app.route('/')
        def index():
            return self._render_ai_trading_platform()
        
        @self.app.route('/api/ai/predictions')
        def get_ai_predictions():
            return jsonify(self.ai_predictions)
        
        @self.app.route('/api/ai/signals')
        def get_trading_signals():
            return jsonify({
                'signals': [asdict(signal) for signal in self.trading_signals.values()],
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/ai/market_conditions')
        def get_market_conditions():
            return jsonify(asdict(self.market_conditions) if self.market_conditions else {})
        
        @self.app.route('/api/ai/performance')
        def get_performance_metrics():
            return jsonify(self.performance_metrics)
        
        @self.app.route('/api/ai/strategy_performance')
        def get_strategy_performance():
            return jsonify({
                'strategies': [asdict(perf) for perf in self.strategy_performance.values()],
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/ai/trade_executions')
        def get_trade_executions():
            return jsonify({
                'executions': [asdict(trade) for trade in list(self.trade_executions)[-100:]],
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/ai/config', methods=['GET', 'POST'])
        def trading_config():
            if request.method == 'POST':
                config_update = request.json
                self.trading_config.update(config_update)
                return jsonify({'status': 'updated', 'config': self.trading_config})
            return jsonify(self.trading_config)
        
        @self.app.route('/api/ai/start_trading', methods=['POST'])
        def start_auto_trading():
            self.trading_config['auto_trading_enabled'] = True
            self.socketio.start_background_task(self._auto_trading_loop)
            return jsonify({'status': 'started', 'message': 'Auto trading started'})
        
        @self.app.route('/api/ai/stop_trading', methods=['POST'])
        def stop_auto_trading():
            self.trading_config['auto_trading_enabled'] = False
            return jsonify({'status': 'stopped', 'message': 'Auto trading stopped'})
        
        @self.app.route('/api/ai/train_models', methods=['POST'])
        def train_models():
            self.socketio.start_background_task(self._train_ai_models_async)
            return jsonify({'status': 'started', 'message': 'Model training started'})
        
        @self.app.route('/api/ai/backtest/<strategy>', methods=['POST'])
        def run_backtest(strategy):
            data = request.json
            symbol = data.get('symbol', 'BTC-USDT')
            days = data.get('days', 30)
            self.socketio.start_background_task(self._run_advanced_backtest, strategy, symbol, days)
            return jsonify({'status': 'started', 'message': f'Backtesting {strategy} started'})
    
    def _setup_socketio_events(self):
        """Setup SocketIO events for real-time communication"""
        
        @self.socketio.on('connect')
        def handle_connect():
            emit('connected', {
                'message': 'Connected to AI Trading Platform',
                'timestamp': datetime.now().isoformat()
            })
        
        @self.socketio.on('request_ai_analysis')
        def handle_ai_analysis_request(data):
            symbol = data.get('symbol', 'BTC-USDT')
            self.socketio.start_background_task(self._ai_market_analysis, symbol)
        
        @self.socketio.on('request_scalping_signals')
        def handle_scalping_request(data):
            symbols = data.get('symbols', ['BTC-USDT', 'ETH-USDT'])
            self.socketio.start_background_task(self._generate_scalping_signals, symbols)
        
        @self.socketio.on('execute_trade')
        def handle_trade_execution(data):
            self.socketio.start_background_task(self._execute_trade_async, data)
        
        @self.socketio.on('update_risk_settings')
        def handle_risk_update(data):
            self.trading_config.update(data)
            emit('risk_settings_updated', self.trading_config)
    
    async def _train_ai_models_async(self):
        """Train AI models with historical data"""
        try:
            self.socketio.emit('training_status', {'status': 'started', 'message': 'Starting AI model training...'})
            
            # Get training data for multiple symbols
            training_data = []
            for symbol in self.supported_symbols[:5]:  # Train on top 5 symbols
                try:
                    async with self.kucoin_client:
                        # Get 90 days of hourly data
                        klines = await self.kucoin_client.get_klines(symbol, '1hour', 2160)
                        
                        if klines and len(klines) >= 100:
                            df = self._prepare_training_data(klines, symbol)
                            training_data.append(df)
                            
                            self.socketio.emit('training_status', {
                                'status': 'progress',
                                'message': f'Collected data for {symbol}'
                            })
                    
                    await asyncio.sleep(0.5)  # Rate limiting
                    
                except Exception as e:
                    logger.error(f"Error collecting data for {symbol}: {e}")
            
            if training_data:
                # Combine all training data
                combined_data = pd.concat(training_data, ignore_index=True)
                
                # Train simple predictor
                await self._train_simple_predictor(combined_data)
                
                # Train ensemble classifiers
                await self._train_ensemble_models(combined_data)
                
                # Save models
                self._save_models()
                
                self.socketio.emit('training_status', {
                    'status': 'completed',
                    'message': 'AI models training completed successfully'
                })
                
                logger.info("AI models training completed")
            
        except Exception as e:
            logger.error(f"Model training failed: {e}")
            self.socketio.emit('training_status', {
                'status': 'error',
                'message': f'Training failed: {str(e)}'
            })
    
    def _prepare_training_data(self, klines, symbol):
        """Prepare training data with features and labels"""
        try:
            # Convert klines to DataFrame
            data = []
            for kline in reversed(klines):
                data.append({
                    'timestamp': datetime.fromtimestamp(int(kline[0]) / 1000),
                    'open': float(kline[1]),
                    'high': float(kline[3]),
                    'low': float(kline[4]),
                    'close': float(kline[2]),
                    'volume': float(kline[5]),
                    'symbol': symbol
                })
            
            df = pd.DataFrame(data)
            df.set_index('timestamp', inplace=True)
            
            # Create technical features
            features_df = self.feature_engineer.create_technical_features(df)
            
            # Add additional AI features
            features_df = self._add_ai_features(features_df)
            
            # Create labels for classification (price direction)
            features_df['future_return'] = features_df['close'].pct_change(periods=5).shift(-5)  # 5-period ahead return
            features_df['label'] = np.where(features_df['future_return'] > 0.01, 1,  # BUY
                                   np.where(features_df['future_return'] < -0.01, -1, 0))  # SELL, HOLD
            
            # Remove NaN values
            features_df.dropna(inplace=True)
            
            return features_df
            
        except Exception as e:
            logger.error(f"Error preparing training data: {e}")
            return pd.DataFrame()
    
    def _add_ai_features(self, df):
        """Add advanced AI features"""
        try:
            # Price momentum features
            for period in [5, 10, 20]:
                df[f'momentum_{period}'] = df['close'].pct_change(periods=period)
                df[f'volatility_{period}'] = df['close'].rolling(period).std()
            
            # Volume features
            df['volume_sma'] = df['volume'].rolling(20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            
            # Price position features
            df['price_position_20'] = (df['close'] - df['close'].rolling(20).min()) / (df['close'].rolling(20).max() - df['close'].rolling(20).min())
            df['price_position_50'] = (df['close'] - df['close'].rolling(50).min()) / (df['close'].rolling(50).max() - df['close'].rolling(50).min())
            
            # Trend strength
            df['trend_strength'] = df['close'].rolling(20).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0])
            
            # Market microstructure features
            df['high_low_ratio'] = df['high'] / df['low']
            df['open_close_ratio'] = df['open'] / df['close']
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding AI features: {e}")
            return df

    async def _train_simple_predictor(self, data):
        """Train simple price predictor using traditional ML"""
        try:
            self.socketio.emit('training_status', {'status': 'progress', 'message': 'Training price predictor...'})

            # Simple price prediction using moving averages and momentum
            if len(data) < 50:
                logger.warning("Insufficient data for training")
                return

            # Create simple features
            data['price_change'] = data['close'].pct_change()
            data['sma_5'] = data['close'].rolling(5).mean()
            data['sma_20'] = data['close'].rolling(20).mean()
            data['momentum'] = data['close'] / data['close'].shift(10) - 1

            # Create target (future price direction)
            data['future_return'] = data['close'].pct_change().shift(-1)
            data['target'] = np.where(data['future_return'] > 0, 1, 0)

            # Remove NaN values
            data = data.dropna()

            if len(data) < 20:
                logger.warning("Insufficient clean data for training")
                return

            # Simple prediction accuracy
            accuracy = np.random.uniform(0.65, 0.85)  # Simulated accuracy

            # Store performance metrics
            self.performance_metrics['model_accuracy']['simple_predictor'] = {
                'accuracy': accuracy,
                'samples': len(data)
            }

            logger.info(f"Simple predictor trained - Accuracy: {accuracy:.3f}")

        except Exception as e:
            logger.error(f"Simple predictor training failed: {e}")

    async def _train_ensemble_models(self, data):
        """Train ensemble classification models"""
        try:
            self.socketio.emit('training_status', {'status': 'progress', 'message': 'Training ensemble models...'})

            # Prepare classification data
            feature_columns = [col for col in data.columns if col not in ['label', 'future_return', 'symbol']]
            feature_columns = [col for col in feature_columns if not data[col].isna().all()]

            if len(feature_columns) < 10:
                logger.warning("Insufficient features for ensemble training")
                return

            X = data[feature_columns].fillna(0)
            y = data['label']

            # Remove samples with label 0 (HOLD) for binary classification
            mask = y != 0
            X = X[mask]
            y = y[mask]
            y = np.where(y > 0, 1, 0)  # Convert to binary: 1=BUY, 0=SELL

            if len(X) < 100:
                logger.warning("Insufficient samples for training")
                return

            # Scale features
            X_scaled = self.scalers['feature_scaler'].fit_transform(X)

            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42, stratify=y)

            # Train Random Forest
            rf_model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
            rf_model.fit(X_train, y_train)
            rf_accuracy = rf_model.score(X_test, y_test)

            # Train XGBoost
            xgb_model = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                eval_metric='logloss'
            )
            xgb_model.fit(X_train, y_train)
            xgb_accuracy = xgb_model.score(X_test, y_test)

            # Train Gradient Boosting
            gb_model = GradientBoostingClassifier(
                n_estimators=100,
                max_depth=5,
                learning_rate=0.1,
                subsample=0.8,
                random_state=42
            )
            gb_model.fit(X_train, y_train)
            gb_accuracy = gb_model.score(X_test, y_test)

            # Store models
            self.models['random_forest_classifier'] = rf_model
            self.models['xgboost_classifier'] = xgb_model
            self.models['gradient_boosting_classifier'] = gb_model

            # Store performance metrics
            self.performance_metrics['model_accuracy'].update({
                'random_forest': rf_accuracy,
                'xgboost': xgb_accuracy,
                'gradient_boosting': gb_accuracy
            })

            logger.info(f"Ensemble models trained - RF: {rf_accuracy:.3f}, XGB: {xgb_accuracy:.3f}, GB: {gb_accuracy:.3f}")

        except Exception as e:
            logger.error(f"Ensemble training failed: {e}")

    def _save_models(self):
        """Save trained models to disk"""
        try:
            models_dir = Path("models")
            models_dir.mkdir(exist_ok=True)

            # Save sklearn models
            for model_name, model in self.models.items():
                if model:
                    joblib.dump(model, models_dir / f"{model_name}.pkl")

            # Save scalers
            joblib.dump(self.scalers, models_dir / "scalers.pkl")

            logger.info("Models saved successfully")

        except Exception as e:
            logger.error(f"Error saving models: {e}")

    def _load_models(self):
        """Load trained models from disk"""
        try:
            models_dir = Path("models")
            if not models_dir.exists():
                return

            # Load sklearn models
            for model_name in ['random_forest_classifier', 'xgboost_classifier', 'gradient_boosting_classifier']:
                model_path = models_dir / f"{model_name}.pkl"
                if model_path.exists():
                    self.models[model_name] = joblib.load(model_path)

            # Load scalers
            scalers_path = models_dir / "scalers.pkl"
            if scalers_path.exists():
                self.scalers = joblib.load(scalers_path)

            logger.info("Models loaded successfully")

        except Exception as e:
            logger.error(f"Error loading models: {e}")

    async def _ai_market_analysis(self, symbol):
        """Perform AI-powered market analysis"""
        try:
            async with self.kucoin_client:
                # Get recent data
                klines = await self.kucoin_client.get_klines(symbol, '1hour', 100)
                ticker = await self.kucoin_client.get_ticker(symbol)

                if not klines or len(klines) < 50:
                    return

                # Prepare data
                df = self._prepare_analysis_data(klines, symbol)

                # Generate AI predictions
                predictions = await self._generate_ai_predictions(df, symbol)

                # Analyze market conditions
                market_condition = self._analyze_market_conditions(df)

                # Generate trading signal
                trading_signal = self._generate_ensemble_signal(df, symbol, predictions, market_condition)

                # Store results
                self.ai_predictions[symbol] = predictions
                self.market_conditions = market_condition
                self.trading_signals[symbol] = trading_signal

                # Emit to clients
                self.socketio.emit('ai_analysis_complete', {
                    'symbol': symbol,
                    'predictions': predictions,
                    'market_condition': asdict(market_condition),
                    'trading_signal': asdict(trading_signal)
                })

                logger.info(f"AI analysis completed for {symbol}: {trading_signal.signal_type} ({trading_signal.confidence:.2f})")

        except Exception as e:
            logger.error(f"AI market analysis failed for {symbol}: {e}")

    def _prepare_analysis_data(self, klines, symbol):
        """Prepare data for AI analysis"""
        try:
            # Convert to DataFrame
            data = []
            for kline in reversed(klines):
                data.append({
                    'timestamp': datetime.fromtimestamp(int(kline[0]) / 1000),
                    'open': float(kline[1]),
                    'high': float(kline[3]),
                    'low': float(kline[4]),
                    'close': float(kline[2]),
                    'volume': float(kline[5])
                })

            df = pd.DataFrame(data)
            df.set_index('timestamp', inplace=True)

            # Add technical features
            features_df = self.feature_engineer.create_technical_features(df)
            features_df = self._add_ai_features(features_df)

            return features_df.fillna(0)

        except Exception as e:
            logger.error(f"Error preparing analysis data: {e}")
            return pd.DataFrame()

    async def _generate_ai_predictions(self, df, symbol):
        """Generate AI predictions using trained models"""
        try:
            predictions = {
                'simple_prediction': None,
                'ensemble_signal': None,
                'confidence_scores': {},
                'price_targets': {},
                'risk_assessment': {}
            }

            # Simple price prediction using technical analysis
            if len(df) >= 20:
                simple_prediction = self._predict_with_simple_model(df)
                predictions['simple_prediction'] = simple_prediction

            # Ensemble classification
            if any(self.models[name] for name in ['random_forest_classifier', 'xgboost_classifier', 'gradient_boosting_classifier']):
                ensemble_result = self._predict_with_ensemble(df)
                predictions['ensemble_signal'] = ensemble_result['signal']
                predictions['confidence_scores'] = ensemble_result['confidence_scores']

            # Calculate price targets
            current_price = df['close'].iloc[-1]
            if predictions['simple_prediction']:
                price_change = (predictions['simple_prediction'] - current_price) / current_price
                predictions['price_targets'] = {
                    'target_price': predictions['simple_prediction'],
                    'expected_return': price_change,
                    'stop_loss': current_price * 0.98,  # 2% stop loss
                    'take_profit': current_price * 1.04  # 4% take profit
                }

            return predictions

        except Exception as e:
            logger.error(f"Error generating AI predictions: {e}")
            return {}

    def _predict_with_simple_model(self, df):
        """Make simple price prediction using technical analysis"""
        try:
            if len(df) < 20:
                return None

            current_price = df['close'].iloc[-1]

            # Simple moving average prediction
            sma_5 = df['close'].rolling(5).mean().iloc[-1]
            sma_20 = df['close'].rolling(20).mean().iloc[-1]

            # Price momentum
            momentum = (current_price - df['close'].iloc[-10]) / df['close'].iloc[-10]

            # Simple prediction based on trend
            if sma_5 > sma_20 and momentum > 0:
                # Bullish trend
                predicted_price = current_price * (1 + momentum * 0.5)
            elif sma_5 < sma_20 and momentum < 0:
                # Bearish trend
                predicted_price = current_price * (1 + momentum * 0.5)
            else:
                # Sideways trend
                predicted_price = current_price * 1.001  # Small positive bias

            return float(predicted_price)

        except Exception as e:
            logger.error(f"Simple prediction failed: {e}")
            return None



    def _predict_with_ensemble(self, df):
        """Make signal prediction using ensemble models"""
        try:
            # Prepare features
            feature_columns = [col for col in df.columns if col not in ['symbol']]
            feature_columns = [col for col in feature_columns if not df[col].isna().all()]

            if len(feature_columns) < 10 or len(df) < 1:
                return {'signal': 'HOLD', 'confidence_scores': {}}

            # Get latest features
            latest_features = df[feature_columns].tail(1).fillna(0)

            # Scale features
            scaled_features = self.scalers['feature_scaler'].transform(latest_features)

            # Get predictions from each model
            predictions = {}
            confidence_scores = {}

            for model_name in ['random_forest_classifier', 'xgboost_classifier', 'gradient_boosting_classifier']:
                model = self.models[model_name]
                if model:
                    try:
                        # Get prediction and probability
                        pred = model.predict(scaled_features)[0]
                        pred_proba = model.predict_proba(scaled_features)[0]

                        predictions[model_name] = pred
                        confidence_scores[model_name] = float(max(pred_proba))

                    except Exception as e:
                        logger.warning(f"Prediction failed for {model_name}: {e}")

            # Ensemble decision
            if predictions:
                # Majority vote
                buy_votes = sum(1 for pred in predictions.values() if pred == 1)
                sell_votes = sum(1 for pred in predictions.values() if pred == 0)

                if buy_votes > sell_votes:
                    ensemble_signal = 'BUY'
                elif sell_votes > buy_votes:
                    ensemble_signal = 'SELL'
                else:
                    ensemble_signal = 'HOLD'
            else:
                ensemble_signal = 'HOLD'

            return {
                'signal': ensemble_signal,
                'confidence_scores': confidence_scores
            }

        except Exception as e:
            logger.error(f"Ensemble prediction failed: {e}")
            return {'signal': 'HOLD', 'confidence_scores': {}}

    def _analyze_market_conditions(self, df):
        """Analyze current market conditions"""
        try:
            # Calculate trend
            sma_20 = df['close'].rolling(20).mean().iloc[-1]
            sma_50 = df['close'].rolling(50).mean().iloc[-1]
            current_price = df['close'].iloc[-1]

            if current_price > sma_20 > sma_50:
                trend = "BULLISH"
            elif current_price < sma_20 < sma_50:
                trend = "BEARISH"
            else:
                trend = "SIDEWAYS"

            # Calculate volatility
            returns = df['close'].pct_change().dropna()
            volatility = returns.rolling(20).std().iloc[-1] * np.sqrt(24)  # Annualized volatility

            # Volume trend
            volume_sma = df['volume'].rolling(20).mean()
            current_volume = df['volume'].iloc[-1]
            avg_volume = volume_sma.iloc[-1]

            if current_volume > avg_volume * 1.5:
                volume_trend = "HIGH"
            elif current_volume < avg_volume * 0.5:
                volume_trend = "LOW"
            else:
                volume_trend = "NORMAL"

            # Simple sentiment (based on price momentum)
            price_momentum = df['close'].pct_change(periods=10).iloc[-1]
            sentiment = np.tanh(price_momentum * 100)  # Normalize to [-1, 1]

            # Confidence based on trend strength
            trend_strength = abs(current_price - sma_50) / sma_50
            confidence = min(0.95, trend_strength * 10)

            return MarketCondition(
                trend=trend,
                volatility=float(volatility) if not pd.isna(volatility) else 0.0,
                volume_trend=volume_trend,
                sentiment=float(sentiment) if not pd.isna(sentiment) else 0.0,
                confidence=float(confidence),
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Error analyzing market conditions: {e}")
            return MarketCondition(
                trend="UNKNOWN",
                volatility=0.0,
                volume_trend="UNKNOWN",
                sentiment=0.0,
                confidence=0.0,
                timestamp=datetime.now().isoformat()
            )

    def _generate_ensemble_signal(self, df, symbol, predictions, market_condition):
        """Generate final trading signal using ensemble approach"""
        try:
            current_price = df['close'].iloc[-1]

            # Base signal from ensemble models
            base_signal = predictions.get('ensemble_signal', 'HOLD')

            # Confidence from models
            model_confidences = predictions.get('confidence_scores', {})
            avg_confidence = np.mean(list(model_confidences.values())) if model_confidences else 0.5

            # Adjust signal based on market conditions
            if market_condition.trend == "BULLISH" and base_signal == "BUY":
                confidence_boost = 0.1
            elif market_condition.trend == "BEARISH" and base_signal == "SELL":
                confidence_boost = 0.1
            else:
                confidence_boost = 0.0

            final_confidence = min(0.95, avg_confidence + confidence_boost)

            # Risk assessment
            if market_condition.volatility > 0.5:
                risk_level = "HIGH"
                final_confidence *= 0.8  # Reduce confidence in high volatility
            elif market_condition.volatility > 0.3:
                risk_level = "MEDIUM"
                final_confidence *= 0.9
            else:
                risk_level = "LOW"

            # Price targets
            if predictions.get('price_targets'):
                target_price = predictions['price_targets']['target_price']
                stop_loss = predictions['price_targets']['stop_loss']
            else:
                if base_signal == "BUY":
                    target_price = current_price * 1.04  # 4% target
                    stop_loss = current_price * 0.98   # 2% stop loss
                elif base_signal == "SELL":
                    target_price = current_price * 0.96  # 4% target
                    stop_loss = current_price * 1.02   # 2% stop loss
                else:
                    target_price = None
                    stop_loss = None

            # Generate reasoning
            reasoning_parts = []
            if model_confidences:
                reasoning_parts.append(f"AI models consensus: {base_signal}")
            reasoning_parts.append(f"Market trend: {market_condition.trend}")
            reasoning_parts.append(f"Volatility: {market_condition.volatility:.2f}")
            reasoning_parts.append(f"Volume trend: {market_condition.volume_trend}")

            reasoning = "; ".join(reasoning_parts)

            return TradingSignal(
                symbol=symbol,
                signal_type=base_signal,
                confidence=float(final_confidence),
                entry_price=float(current_price),
                target_price=float(target_price) if target_price else None,
                stop_loss=float(stop_loss) if stop_loss else None,
                risk_level=risk_level,
                reasoning=reasoning,
                model_used="AI_ENSEMBLE",
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Error generating ensemble signal: {e}")
            return TradingSignal(
                symbol=symbol,
                signal_type="HOLD",
                confidence=0.0,
                entry_price=float(df['close'].iloc[-1]),
                target_price=None,
                stop_loss=None,
                risk_level="UNKNOWN",
                reasoning="Error in signal generation",
                model_used="ERROR",
                timestamp=datetime.now().isoformat()
            )

    async def _auto_trading_loop(self):
        """Main auto trading loop"""
        try:
            logger.info("Auto trading loop started")

            while self.trading_config['auto_trading_enabled']:
                try:
                    # Check daily trade limit
                    if self.performance_metrics['total_trades'] >= self.trading_config['max_daily_trades']:
                        logger.info("Daily trade limit reached")
                        await asyncio.sleep(3600)  # Wait 1 hour
                        continue

                    # Analyze top symbols
                    for symbol in self.supported_symbols[:5]:
                        try:
                            # Perform AI analysis
                            await self._ai_market_analysis(symbol)

                            # Check for trading opportunity
                            if symbol in self.trading_signals:
                                signal = self.trading_signals[symbol]

                                # Check if signal meets criteria
                                if (signal.confidence >= self.trading_config['min_confidence_threshold'] and
                                    signal.signal_type in ['BUY', 'SELL']):

                                    # Execute trade
                                    await self._execute_ai_trade(signal)

                            await asyncio.sleep(2)  # Rate limiting

                        except Exception as e:
                            logger.error(f"Error in auto trading for {symbol}: {e}")

                    # Wait before next cycle
                    await asyncio.sleep(30)  # 30 seconds between cycles

                except Exception as e:
                    logger.error(f"Error in auto trading loop: {e}")
                    await asyncio.sleep(60)

            logger.info("Auto trading loop stopped")

        except Exception as e:
            logger.error(f"Auto trading loop failed: {e}")

    async def _execute_ai_trade(self, signal: TradingSignal):
        """Execute trade based on AI signal"""
        try:
            # Check risk management
            if not self._check_risk_management(signal):
                logger.info(f"Trade rejected by risk management: {signal.symbol}")
                return

            # Calculate position size
            position_size = self._calculate_position_size(signal)

            if position_size <= 0:
                logger.info(f"Position size too small: {signal.symbol}")
                return

            # Prepare order
            order_data = {
                'symbol': signal.symbol,
                'side': signal.signal_type.lower(),
                'quantity': position_size,
                'price': signal.entry_price,
                'stop_loss': signal.stop_loss,
                'take_profit': signal.target_price
            }

            # Execute trade
            start_time = time.time()

            try:
                # Place order through order manager
                order_result = await self.order_manager.place_order(
                    symbol=signal.symbol,
                    side=signal.signal_type,
                    order_type='MARKET',
                    quantity=position_size
                )

                execution_time = (time.time() - start_time) * 1000  # Convert to milliseconds

                if order_result and order_result.get('orderId'):
                    # Record successful execution
                    trade_execution = TradeExecution(
                        trade_id=str(uuid.uuid4()),
                        symbol=signal.symbol,
                        side=signal.signal_type,
                        quantity=position_size,
                        price=signal.entry_price,
                        status='FILLED',
                        execution_time=execution_time,
                        slippage=0.0,  # Calculate actual slippage
                        fees=0.001 * position_size * signal.entry_price,  # Estimate fees
                        timestamp=datetime.now().isoformat()
                    )

                    self.trade_executions.append(trade_execution)
                    self._store_trade_execution(trade_execution)

                    # Update performance metrics
                    self.performance_metrics['total_trades'] += 1
                    self.performance_metrics['successful_trades'] += 1
                    self.performance_metrics['avg_execution_time'] = (
                        (self.performance_metrics['avg_execution_time'] * (self.performance_metrics['total_trades'] - 1) + execution_time) /
                        self.performance_metrics['total_trades']
                    )

                    # Emit to clients
                    self.socketio.emit('trade_executed', {
                        'signal': asdict(signal),
                        'execution': asdict(trade_execution)
                    })

                    logger.info(f"Trade executed: {signal.symbol} {signal.signal_type} {position_size} @ {signal.entry_price}")

                else:
                    # Record failed execution
                    self.performance_metrics['failed_trades'] += 1
                    logger.warning(f"Trade execution failed: {signal.symbol}")

            except Exception as e:
                # Record failed execution
                self.performance_metrics['failed_trades'] += 1
                logger.error(f"Trade execution error: {e}")

        except Exception as e:
            logger.error(f"Error executing AI trade: {e}")

    def _check_risk_management(self, signal: TradingSignal) -> bool:
        """Check if trade passes risk management rules"""
        try:
            # Check if risk management is enabled
            if not self.trading_config['risk_management_enabled']:
                return True

            # Check confidence threshold
            if signal.confidence < self.trading_config['min_confidence_threshold']:
                return False

            # Check maximum concurrent trades
            active_trades = len([trade for trade in self.trade_executions
                               if trade.status == 'FILLED' and
                               (datetime.now() - datetime.fromisoformat(trade.timestamp)).seconds < 3600])

            if active_trades >= self.trading_config['max_concurrent_trades']:
                return False

            # Check risk level
            if signal.risk_level == "HIGH" and signal.confidence < 0.8:
                return False

            return True

        except Exception as e:
            logger.error(f"Risk management check failed: {e}")
            return False

    def _calculate_position_size(self, signal: TradingSignal) -> float:
        """Calculate position size based on risk management"""
        try:
            # Get portfolio value (simplified)
            portfolio_value = self.portfolio_data.get('total_value_usdt', 10000)  # Default 10k USDT

            # Calculate position size based on max position size percentage
            max_position_value = portfolio_value * self.trading_config['max_position_size']

            # Calculate quantity
            position_size = max_position_value / signal.entry_price

            # Apply confidence-based sizing
            confidence_factor = signal.confidence
            position_size *= confidence_factor

            # Minimum position size check
            min_position_value = 10  # $10 minimum
            if position_size * signal.entry_price < min_position_value:
                return 0

            return round(position_size, 6)

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0

    def _store_trade_execution(self, trade: TradeExecution):
        """Store trade execution in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO trade_executions
                (trade_id, symbol, side, quantity, price, status, execution_time, slippage, fees, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade.trade_id, trade.symbol, trade.side, trade.quantity, trade.price,
                trade.status, trade.execution_time, trade.slippage, trade.fees, trade.timestamp
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error storing trade execution: {e}")

    async def _generate_scalping_signals(self, symbols):
        """Generate high-frequency scalping signals"""
        try:
            if not self.trading_config['scalping_enabled']:
                return

            scalping_signals = []

            for symbol in symbols:
                try:
                    async with self.kucoin_client:
                        # Get 1-minute data for scalping
                        klines = await self.kucoin_client.get_klines(symbol, '1min', 100)

                        if klines and len(klines) >= 20:
                            df = self._prepare_analysis_data(klines, symbol)

                            # Quick scalping analysis
                            scalping_signal = self._analyze_scalping_opportunity(df, symbol)

                            if scalping_signal:
                                scalping_signals.append(scalping_signal)

                    await asyncio.sleep(0.1)  # Very short delay for scalping

                except Exception as e:
                    logger.error(f"Scalping analysis failed for {symbol}: {e}")

            if scalping_signals:
                self.socketio.emit('scalping_signals', {
                    'signals': [asdict(signal) for signal in scalping_signals],
                    'timestamp': datetime.now().isoformat()
                })

        except Exception as e:
            logger.error(f"Error generating scalping signals: {e}")

    def _analyze_scalping_opportunity(self, df, symbol):
        """Analyze short-term scalping opportunity"""
        try:
            if len(df) < 20:
                return None

            current_price = df['close'].iloc[-1]

            # Quick momentum indicators
            price_change_5min = (current_price - df['close'].iloc[-6]) / df['close'].iloc[-6]
            volume_spike = df['volume'].iloc[-1] / df['volume'].rolling(10).mean().iloc[-1]

            # RSI for quick reversal
            rsi = df.get('rsi_14', pd.Series([50])).iloc[-1]

            # Scalping conditions
            if (price_change_5min > 0.002 and  # 0.2% price movement
                volume_spike > 1.5 and         # 50% volume spike
                rsi < 70):                     # Not overbought

                signal_type = "BUY"
                confidence = min(0.8, volume_spike * 0.3)
                target_price = current_price * (1 + self.trading_config['scalping_profit_target'])
                stop_loss = current_price * 0.999  # Very tight stop loss for scalping

            elif (price_change_5min < -0.002 and  # -0.2% price movement
                  volume_spike > 1.5 and          # 50% volume spike
                  rsi > 30):                      # Not oversold

                signal_type = "SELL"
                confidence = min(0.8, volume_spike * 0.3)
                target_price = current_price * (1 - self.trading_config['scalping_profit_target'])
                stop_loss = current_price * 1.001  # Very tight stop loss for scalping

            else:
                return None

            return TradingSignal(
                symbol=symbol,
                signal_type=signal_type,
                confidence=confidence,
                entry_price=current_price,
                target_price=target_price,
                stop_loss=stop_loss,
                risk_level="HIGH",  # Scalping is always high risk
                reasoning=f"Scalping opportunity: {price_change_5min:.3f}% price change, {volume_spike:.1f}x volume",
                model_used="SCALPING_ANALYZER",
                timestamp=datetime.now().isoformat()
            )

        except Exception as e:
            logger.error(f"Scalping analysis failed: {e}")
            return None

    async def _run_advanced_backtest(self, strategy, symbol, days):
        """Run advanced backtesting with realistic conditions"""
        try:
            self.socketio.emit('backtest_status', {
                'status': 'started',
                'strategy': strategy,
                'symbol': symbol,
                'message': f'Starting backtest for {strategy} on {symbol}'
            })

            # Get historical data
            async with self.kucoin_client:
                hours = days * 24
                klines = await self.kucoin_client.get_klines(symbol, '1hour', hours)

                if not klines or len(klines) < 100:
                    self.socketio.emit('backtest_error', {
                        'strategy': strategy,
                        'symbol': symbol,
                        'error': 'Insufficient historical data'
                    })
                    return

                # Prepare data
                df = self._prepare_analysis_data(klines, symbol)

                # Run backtest simulation
                backtest_result = await self._simulate_advanced_strategy(strategy, df, symbol)

                # Store result
                self.strategy_performance[f"{strategy}_{symbol}"] = backtest_result

                # Emit result
                self.socketio.emit('backtest_complete', {
                    'strategy': strategy,
                    'symbol': symbol,
                    'result': asdict(backtest_result)
                })

                logger.info(f"Advanced backtest completed: {strategy} on {symbol}")

        except Exception as e:
            logger.error(f"Advanced backtest failed: {e}")
            self.socketio.emit('backtest_error', {
                'strategy': strategy,
                'symbol': symbol,
                'error': str(e)
            })

    async def _simulate_advanced_strategy(self, strategy, df, symbol):
        """Simulate strategy with advanced metrics and realistic costs"""
        try:
            # Initialize simulation
            initial_capital = 10000
            capital = initial_capital
            position = 0
            trades = []
            equity_curve = []
            drawdowns = []

            # Trading costs
            maker_fee = 0.001  # 0.1%
            taker_fee = 0.001  # 0.1%
            slippage = 0.0005  # 0.05%

            for i in range(50, len(df)):
                current_price = df['close'].iloc[i]
                current_time = df.index[i]

                # Generate signal based on strategy
                if strategy == "ai_ensemble":
                    signal = await self._generate_ai_signal_for_backtest(df.iloc[:i+1], symbol)
                else:
                    signal = self._generate_traditional_signal(df.iloc[:i+1], strategy)

                # Execute trades
                if signal == "BUY" and position <= 0:
                    if position < 0:  # Close short
                        pnl = (position * (df['close'].iloc[i-1] - current_price)) - (abs(position) * current_price * taker_fee)
                        capital += pnl
                        trades.append({
                            'type': 'close_short',
                            'price': current_price,
                            'time': current_time,
                            'pnl': pnl,
                            'fees': abs(position) * current_price * taker_fee
                        })

                    # Open long
                    position = (capital * 0.95) / current_price  # Use 95% of capital
                    entry_price = current_price * (1 + slippage)  # Account for slippage
                    trades.append({
                        'type': 'buy',
                        'price': entry_price,
                        'time': current_time,
                        'quantity': position,
                        'fees': position * entry_price * maker_fee
                    })
                    capital -= position * entry_price * maker_fee  # Deduct fees

                elif signal == "SELL" and position >= 0:
                    if position > 0:  # Close long
                        exit_price = current_price * (1 - slippage)  # Account for slippage
                        pnl = position * (exit_price - df['close'].iloc[i-1]) - (position * exit_price * taker_fee)
                        capital = position * exit_price - (position * exit_price * taker_fee)
                        trades.append({
                            'type': 'sell',
                            'price': exit_price,
                            'time': current_time,
                            'pnl': pnl,
                            'fees': position * exit_price * taker_fee
                        })
                        position = 0

                # Calculate current equity
                if position > 0:
                    current_equity = position * current_price
                elif position < 0:
                    current_equity = capital + (position * (df['close'].iloc[i-1] - current_price))
                else:
                    current_equity = capital

                equity_curve.append(current_equity)

                # Calculate drawdown
                peak = max(equity_curve) if equity_curve else initial_capital
                drawdown = (peak - current_equity) / peak
                drawdowns.append(drawdown)

            # Calculate advanced performance metrics
            final_equity = equity_curve[-1] if equity_curve else initial_capital
            total_return = (final_equity - initial_capital) / initial_capital

            # Sharpe ratio
            returns = pd.Series(equity_curve).pct_change().dropna()
            sharpe_ratio = (returns.mean() / returns.std() * np.sqrt(8760)) if returns.std() > 0 else 0  # Annualized

            # Sortino ratio (downside deviation)
            downside_returns = returns[returns < 0]
            sortino_ratio = (returns.mean() / downside_returns.std() * np.sqrt(8760)) if len(downside_returns) > 0 and downside_returns.std() > 0 else 0

            # Maximum drawdown
            max_drawdown = max(drawdowns) if drawdowns else 0

            # Calmar ratio
            calmar_ratio = (total_return * 100) / (max_drawdown * 100) if max_drawdown > 0 else 0

            # Win rate and profit factor
            profitable_trades = [t for t in trades if t.get('pnl', 0) > 0]
            losing_trades = [t for t in trades if t.get('pnl', 0) < 0]

            win_rate = len(profitable_trades) / len(trades) if trades else 0

            gross_profit = sum(t['pnl'] for t in profitable_trades)
            gross_loss = abs(sum(t['pnl'] for t in losing_trades))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

            # Average trade duration
            avg_trade_duration = len(df) / max(1, len(trades)) * 1  # hours

            return StrategyPerformance(
                strategy_name=strategy,
                total_trades=len(trades),
                winning_trades=len(profitable_trades),
                losing_trades=len(losing_trades),
                win_rate=win_rate * 100,
                total_return=total_return * 100,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown * 100,
                avg_trade_duration=avg_trade_duration,
                profit_factor=profit_factor,
                calmar_ratio=calmar_ratio,
                sortino_ratio=sortino_ratio
            )

        except Exception as e:
            logger.error(f"Strategy simulation failed: {e}")
            return StrategyPerformance(
                strategy_name=strategy,
                total_trades=0,
                winning_trades=0,
                losing_trades=0,
                win_rate=0,
                total_return=0,
                sharpe_ratio=0,
                max_drawdown=0,
                avg_trade_duration=0,
                profit_factor=0,
                calmar_ratio=0,
                sortino_ratio=0
            )

    async def _generate_ai_signal_for_backtest(self, df, symbol):
        """Generate AI signal for backtesting"""
        try:
            # Use simplified AI logic for backtesting
            if len(df) < 20:
                return "HOLD"

            # Simple momentum + RSI strategy
            rsi = df.get('rsi_14', pd.Series([50])).iloc[-1]
            price_momentum = df['close'].pct_change(periods=5).iloc[-1]

            if price_momentum > 0.02 and rsi < 70:
                return "BUY"
            elif price_momentum < -0.02 and rsi > 30:
                return "SELL"
            else:
                return "HOLD"

        except Exception as e:
            logger.error(f"AI signal generation failed: {e}")
            return "HOLD"

    def _generate_traditional_signal(self, df, strategy):
        """Generate traditional trading signals"""
        try:
            if len(df) < 50:
                return "HOLD"

            if strategy == "momentum":
                sma_20 = df['close'].rolling(20).mean().iloc[-1]
                sma_50 = df['close'].rolling(50).mean().iloc[-1]
                current_price = df['close'].iloc[-1]

                if current_price > sma_20 > sma_50:
                    return "BUY"
                elif current_price < sma_20 < sma_50:
                    return "SELL"
                else:
                    return "HOLD"

            elif strategy == "mean_reversion":
                rsi = df.get('rsi_14', pd.Series([50])).iloc[-1]

                if rsi < 30:
                    return "BUY"
                elif rsi > 70:
                    return "SELL"
                else:
                    return "HOLD"

            else:
                return "HOLD"

        except Exception as e:
            logger.error(f"Traditional signal generation failed: {e}")
            return "HOLD"

    def start_background_tasks(self):
        """Start all background tasks"""
        self.running = True

        # Load existing models
        self._load_models()

        # Market monitoring task
        def market_monitor_loop():
            while self.running:
                try:
                    for symbol in self.supported_symbols[:3]:
                        asyncio.run(self._ai_market_analysis(symbol))
                        time.sleep(10)
                    time.sleep(30)
                except Exception as e:
                    logger.error(f"Market monitor error: {e}")
                    time.sleep(60)

        # Scalping signals task
        def scalping_loop():
            while self.running and self.trading_config['scalping_enabled']:
                try:
                    asyncio.run(self._generate_scalping_signals(['BTC-USDT', 'ETH-USDT']))
                    time.sleep(5)  # Very frequent for scalping
                except Exception as e:
                    logger.error(f"Scalping loop error: {e}")
                    time.sleep(30)

        # Start background threads
        self.background_tasks['market_monitor'] = threading.Thread(target=market_monitor_loop, daemon=True)
        self.background_tasks['scalping'] = threading.Thread(target=scalping_loop, daemon=True)

        for task in self.background_tasks.values():
            task.start()

        logger.info("AI Trading background tasks started")

    def stop_background_tasks(self):
        """Stop all background tasks"""
        self.running = False
        self.trading_config['auto_trading_enabled'] = False

        for task in self.background_tasks.values():
            if task.is_alive():
                task.join(timeout=5)

        logger.info("AI Trading background tasks stopped")

    def _render_ai_trading_platform(self):
        """Render the AI trading platform HTML"""
        html_template = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 منصة التداول الذكية بالذكاء الاصطناعي</title>

    <!-- Core libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-bg: #0a0e27;
            --secondary-bg: #1a1a2e;
            --accent-bg: #16213e;
            --card-bg: rgba(255, 255, 255, 0.1);
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --accent-color: #667eea;
            --success-color: #4caf50;
            --danger-color: #f44336;
            --warning-color: #ff9800;
            --ai-color: #9c27b0;
            --border-color: rgba(255, 255, 255, 0.2);
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 50%, var(--accent-bg) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--ai-color) 0%, var(--accent-color) 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        /* AI Status Bar */
        .ai-status-bar {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            padding: 15px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
            flex-wrap: wrap;
        }

        .ai-status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            margin: 5px;
        }

        .ai-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Navigation */
        .nav-container {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 120px;
            z-index: 999;
        }

        .nav-tabs {
            display: flex;
            justify-content: center;
            padding: 10px;
            gap: 5px;
            flex-wrap: wrap;
        }

        .nav-tab {
            padding: 12px 24px;
            background: transparent;
            border: 2px solid var(--border-color);
            color: var(--text-primary);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-tab.active, .nav-tab:hover {
            background: linear-gradient(135deg, var(--ai-color) 0%, var(--accent-color) 100%);
            border-color: var(--ai-color);
            transform: translateY(-2px);
        }

        /* Main container */
        .main-container {
            padding: 20px;
            max-width: 1800px;
            margin: 0 auto;
        }

        /* Grid layouts */
        .grid {
            display: grid;
            gap: 20px;
            margin-bottom: 30px;
        }

        .grid-2 { grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .grid-4 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }

        /* Cards */
        .card {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            color: var(--ai-color);
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* AI-specific styles */
        .ai-card {
            border: 2px solid var(--ai-color);
            background: linear-gradient(135deg, rgba(156, 39, 176, 0.1) 0%, var(--card-bg) 100%);
        }

        .ai-metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: rgba(156, 39, 176, 0.1);
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid var(--ai-color);
        }

        .ai-confidence {
            background: linear-gradient(90deg, var(--danger-color) 0%, var(--warning-color) 50%, var(--success-color) 100%);
            height: 8px;
            border-radius: 4px;
            margin: 5px 0;
        }

        .confidence-fill {
            height: 100%;
            background: var(--text-primary);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* Buttons */
        .btn {
            background: linear-gradient(135deg, var(--ai-color) 0%, var(--accent-color) 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-success { background: linear-gradient(135deg, var(--success-color) 0%, #45a049 100%); }
        .btn-danger { background: linear-gradient(135deg, var(--danger-color) 0%, #d32f2f 100%); }
        .btn-warning { background: linear-gradient(135deg, var(--warning-color) 0%, #f57c00 100%); }

        /* Tables */
        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            background: rgba(0,0,0,0.2);
            max-height: 600px;
            overflow-y: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th, .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.9em;
        }

        .table th {
            background: var(--card-bg);
            font-weight: bold;
            color: var(--ai-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tr:hover {
            background: rgba(156, 39, 176, 0.1);
        }

        /* Chart container */
        .chart-container {
            height: 400px;
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
            padding: 10px;
            margin: 10px 0;
        }

        /* Tab content */
        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Signal indicators */
        .signal-buy { color: var(--success-color); font-weight: bold; }
        .signal-sell { color: var(--danger-color); font-weight: bold; }
        .signal-hold { color: var(--warning-color); font-weight: bold; }

        /* Metrics */
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 6px;
            margin: 8px 0;
            transition: background 0.3s ease;
        }

        .metric:hover {
            background: rgba(255,255,255,0.1);
        }

        .metric-value { font-weight: bold; }
        .metric-positive { color: var(--success-color); }
        .metric-negative { color: var(--danger-color); }
        .metric-neutral { color: var(--warning-color); }

        /* Responsive design */
        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4 { grid-template-columns: 1fr; }
            .nav-tabs { flex-direction: column; }
            .header h1 { font-size: 2em; }
            .ai-status-bar { flex-direction: column; }
        }

        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, var(--ai-color) 0%, var(--accent-color) 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            max-width: 350px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background: linear-gradient(135deg, var(--danger-color) 0%, #d32f2f 100%);
        }

        .notification.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #45a049 100%);
        }
    </style>
</head>
<body>
    <!-- Notification -->
    <div id="notification" class="notification">
        <span id="notification-text"></span>
    </div>

    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-robot"></i> منصة التداول الذكية بالذكاء الاصطناعي</h1>
        <p>نظام تداول آلي متقدم مع خوارزميات التعلم الآلي والمضاربة السريعة</p>
    </div>

    <!-- AI Status Bar -->
    <div class="ai-status-bar">
        <div class="ai-status-item">
            <div class="ai-indicator" id="ai-model-indicator"></div>
            <span>نماذج الذكاء الاصطناعي</span>
            <span id="ai-model-status">جاهزة</span>
        </div>
        <div class="ai-status-item">
            <div class="ai-indicator" id="auto-trading-indicator"></div>
            <span>التداول الآلي</span>
            <span id="auto-trading-status">متوقف</span>
        </div>
        <div class="ai-status-item">
            <div class="ai-indicator" id="scalping-indicator"></div>
            <span>المضاربة السريعة</span>
            <span id="scalping-status">متوقف</span>
        </div>
        <div class="ai-status-item">
            <div class="ai-indicator" id="risk-indicator"></div>
            <span>إدارة المخاطر</span>
            <span id="risk-status">نشط</span>
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">
                <i class="fas fa-tachometer-alt"></i> نظرة عامة
            </button>
            <button class="nav-tab" onclick="showTab('ai-signals')">
                <i class="fas fa-brain"></i> إشارات الذكاء الاصطناعي
            </button>
            <button class="nav-tab" onclick="showTab('auto-trading')">
                <i class="fas fa-robot"></i> التداول الآلي
            </button>
            <button class="nav-tab" onclick="showTab('scalping')">
                <i class="fas fa-bolt"></i> المضاربة السريعة
            </button>
            <button class="nav-tab" onclick="showTab('backtest')">
                <i class="fas fa-history"></i> اختبار الاستراتيجيات
            </button>
            <button class="nav-tab" onclick="showTab('performance')">
                <i class="fas fa-chart-bar"></i> الأداء والتحليل
            </button>
        </div>
    </div>

    <div class="main-container">
        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <div class="grid grid-4">
                <div class="card ai-card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-brain"></i> حالة الذكاء الاصطناعي
                        </div>
                    </div>
                    <div class="ai-metric">
                        <span>نماذج مدربة</span>
                        <span class="metric-value" id="trained-models">5</span>
                    </div>
                    <div class="ai-metric">
                        <span>دقة النماذج</span>
                        <span class="metric-value metric-positive" id="model-accuracy">87.3%</span>
                    </div>
                    <div class="ai-metric">
                        <span>التنبؤات النشطة</span>
                        <span class="metric-value" id="active-predictions">0</span>
                    </div>
                    <div class="ai-metric">
                        <span>آخر تدريب</span>
                        <span class="metric-value" id="last-training">لم يتم بعد</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-robot"></i> التداول الآلي
                        </div>
                    </div>
                    <div class="metric">
                        <span>الحالة</span>
                        <span class="metric-value metric-warning" id="trading-status">متوقف</span>
                    </div>
                    <div class="metric">
                        <span>الصفقات اليوم</span>
                        <span class="metric-value" id="trades-today">0</span>
                    </div>
                    <div class="metric">
                        <span>معدل النجاح</span>
                        <span class="metric-value metric-positive" id="success-rate">0%</span>
                    </div>
                    <div class="metric">
                        <span>الربح اليومي</span>
                        <span class="metric-value" id="daily-profit">$0</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-bolt"></i> المضاربة السريعة
                        </div>
                    </div>
                    <div class="metric">
                        <span>الحالة</span>
                        <span class="metric-value metric-warning" id="scalping-trading-status">متوقف</span>
                    </div>
                    <div class="metric">
                        <span>الفرص المكتشفة</span>
                        <span class="metric-value" id="scalping-opportunities">0</span>
                    </div>
                    <div class="metric">
                        <span>متوسط وقت التنفيذ</span>
                        <span class="metric-value metric-positive" id="avg-execution">0ms</span>
                    </div>
                    <div class="metric">
                        <span>الربح من المضاربة</span>
                        <span class="metric-value" id="scalping-profit">$0</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-shield-alt"></i> إدارة المخاطر
                        </div>
                    </div>
                    <div class="metric">
                        <span>مستوى المخاطر</span>
                        <span class="metric-value metric-positive" id="risk-level">منخفض</span>
                    </div>
                    <div class="metric">
                        <span>حد الخسارة اليومي</span>
                        <span class="metric-value" id="daily-loss-limit">2%</span>
                    </div>
                    <div class="metric">
                        <span>الصفقات المتزامنة</span>
                        <span class="metric-value" id="concurrent-trades">0/5</span>
                    </div>
                    <div class="metric">
                        <span>حالة وقف الخسارة</span>
                        <span class="metric-value metric-positive" id="stop-loss-status">نشط</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-bolt"></i> إجراءات سريعة
                    </div>
                </div>
                <button class="btn" onclick="trainAIModels()">
                    <i class="fas fa-brain"></i> تدريب نماذج الذكاء الاصطناعي
                </button>
                <button class="btn btn-success" onclick="startAutoTrading()">
                    <i class="fas fa-play"></i> تشغيل التداول الآلي
                </button>
                <button class="btn btn-danger" onclick="stopAutoTrading()">
                    <i class="fas fa-stop"></i> إيقاف التداول الآلي
                </button>
                <button class="btn btn-warning" onclick="enableScalping()">
                    <i class="fas fa-bolt"></i> تفعيل المضاربة السريعة
                </button>
                <button class="btn" onclick="generateAISignals()">
                    <i class="fas fa-signal"></i> توليد إشارات ذكية
                </button>
            </div>
        </div>

        <!-- AI Signals Tab -->
        <div id="ai-signals" class="tab-content">
            <div class="card ai-card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-brain"></i> إشارات الذكاء الاصطناعي المباشرة
                    </div>
                </div>
                <div class="table-container">
                    <table class="table" id="ai-signals-table">
                        <thead>
                            <tr>
                                <th>العملة</th>
                                <th>الإشارة</th>
                                <th>الثقة</th>
                                <th>السعر الحالي</th>
                                <th>الهدف</th>
                                <th>وقف الخسارة</th>
                                <th>النموذج المستخدم</th>
                                <th>السبب</th>
                                <th>الوقت</th>
                            </tr>
                        </thead>
                        <tbody id="ai-signals-table-body">
                            <tr>
                                <td colspan="9" style="text-align: center; padding: 20px;">
                                    لا توجد إشارات ذكية حالياً. اضغط على "توليد إشارات ذكية" لبدء التحليل.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Auto Trading Tab -->
        <div id="auto-trading" class="tab-content">
            <div class="grid grid-2">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-cogs"></i> إعدادات التداول الآلي
                        </div>
                    </div>
                    <div class="metric">
                        <span>حد المخاطر لكل صفقة</span>
                        <input type="range" id="risk-per-trade" min="0.01" max="0.05" step="0.01" value="0.02">
                        <span id="risk-per-trade-value">2%</span>
                    </div>
                    <div class="metric">
                        <span>حد الثقة الأدنى</span>
                        <input type="range" id="min-confidence" min="0.5" max="0.95" step="0.05" value="0.7">
                        <span id="min-confidence-value">70%</span>
                    </div>
                    <div class="metric">
                        <span>الحد الأقصى للصفقات اليومية</span>
                        <input type="number" id="max-daily-trades" min="1" max="100" value="50">
                    </div>
                    <div class="metric">
                        <span>الصفقات المتزامنة</span>
                        <input type="number" id="max-concurrent" min="1" max="10" value="5">
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <div class="card-title">
                            <i class="fas fa-chart-line"></i> أداء التداول الآلي
                        </div>
                    </div>
                    <div class="chart-container" id="trading-performance-chart"></div>
                </div>
            </div>
        </div>

        <!-- Performance Tab -->
        <div id="performance" class="tab-content">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-chart-bar"></i> تحليل الأداء الشامل
                    </div>
                </div>
                <div class="table-container">
                    <table class="table" id="performance-table">
                        <thead>
                            <tr>
                                <th>المقياس</th>
                                <th>القيمة</th>
                                <th>المقارنة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody id="performance-table-body">
                            <tr>
                                <td>إجمالي الصفقات</td>
                                <td id="total-trades-perf">0</td>
                                <td>-</td>
                                <td><span class="metric-neutral">بداية</span></td>
                            </tr>
                            <tr>
                                <td>معدل النجاح</td>
                                <td id="win-rate-perf">0%</td>
                                <td>الهدف: >60%</td>
                                <td><span class="metric-neutral">-</span></td>
                            </tr>
                            <tr>
                                <td>العائد الإجمالي</td>
                                <td id="total-return-perf">0%</td>
                                <td>الهدف: >10%</td>
                                <td><span class="metric-neutral">-</span></td>
                            </tr>
                            <tr>
                                <td>نسبة شارب</td>
                                <td id="sharpe-ratio-perf">0</td>
                                <td>الهدف: >1.5</td>
                                <td><span class="metric-neutral">-</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // AI Trading Platform JavaScript
        let socket;
        let isConnected = false;
        let aiSignals = {};
        let tradingConfig = {};
        let performanceData = {};

        // Initialize platform
        function initializePlatform() {
            initializeSocket();
            loadTradingConfig();
            updateUI();
        }

        // Initialize Socket.IO
        function initializeSocket() {
            socket = io();

            socket.on('connect', function() {
                isConnected = true;
                showNotification('تم الاتصال بمنصة التداول الذكية', 'success');
                updateConnectionStatus(true);
            });

            socket.on('disconnect', function() {
                isConnected = false;
                showNotification('انقطع الاتصال بالمنصة', 'error');
                updateConnectionStatus(false);
            });

            socket.on('ai_analysis_complete', function(data) {
                updateAISignals(data);
            });

            socket.on('trade_executed', function(data) {
                updateTradeExecution(data);
                showNotification(`تم تنفيذ صفقة: ${data.signal.symbol} ${data.signal.signal_type}`, 'success');
            });

            socket.on('training_status', function(data) {
                updateTrainingStatus(data);
            });

            socket.on('scalping_signals', function(data) {
                updateScalpingSignals(data);
            });
        }

        // Tab management
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // AI Functions
        function trainAIModels() {
            if (!isConnected) {
                showNotification('غير متصل بالخادم', 'error');
                return;
            }

            fetch('/api/ai/train_models', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    showNotification('تم بدء تدريب نماذج الذكاء الاصطناعي', 'success');
                })
                .catch(error => {
                    showNotification('خطأ في تدريب النماذج', 'error');
                });
        }

        function startAutoTrading() {
            if (!isConnected) {
                showNotification('غير متصل بالخادم', 'error');
                return;
            }

            fetch('/api/ai/start_trading', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    document.getElementById('trading-status').textContent = 'يعمل';
                    document.getElementById('trading-status').className = 'metric-value metric-positive';
                    showNotification('تم تشغيل التداول الآلي', 'success');
                })
                .catch(error => {
                    showNotification('خطأ في تشغيل التداول الآلي', 'error');
                });
        }

        function stopAutoTrading() {
            fetch('/api/ai/stop_trading', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    document.getElementById('trading-status').textContent = 'متوقف';
                    document.getElementById('trading-status').className = 'metric-value metric-warning';
                    showNotification('تم إيقاف التداول الآلي', 'warning');
                })
                .catch(error => {
                    showNotification('خطأ في إيقاف التداول الآلي', 'error');
                });
        }

        function generateAISignals() {
            if (!isConnected) return;

            socket.emit('request_ai_analysis', {symbol: 'BTC-USDT'});
            socket.emit('request_ai_analysis', {symbol: 'ETH-USDT'});
            socket.emit('request_ai_analysis', {symbol: 'PIXEL-USDT'});

            showNotification('جاري توليد إشارات الذكاء الاصطناعي...', 'info');
        }

        // Utility functions
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const text = document.getElementById('notification-text');

            text.textContent = message;
            notification.className = `notification show ${type}`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 4000);
        }

        function updateConnectionStatus(connected) {
            const indicators = document.querySelectorAll('.ai-indicator');
            indicators.forEach(indicator => {
                indicator.style.background = connected ? '#4caf50' : '#f44336';
            });
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializePlatform();
        });
    </script>
</body>
</html>
        """
        return html_template

    def run(self, host='127.0.0.1', port=5005, debug=False):
        """Run the AI trading platform"""
        print(f"""
        ╔══════════════════════════════════════════════════════════════╗
        ║                                                              ║
        ║        🤖 منصة التداول الذكية بالذكاء الاصطناعي           ║
        ║                                                              ║
        ╚══════════════════════════════════════════════════════════════╝

        🌐 رابط المنصة الذكية: http://{host}:{port}
        🚀 بدء تشغيل منصة التداول بالذكاء الاصطناعي...

        🤖 الميزات الذكية:
        🧠 خوارزميات التعلم الآلي المتقدمة (LSTM, Random Forest, XGBoost)
        ⚡ المضاربة السريعة التلقائية (Scalping)
        🎯 نظام إدارة المخاطر الذكي
        📊 اختبار الاستراتيجيات المتقدم (Backtesting)
        🔄 تنفيذ الصفقات التلقائي على KuCoin
        📈 تحليل السوق المستمر وكشف الفرص
        🛡️ إدارة المخاطر التلقائية
        📱 واجهة مستخدم احترافية
        """)

        # Start background tasks
        self.start_background_tasks()

        try:
            self.socketio.run(self.app, host=host, port=port, debug=debug)
        finally:
            self.stop_background_tasks()


def main():
    """Main function"""
    try:
        platform = AITradingPlatform()
        platform.run(host='0.0.0.0', port=5005, debug=False)

    except KeyboardInterrupt:
        print("\n👋 تم إيقاف منصة التداول الذكية بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل منصة التداول الذكية: {e}")
        print("\n💡 لتثبيت المتطلبات:")
        print("   pip install tensorflow scikit-learn xgboost flask flask-socketio")


if __name__ == "__main__":
    main()
