#!/usr/bin/env python3
"""
Technical Analysis Demo - Real-world usage example
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.analysis import TechnicalIndicators, PatternRecognition, MarketAnalyzer
from src.data import BinanceClient, DataCollector
from src.core.logger import logger


async def analyze_crypto_market():
    """Comprehensive crypto market analysis demo"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🤖 AI Crypto Trading Bot - Technical Analysis Demo   ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Initialize components
    binance_client = BinanceClient(testnet=True)
    collector = DataCollector(binance_client)
    analyzer = MarketAnalyzer()
    
    # Disable database/Redis for demo
    collector.use_database = False
    collector.use_redis = False
    
    try:
        await binance_client.connect()
        print("✅ Connected to Binance API")
        
        # Symbols to analyze
        symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT']
        
        print(f"\n📊 Analyzing {len(symbols)} cryptocurrency pairs...")
        print("=" * 60)
        
        # Collect data for all symbols
        symbols_data = {}
        for symbol in symbols:
            try:
                df = await collector.collect_historical_data(symbol, "1h", days=7)
                if not df.empty:
                    symbols_data[symbol] = df
                    print(f"✅ {symbol}: {len(df)} data points collected")
                else:
                    print(f"❌ {symbol}: No data available")
            except Exception as e:
                print(f"❌ {symbol}: Error collecting data - {e}")
        
        if not symbols_data:
            print("❌ No data collected. Exiting.")
            return
        
        print(f"\n🔍 Performing technical analysis...")
        print("=" * 60)
        
        # Analyze all symbols
        signals = await analyzer.analyze_multiple_symbols(symbols_data)
        
        # Display individual analysis
        for symbol, signal in signals.items():
            print(f"\n📈 {symbol} Analysis:")
            print(f"   Signal: {signal.signal_type}")
            print(f"   Confidence: {signal.confidence:.1%}")
            print(f"   Strength: {signal.strength:.3f}")
            print(f"   Risk Level: {signal.risk_level}")
            print(f"   Current Price: ${signal.entry_price:,.2f}")
            
            if signal.target_price:
                potential_return = ((signal.target_price - signal.entry_price) / signal.entry_price) * 100
                print(f"   Target Price: ${signal.target_price:,.2f} ({potential_return:+.1f}%)")
            
            if signal.stop_loss:
                risk_percent = ((signal.stop_loss - signal.entry_price) / signal.entry_price) * 100
                print(f"   Stop Loss: ${signal.stop_loss:,.2f} ({risk_percent:+.1f}%)")
            
            # Technical indicators summary
            tech_signals = []
            for name, tech_signal in signal.technical_signals.items():
                if tech_signal.signal != 'HOLD':
                    tech_signals.append(f"{name}:{tech_signal.signal}")
            
            if tech_signals:
                print(f"   Active Indicators: {', '.join(tech_signals)}")
            
            # Pattern summary
            if signal.pattern_signals:
                bullish_patterns = [p for p in signal.pattern_signals if p.pattern_type == 'BULLISH']
                bearish_patterns = [p for p in signal.pattern_signals if p.pattern_type == 'BEARISH']
                
                if bullish_patterns:
                    print(f"   Bullish Patterns: {len(bullish_patterns)}")
                if bearish_patterns:
                    print(f"   Bearish Patterns: {len(bearish_patterns)}")
            
            print(f"   Reasoning: {signal.reasoning}")
        
        # Market overview
        print(f"\n📊 Market Overview:")
        print("=" * 60)
        
        overview = analyzer.get_market_overview(signals)
        
        print(f"Total Symbols Analyzed: {overview['total_symbols']}")
        print(f"Market Sentiment: {overview['market_sentiment']}")
        print(f"Average Confidence: {overview['average_confidence']:.1%}")
        print(f"")
        print(f"Signal Distribution:")
        print(f"  🟢 Buy Signals: {overview['buy_signals']}")
        print(f"  🔴 Sell Signals: {overview['sell_signals']}")
        print(f"  ⚪ Hold Signals: {overview['hold_signals']}")
        print(f"  🎯 High Confidence: {overview['high_confidence_signals']}")
        
        # Top opportunities
        if overview['top_opportunities']:
            print(f"\n🎯 Top Trading Opportunities:")
            print("-" * 40)
            
            for i, opp in enumerate(overview['top_opportunities'], 1):
                print(f"{i}. {opp['symbol']}: {opp['signal']} "
                      f"(Confidence: {opp['confidence']:.1%}, "
                      f"Strength: {opp['strength']:.3f})")
        
        # Risk analysis
        print(f"\n⚠️  Risk Analysis:")
        print("-" * 40)
        
        risk_levels = {'LOW': 0, 'MEDIUM': 0, 'HIGH': 0}
        for signal in signals.values():
            risk_levels[signal.risk_level] += 1
        
        print(f"Low Risk Signals: {risk_levels['LOW']}")
        print(f"Medium Risk Signals: {risk_levels['MEDIUM']}")
        print(f"High Risk Signals: {risk_levels['HIGH']}")
        
        # Trading recommendations
        print(f"\n💡 Trading Recommendations:")
        print("-" * 40)
        
        high_confidence_buys = [s for s in signals.values() 
                               if s.signal_type == 'BUY' and s.confidence > 0.7]
        high_confidence_sells = [s for s in signals.values() 
                                if s.signal_type == 'SELL' and s.confidence > 0.7]
        
        if high_confidence_buys:
            print("🟢 Strong Buy Candidates:")
            for signal in sorted(high_confidence_buys, key=lambda x: x.confidence, reverse=True):
                print(f"   {signal.symbol}: {signal.confidence:.1%} confidence, {signal.risk_level} risk")
        
        if high_confidence_sells:
            print("🔴 Strong Sell Candidates:")
            for signal in sorted(high_confidence_sells, key=lambda x: x.confidence, reverse=True):
                print(f"   {signal.symbol}: {signal.confidence:.1%} confidence, {signal.risk_level} risk")
        
        if not high_confidence_buys and not high_confidence_sells:
            print("⚪ No high-confidence signals detected. Consider waiting for better opportunities.")
        
        # Market conditions
        print(f"\n🌡️  Market Conditions:")
        print("-" * 40)
        
        if overview['market_sentiment'] == 'BULLISH':
            print("📈 Market is showing bullish sentiment")
            print("   Consider: Long positions, breakout strategies")
        elif overview['market_sentiment'] == 'BEARISH':
            print("📉 Market is showing bearish sentiment")
            print("   Consider: Short positions, defensive strategies")
        else:
            print("📊 Market is neutral/consolidating")
            print("   Consider: Range trading, wait for clear direction")
        
        print(f"\n✅ Analysis Complete!")
        print(f"📊 Analyzed {len(signals)} symbols with {overview['average_confidence']:.1%} average confidence")
        
    except Exception as e:
        logger.error(f"Demo error: {e}")
        print(f"❌ Demo failed: {e}")
    
    finally:
        await binance_client.disconnect()
        print("\n👋 Demo finished!")


async def detailed_single_analysis():
    """Detailed analysis of a single cryptocurrency"""
    print("\n" + "=" * 60)
    print("🔍 DETAILED SINGLE SYMBOL ANALYSIS")
    print("=" * 60)
    
    symbol = "BTCUSDT"
    
    # Initialize components
    binance_client = BinanceClient(testnet=True)
    collector = DataCollector(binance_client)
    indicators = TechnicalIndicators()
    patterns = PatternRecognition()
    
    collector.use_database = False
    collector.use_redis = False
    
    try:
        await binance_client.connect()
        
        # Get data
        df = await collector.collect_historical_data(symbol, "1h", days=14)
        
        if df.empty:
            print(f"❌ No data available for {symbol}")
            return
        
        print(f"📊 Analyzing {symbol} - {len(df)} data points")
        print(f"   Period: {df.index.min()} to {df.index.max()}")
        print(f"   Price Range: ${df['low'].min():,.2f} - ${df['high'].max():,.2f}")
        print(f"   Current Price: ${df['close'].iloc[-1]:,.2f}")
        
        # Technical Indicators
        print(f"\n📈 Technical Indicators:")
        print("-" * 30)
        
        # Moving Averages
        sma_20 = indicators.sma(df['close'], 20).iloc[-1]
        ema_20 = indicators.ema(df['close'], 20).iloc[-1]
        print(f"SMA(20): ${sma_20:,.2f}")
        print(f"EMA(20): ${ema_20:,.2f}")
        
        # Oscillators
        rsi = indicators.rsi(df['close']).iloc[-1]
        print(f"RSI(14): {rsi:.1f}")
        
        if rsi < 30:
            print("   → Oversold condition")
        elif rsi > 70:
            print("   → Overbought condition")
        else:
            print("   → Neutral zone")
        
        # MACD
        macd_line, signal_line, histogram = indicators.macd(df['close'])
        print(f"MACD: {macd_line.iloc[-1]:.2f}")
        print(f"Signal: {signal_line.iloc[-1]:.2f}")
        print(f"Histogram: {histogram.iloc[-1]:.2f}")
        
        if macd_line.iloc[-1] > signal_line.iloc[-1]:
            print("   → Bullish MACD")
        else:
            print("   → Bearish MACD")
        
        # Bollinger Bands
        upper_bb, sma_bb, lower_bb = indicators.bollinger_bands(df['close'])
        current_price = df['close'].iloc[-1]
        bb_position = (current_price - lower_bb.iloc[-1]) / (upper_bb.iloc[-1] - lower_bb.iloc[-1])
        
        print(f"Bollinger Bands:")
        print(f"   Upper: ${upper_bb.iloc[-1]:,.2f}")
        print(f"   Middle: ${sma_bb.iloc[-1]:,.2f}")
        print(f"   Lower: ${lower_bb.iloc[-1]:,.2f}")
        print(f"   Position: {bb_position:.1%}")
        
        # Volume Analysis
        obv = indicators.obv(df['close'], df['volume']).iloc[-1]
        avg_volume = df['volume'].tail(20).mean()
        current_volume = df['volume'].iloc[-1]
        
        print(f"\nVolume Analysis:")
        print(f"   Current Volume: {current_volume:,.0f}")
        print(f"   20-day Average: {avg_volume:,.0f}")
        print(f"   Volume Ratio: {current_volume/avg_volume:.1f}x")
        print(f"   OBV: {obv:,.0f}")
        
        # Pattern Recognition
        print(f"\n🔍 Pattern Analysis:")
        print("-" * 30)
        
        # Support and Resistance
        support_resistance = patterns.detect_support_resistance(df)
        
        print(f"Support Levels:")
        for level in support_resistance['support'][-3:]:  # Last 3
            distance = ((current_price - level) / level) * 100
            print(f"   ${level:,.2f} ({distance:+.1f}%)")
        
        print(f"Resistance Levels:")
        for level in support_resistance['resistance'][:3]:  # First 3
            distance = ((level - current_price) / current_price) * 100
            print(f"   ${level:,.2f} (+{distance:.1f}%)")
        
        # Candlestick Patterns
        candlestick_patterns = patterns.detect_candlestick_patterns(df.tail(20))
        
        if candlestick_patterns:
            print(f"\nRecent Candlestick Patterns:")
            pattern_counts = {}
            for pattern in candlestick_patterns:
                pattern_counts[pattern.pattern_name] = pattern_counts.get(pattern.pattern_name, 0) + 1
            
            for pattern_name, count in pattern_counts.items():
                print(f"   {pattern_name}: {count}")
        
        # Trend Analysis
        trend_analysis = indicators.get_trend_strength(df)
        print(f"\nTrend Analysis:")
        print(f"   Direction: {trend_analysis['trend_direction']}")
        print(f"   Strength: {trend_analysis['trend_strength']:.4f}")
        
        factors = trend_analysis.get('factors', {})
        for factor_name, value in factors.items():
            print(f"   {factor_name}: {value:+.4f}")
        
    except Exception as e:
        print(f"❌ Detailed analysis failed: {e}")
    
    finally:
        await binance_client.disconnect()


async def main():
    """Main demo function"""
    try:
        # Run comprehensive market analysis
        await analyze_crypto_market()
        
        # Run detailed single symbol analysis
        await detailed_single_analysis()
        
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
