"""
Order Management System for AI Crypto Trading Bot
"""

import asyncio
from typing import Dict, List, Optional, Union
from decimal import Decimal
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..data.kucoin_client import KuCoinClient, KuCoinAPIError
from ..core.config import settings
from ..core.logger import logger, trading_logger


class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "pending"
    ACTIVE = "active"
    FILLED = "filled"
    CANCELLED = "cancelled"
    FAILED = "failed"


class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop"
    TAKE_PROFIT = "take_profit"


@dataclass
class OrderRequest:
    """Order request structure"""
    symbol: str
    side: str  # 'buy' or 'sell'
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"  # Good Till Cancelled
    client_order_id: Optional[str] = None


@dataclass
class Order:
    """Order structure"""
    order_id: str
    client_order_id: str
    symbol: str
    side: str
    order_type: str
    quantity: float
    price: float
    filled_quantity: float
    remaining_quantity: float
    status: OrderStatus
    created_at: datetime
    updated_at: datetime
    commission: float = 0.0
    commission_asset: str = ""
    
    def to_dict(self) -> Dict:
        return {
            'order_id': self.order_id,
            'client_order_id': self.client_order_id,
            'symbol': self.symbol,
            'side': self.side,
            'order_type': self.order_type,
            'quantity': self.quantity,
            'price': self.price,
            'filled_quantity': self.filled_quantity,
            'remaining_quantity': self.remaining_quantity,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'commission': self.commission,
            'commission_asset': self.commission_asset
        }


class OrderManager:
    """
    Advanced order management system for KuCoin trading
    """
    
    def __init__(self, kucoin_client: KuCoinClient = None):
        self.kucoin_client = kucoin_client or KuCoinClient()
        self.active_orders: Dict[str, Order] = {}
        self.order_history: List[Order] = []
        self.dry_run = True  # Start in dry run mode for safety
        
        # Order tracking
        self.order_update_interval = 5  # seconds
        self.monitoring_task: Optional[asyncio.Task] = None
        
        logger.info("Order Manager initialized")
    
    def set_dry_run(self, dry_run: bool):
        """Enable/disable dry run mode"""
        self.dry_run = dry_run
        mode = "DRY RUN" if dry_run else "LIVE TRADING"
        logger.warning(f"Order Manager mode set to: {mode}")
    
    async def place_order(self, order_request: OrderRequest) -> Optional[Order]:
        """
        Place a new order
        """
        try:
            # Validate order request
            if not await self._validate_order_request(order_request):
                return None
            
            # Generate client order ID if not provided
            if not order_request.client_order_id:
                order_request.client_order_id = f"AI_BOT_{int(datetime.now().timestamp() * 1000)}"
            
            if self.dry_run:
                # Simulate order placement
                order = await self._simulate_order(order_request)
                logger.info(f"🧪 DRY RUN: Simulated order placed - {order.symbol} {order.side} {order.quantity}")
            else:
                # Place real order
                order = await self._place_real_order(order_request)
                logger.info(f"📋 LIVE: Real order placed - {order.symbol} {order.side} {order.quantity}")
            
            if order:
                self.active_orders[order.order_id] = order
                
                # Log trade attempt
                trading_logger.trade_executed(
                    symbol=order.symbol,
                    side=order.side,
                    quantity=order.quantity,
                    price=order.price,
                    order_id=order.order_id
                )
            
            return order
            
        except Exception as e:
            logger.error(f"Failed to place order: {e}")
            trading_logger.trade_failed(
                symbol=order_request.symbol,
                side=order_request.side,
                reason="Order placement failed",
                error=str(e)
            )
            return None
    
    async def _validate_order_request(self, order_request: OrderRequest) -> bool:
        """Validate order request"""
        try:
            # Check if symbol is valid and trading
            if not await self.kucoin_client.is_symbol_trading(order_request.symbol):
                logger.error(f"Symbol {order_request.symbol} is not trading")
                return False
            
            # Validate quantity
            if order_request.quantity <= 0:
                logger.error("Order quantity must be positive")
                return False
            
            # Validate price for limit orders
            if order_request.order_type == OrderType.LIMIT and not order_request.price:
                logger.error("Limit orders require a price")
                return False
            
            # Check minimum order size
            symbol_info = await self.kucoin_client.get_symbol_info(order_request.symbol)
            if symbol_info:
                min_size = float(symbol_info.get('baseMinSize', 0))
                if order_request.quantity < min_size:
                    logger.error(f"Order quantity {order_request.quantity} below minimum {min_size}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Order validation failed: {e}")
            return False
    
    async def _simulate_order(self, order_request: OrderRequest) -> Order:
        """Simulate order for dry run mode"""
        # Get current market price
        ticker = await self.kucoin_client.get_ticker(order_request.symbol)
        current_price = float(ticker.get('last', 0))
        
        # Use market price for market orders, specified price for limit orders
        order_price = current_price if order_request.order_type == OrderType.MARKET else order_request.price
        
        order = Order(
            order_id=f"SIM_{int(datetime.now().timestamp() * 1000)}",
            client_order_id=order_request.client_order_id,
            symbol=order_request.symbol,
            side=order_request.side,
            order_type=order_request.order_type.value,
            quantity=order_request.quantity,
            price=order_price,
            filled_quantity=order_request.quantity if order_request.order_type == OrderType.MARKET else 0,
            remaining_quantity=0 if order_request.order_type == OrderType.MARKET else order_request.quantity,
            status=OrderStatus.FILLED if order_request.order_type == OrderType.MARKET else OrderStatus.ACTIVE,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        return order
    
    async def _place_real_order(self, order_request: OrderRequest) -> Order:
        """Place real order on KuCoin"""
        try:
            # Prepare order parameters
            order_params = {
                'symbol': order_request.symbol,
                'side': order_request.side,
                'order_type': order_request.order_type.value,
                'size': order_request.quantity,
                'client_oid': order_request.client_order_id
            }
            
            if order_request.order_type == OrderType.LIMIT:
                order_params['price'] = order_request.price
            elif order_request.order_type == OrderType.MARKET and order_request.side == 'buy':
                # For market buy orders, KuCoin requires 'funds' instead of 'size'
                ticker = await self.kucoin_client.get_ticker(order_request.symbol)
                current_price = float(ticker.get('last', 0))
                order_params['funds'] = order_request.quantity * current_price
                del order_params['size']
            
            # Place order
            result = await self.kucoin_client.place_order(**order_params)
            
            # Get order details
            order_details = await self.kucoin_client.get_order(result['orderId'])
            
            # Create order object
            order = Order(
                order_id=result['orderId'],
                client_order_id=order_request.client_order_id,
                symbol=order_request.symbol,
                side=order_request.side,
                order_type=order_request.order_type.value,
                quantity=float(order_details.get('size', order_request.quantity)),
                price=float(order_details.get('price', order_request.price or 0)),
                filled_quantity=float(order_details.get('dealSize', 0)),
                remaining_quantity=float(order_details.get('size', 0)) - float(order_details.get('dealSize', 0)),
                status=OrderStatus.ACTIVE if order_details.get('isActive') else OrderStatus.FILLED,
                created_at=datetime.fromtimestamp(int(order_details.get('createdAt', 0)) / 1000),
                updated_at=datetime.now()
            )
            
            return order
            
        except KuCoinAPIError as e:
            logger.error(f"KuCoin API error placing order: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error placing order: {e}")
            raise
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an active order"""
        try:
            if order_id not in self.active_orders:
                logger.warning(f"Order {order_id} not found in active orders")
                return False
            
            if self.dry_run:
                # Simulate cancellation
                order = self.active_orders[order_id]
                order.status = OrderStatus.CANCELLED
                order.updated_at = datetime.now()
                logger.info(f"🧪 DRY RUN: Simulated order cancellation - {order_id}")
            else:
                # Cancel real order
                await self.kucoin_client.cancel_order(order_id)
                order = self.active_orders[order_id]
                order.status = OrderStatus.CANCELLED
                order.updated_at = datetime.now()
                logger.info(f"❌ LIVE: Order cancelled - {order_id}")
            
            # Move to history
            self.order_history.append(self.active_orders[order_id])
            del self.active_orders[order_id]
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel order {order_id}: {e}")
            return False
    
    async def get_order_status(self, order_id: str) -> Optional[Order]:
        """Get current order status"""
        if order_id in self.active_orders:
            if not self.dry_run:
                # Update from exchange
                try:
                    order_details = await self.kucoin_client.get_order(order_id)
                    order = self.active_orders[order_id]
                    
                    # Update order details
                    order.filled_quantity = float(order_details.get('dealSize', 0))
                    order.remaining_quantity = order.quantity - order.filled_quantity
                    order.status = OrderStatus.FILLED if not order_details.get('isActive') else OrderStatus.ACTIVE
                    order.updated_at = datetime.now()
                    
                    # Move to history if filled
                    if order.status == OrderStatus.FILLED:
                        self.order_history.append(order)
                        del self.active_orders[order_id]
                    
                except Exception as e:
                    logger.error(f"Failed to update order status: {e}")
            
            return self.active_orders.get(order_id)
        
        # Check history
        for order in self.order_history:
            if order.order_id == order_id:
                return order
        
        return None
    
    async def start_monitoring(self):
        """Start monitoring active orders"""
        if self.monitoring_task and not self.monitoring_task.done():
            return
        
        self.monitoring_task = asyncio.create_task(self._monitor_orders())
        logger.info("Started order monitoring")
    
    async def stop_monitoring(self):
        """Stop monitoring active orders"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped order monitoring")
    
    async def _monitor_orders(self):
        """Monitor active orders for updates"""
        while True:
            try:
                if self.active_orders and not self.dry_run:
                    for order_id in list(self.active_orders.keys()):
                        await self.get_order_status(order_id)
                
                await asyncio.sleep(self.order_update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring orders: {e}")
                await asyncio.sleep(self.order_update_interval)
    
    def get_active_orders(self) -> List[Order]:
        """Get all active orders"""
        return list(self.active_orders.values())
    
    def get_order_history(self, limit: int = 100) -> List[Order]:
        """Get order history"""
        return self.order_history[-limit:]
    
    def get_orders_by_symbol(self, symbol: str) -> List[Order]:
        """Get all orders for a specific symbol"""
        symbol_orders = []
        
        # Active orders
        for order in self.active_orders.values():
            if order.symbol == symbol:
                symbol_orders.append(order)
        
        # Historical orders
        for order in self.order_history:
            if order.symbol == symbol:
                symbol_orders.append(order)
        
        return sorted(symbol_orders, key=lambda x: x.created_at, reverse=True)
