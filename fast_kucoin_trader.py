#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
منصة التداول السريعة والفعالة مع KuCoin
Fast & Efficient KuCoin Trading Platform

منصة تداول حقيقية سريعة:
- اتصال مباشر وسريع مع KuCoin
- تنفيذ صفقات فوري
- واجهة بسيطة وسريعة
- إدارة مخاطر فعالة
"""

import requests
import json
import time
import hashlib
import hmac
import base64
from datetime import datetime
from flask import Flask, jsonify, render_template_string, request

# KuCoin API Configuration
API_KEY = "686a4e782301b10001e7457c"
SECRET_KEY = "61718954-dc69-4b89-b21c-dff5b80fff15"
PASSPHRASE = "Eslam*17*3*1999"
BASE_URL = "https://api.kucoin.com"

class FastKuCoinTrader:
    """متداول KuCoin السريع"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 10  # 10 seconds timeout
        
        # Trading state
        self.portfolio = {}
        self.last_prices = {}
        self.signals = []
        self.trades = []
        
        print("🚀 Fast KuCoin Trader initialized")
    
    def _generate_signature(self, timestamp, method, endpoint, body=""):
        """توليد التوقيع بسرعة"""
        message = timestamp + method.upper() + endpoint + body
        signature = base64.b64encode(
            hmac.new(SECRET_KEY.encode(), message.encode(), hashlib.sha256).digest()
        ).decode()
        return signature
    
    def _get_headers(self, method, endpoint, body=""):
        """الحصول على headers بسرعة"""
        timestamp = str(int(time.time() * 1000))
        signature = self._generate_signature(timestamp, method, endpoint, body)
        passphrase = base64.b64encode(
            hmac.new(SECRET_KEY.encode(), PASSPHRASE.encode(), hashlib.sha256).digest()
        ).decode()
        
        return {
            "KC-API-KEY": API_KEY,
            "KC-API-SIGN": signature,
            "KC-API-TIMESTAMP": timestamp,
            "KC-API-PASSPHRASE": passphrase,
            "KC-API-KEY-VERSION": "2",
            "Content-Type": "application/json"
        }
    
    def _request(self, method, endpoint, params=None, data=None, signed=False):
        """طلب HTTP سريع"""
        url = BASE_URL + endpoint
        headers = {}
        
        if signed:
            body = json.dumps(data) if data else ""
            headers = self._get_headers(method, endpoint, body)
            data = body if body else None
        
        try:
            response = self.session.request(method, url, params=params, data=data, headers=headers)
            result = response.json()
            
            if response.status_code == 200 and result.get("code") == "200000":
                return result.get("data", {})
            else:
                print(f"❌ API Error: {result.get('msg', 'Unknown error')}")
                return None
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
            return None
    
    def test_connection(self):
        """اختبار الاتصال السريع"""
        print("🔍 Testing KuCoin connection...")
        
        # Test public endpoint
        server_time = self._request("GET", "/api/v1/timestamp")
        if not server_time:
            return False
        
        # Test private endpoint
        accounts = self._request("GET", "/api/v1/accounts", signed=True)
        if not accounts:
            return False
        
        print(f"✅ Connection successful! Server time: {server_time}")
        print(f"✅ Account access OK! Found {len(accounts)} accounts")
        return True
    
    def get_portfolio(self):
        """الحصول على المحفظة بسرعة"""
        print("💰 Getting portfolio...")
        
        accounts = self._request("GET", "/api/v1/accounts", signed=True)
        if not accounts:
            return {}
        
        portfolio = {}
        total_value = 0
        
        # Get current prices
        tickers = self._request("GET", "/api/v1/market/allTickers")
        prices = {}
        if tickers and "ticker" in tickers:
            for ticker in tickers["ticker"]:
                if ticker["symbol"].endswith("-USDT"):
                    symbol = ticker["symbol"].replace("-USDT", "")
                    prices[symbol] = float(ticker["last"])
        
        # Calculate portfolio
        for account in accounts:
            if account["type"] == "trade" and float(account["balance"]) > 0:
                currency = account["currency"]
                balance = float(account["balance"])
                available = float(account["available"])
                
                if currency == "USDT":
                    value = balance
                else:
                    price = prices.get(currency, 0)
                    value = balance * price
                
                total_value += value
                portfolio[currency] = {
                    "balance": balance,
                    "available": available,
                    "price": prices.get(currency, 1.0 if currency == "USDT" else 0),
                    "value": value
                }
        
        self.portfolio = {
            "total_value": total_value,
            "balances": portfolio,
            "updated": datetime.now().strftime("%H:%M:%S")
        }
        
        print(f"✅ Portfolio updated - Total: ${total_value:.2f}")
        return self.portfolio
    
    def get_price(self, symbol):
        """الحصول على السعر بسرعة"""
        ticker = self._request("GET", f"/api/v1/market/stats?symbol={symbol}")
        if ticker:
            price = float(ticker["last"])
            self.last_prices[symbol] = price
            return price
        return None
    
    def analyze_symbol(self, symbol):
        """تحليل سريع للرمز"""
        print(f"📊 Analyzing {symbol}...")
        
        # Get current price and stats
        ticker = self._request("GET", f"/api/v1/market/stats?symbol={symbol}")
        if not ticker:
            return None
        
        current_price = float(ticker["last"])
        change_rate = float(ticker["changeRate"]) * 100
        volume = float(ticker["vol"])
        
        # Simple but effective analysis
        signal_type = "HOLD"
        confidence = 0.5
        reasoning = "Market analysis: "
        
        # Price momentum analysis
        if change_rate > 2:
            signal_type = "BUY"
            confidence = 0.7
            reasoning += f"Strong upward momentum (+{change_rate:.1f}%), "
        elif change_rate < -2:
            signal_type = "SELL"
            confidence = 0.7
            reasoning += f"Strong downward momentum ({change_rate:.1f}%), "
        
        # Volume analysis
        if volume > 1000:  # High volume threshold
            confidence += 0.1
            reasoning += "High volume, "
        
        # Risk assessment
        volatility = abs(change_rate)
        if volatility > 5:
            risk_level = "HIGH"
            confidence *= 0.8
        elif volatility > 2:
            risk_level = "MEDIUM"
        else:
            risk_level = "LOW"
        
        signal = {
            "symbol": symbol,
            "signal_type": signal_type,
            "confidence": round(confidence, 2),
            "current_price": current_price,
            "change_24h": change_rate,
            "volume_24h": volume,
            "risk_level": risk_level,
            "reasoning": reasoning.rstrip(", "),
            "timestamp": datetime.now().strftime("%H:%M:%S")
        }
        
        self.signals.append(signal)
        print(f"✅ {symbol}: {signal_type} ({confidence:.0%}) - {reasoning}")
        return signal
    
    def place_order(self, symbol, side, amount):
        """وضع أمر بسرعة"""
        print(f"🔥 Placing {side} order: {symbol} - ${amount}")
        
        try:
            data = {
                "clientOid": f"fast_trader_{int(time.time())}",
                "symbol": symbol,
                "side": side.lower(),
                "type": "market"
            }
            
            if side.lower() == "buy":
                data["funds"] = str(amount)  # USDT amount
            else:
                # For sell, we need crypto amount
                current_price = self.get_price(symbol)
                if current_price:
                    crypto_amount = amount / current_price
                    data["size"] = str(crypto_amount)
                else:
                    print("❌ Cannot get current price for sell order")
                    return None
            
            result = self._request("POST", "/api/v1/orders", data=data, signed=True)
            
            if result:
                trade = {
                    "order_id": result.get("orderId", ""),
                    "symbol": symbol,
                    "side": side,
                    "amount": amount,
                    "status": "FILLED",
                    "timestamp": datetime.now().strftime("%H:%M:%S")
                }
                
                self.trades.append(trade)
                print(f"✅ Order executed! Order ID: {trade['order_id']}")
                return trade
            else:
                print("❌ Order failed!")
                return None
                
        except Exception as e:
            print(f"❌ Order error: {e}")
            return None
    
    def quick_trade(self, symbol, amount=20):
        """تداول سريع"""
        print(f"⚡ Quick trading {symbol} with ${amount}")
        
        # Analyze
        signal = self.analyze_symbol(symbol)
        if not signal:
            return False
        
        # Trade if signal is strong
        if signal["confidence"] >= 0.7 and signal["signal_type"] in ["BUY", "SELL"]:
            trade = self.place_order(symbol, signal["signal_type"], amount)
            return trade is not None
        else:
            print(f"⏸️ Signal not strong enough: {signal['signal_type']} ({signal['confidence']:.0%})")
            return False


# Flask Web Interface
app = Flask(__name__)
trader = FastKuCoinTrader()

@app.route('/')
def index():
    """الصفحة الرئيسية السريعة"""
    html = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ متداول KuCoin السريع</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .card h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        .btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 5px;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        .btn-success { background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); }
        .btn-warning { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%); }
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .metric:last-child { border-bottom: none; }
        .metric-value {
            font-weight: bold;
            color: #ffd700;
        }
        .signals, .trades {
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        .signal-item, .trade-item {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .signal-buy { border-left: 4px solid #00b894; }
        .signal-sell { border-left: 4px solid #d63031; }
        .signal-hold { border-left: 4px solid #fdcb6e; }
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 1000;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        input[type="number"] {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 5px;
            padding: 8px;
            color: white;
            width: 100px;
            margin: 0 10px;
        }
        input[type="number"]::placeholder {
            color: rgba(255,255,255,0.7);
        }
        select {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 5px;
            padding: 8px;
            color: white;
            margin: 0 10px;
        }
        option {
            background: #333;
            color: white;
        }
    </style>
</head>
<body>
    <div id="status" class="status" style="display: none;">جاري التحميل...</div>

    <div class="container">
        <div class="header">
            <h1>⚡ متداول KuCoin السريع</h1>
            <p>منصة تداول حقيقية سريعة وفعالة</p>
        </div>

        <div class="grid">
            <!-- Control Panel -->
            <div class="card">
                <h3>🎮 لوحة التحكم</h3>
                <button class="btn" onclick="testConnection()">🔍 اختبار الاتصال</button>
                <button class="btn btn-success" onclick="updatePortfolio()">💰 تحديث المحفظة</button>
                <button class="btn btn-warning" onclick="analyzeMarket()">📊 تحليل السوق</button>
                <hr style="margin: 15px 0; border: 1px solid rgba(255,255,255,0.2);">
                <div style="margin: 10px 0;">
                    <select id="symbol">
                        <option value="BTC-USDT">BTC-USDT</option>
                        <option value="ETH-USDT">ETH-USDT</option>
                        <option value="BNB-USDT">BNB-USDT</option>
                    </select>
                    <input type="number" id="amount" placeholder="20" value="20" min="10" max="100">
                    <button class="btn" onclick="quickTrade()">⚡ تداول سريع</button>
                </div>
            </div>

            <!-- Portfolio -->
            <div class="card">
                <h3>💰 المحفظة</h3>
                <div class="metric">
                    <span>القيمة الإجمالية</span>
                    <span class="metric-value" id="total-value">$0.00</span>
                </div>
                <div class="metric">
                    <span>USDT</span>
                    <span class="metric-value" id="usdt-balance">0.00</span>
                </div>
                <div class="metric">
                    <span>BTC</span>
                    <span class="metric-value" id="btc-balance">0.00000000</span>
                </div>
                <div class="metric">
                    <span>ETH</span>
                    <span class="metric-value" id="eth-balance">0.00000000</span>
                </div>
                <div class="metric">
                    <span>آخر تحديث</span>
                    <span class="metric-value" id="last-update">لم يتم بعد</span>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card">
                <h3>📈 إحصائيات سريعة</h3>
                <div class="metric">
                    <span>إجمالي الإشارات</span>
                    <span class="metric-value" id="total-signals">0</span>
                </div>
                <div class="metric">
                    <span>إجمالي الصفقات</span>
                    <span class="metric-value" id="total-trades">0</span>
                </div>
                <div class="metric">
                    <span>آخر إشارة</span>
                    <span class="metric-value" id="last-signal">لا توجد</span>
                </div>
                <div class="metric">
                    <span>حالة الاتصال</span>
                    <span class="metric-value" id="connection-status">غير متصل</span>
                </div>
            </div>
        </div>

        <!-- Signals -->
        <div class="card">
            <h3>🧠 إشارات التداول</h3>
            <div class="signals" id="signals-container">
                <div style="text-align: center; color: rgba(255,255,255,0.7);">
                    لا توجد إشارات متاحة
                </div>
            </div>
        </div>

        <!-- Trades -->
        <div class="card">
            <h3>💼 الصفقات المنفذة</h3>
            <div class="trades" id="trades-container">
                <div style="text-align: center; color: rgba(255,255,255,0.7);">
                    لا توجد صفقات منفذة
                </div>
            </div>
        </div>
    </div>

    <script>
        let isLoading = false;

        function showStatus(message, duration = 3000) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.display = 'block';
            setTimeout(() => {
                status.style.display = 'none';
            }, duration);
        }

        function setLoading(loading) {
            isLoading = loading;
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.disabled = loading;
                if (loading) {
                    btn.innerHTML = '<div class="loading"></div> جاري التحميل...';
                }
            });
        }

        async function testConnection() {
            if (isLoading) return;
            setLoading(true);
            showStatus('جاري اختبار الاتصال...');

            try {
                const response = await fetch('/api/test');
                const data = await response.json();

                if (data.success) {
                    document.getElementById('connection-status').textContent = 'متصل ✅';
                    showStatus('تم الاتصال بنجاح!');
                } else {
                    document.getElementById('connection-status').textContent = 'خطأ ❌';
                    showStatus('فشل الاتصال: ' + data.error);
                }
            } catch (error) {
                showStatus('خطأ في الاتصال: ' + error.message);
            }

            setLoading(false);
            resetButtons();
        }

        async function updatePortfolio() {
            if (isLoading) return;
            setLoading(true);
            showStatus('جاري تحديث المحفظة...');

            try {
                const response = await fetch('/api/portfolio');
                const data = await response.json();

                if (data.success) {
                    const portfolio = data.portfolio;
                    document.getElementById('total-value').textContent = '$' + portfolio.total_value.toFixed(2);
                    document.getElementById('last-update').textContent = portfolio.updated;

                    // Update balances
                    const balances = portfolio.balances;
                    document.getElementById('usdt-balance').textContent = (balances.USDT?.balance || 0).toFixed(2);
                    document.getElementById('btc-balance').textContent = (balances.BTC?.balance || 0).toFixed(8);
                    document.getElementById('eth-balance').textContent = (balances.ETH?.balance || 0).toFixed(8);

                    showStatus('تم تحديث المحفظة بنجاح!');
                } else {
                    showStatus('فشل تحديث المحفظة: ' + data.error);
                }
            } catch (error) {
                showStatus('خطأ في تحديث المحفظة: ' + error.message);
            }

            setLoading(false);
            resetButtons();
        }

        async function analyzeMarket() {
            if (isLoading) return;
            setLoading(true);
            showStatus('جاري تحليل السوق...');

            try {
                const response = await fetch('/api/analyze');
                const data = await response.json();

                if (data.success) {
                    updateSignals(data.signals);
                    document.getElementById('total-signals').textContent = data.signals.length;
                    if (data.signals.length > 0) {
                        const lastSignal = data.signals[data.signals.length - 1];
                        document.getElementById('last-signal').textContent =
                            `${lastSignal.symbol}: ${lastSignal.signal_type}`;
                    }
                    showStatus('تم تحليل السوق بنجاح!');
                } else {
                    showStatus('فشل تحليل السوق: ' + data.error);
                }
            } catch (error) {
                showStatus('خطأ في تحليل السوق: ' + error.message);
            }

            setLoading(false);
            resetButtons();
        }

        async function quickTrade() {
            if (isLoading) return;

            const symbol = document.getElementById('symbol').value;
            const amount = document.getElementById('amount').value;

            if (!amount || amount < 10) {
                showStatus('يجب أن يكون المبلغ 10 دولار على الأقل');
                return;
            }

            if (!confirm(`هل أنت متأكد من التداول السريع؟\\n${symbol} - $${amount}`)) {
                return;
            }

            setLoading(true);
            showStatus('جاري التداول السريع...');

            try {
                const response = await fetch('/api/quick_trade', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({symbol: symbol, amount: parseFloat(amount)})
                });
                const data = await response.json();

                if (data.success) {
                    if (data.traded) {
                        updateTrades(data.trades);
                        document.getElementById('total-trades').textContent = data.trades.length;
                        showStatus('تم التداول بنجاح! 🎉');
                    } else {
                        showStatus('لم يتم التداول - الإشارة ضعيفة');
                    }
                } else {
                    showStatus('فشل التداول: ' + data.error);
                }
            } catch (error) {
                showStatus('خطأ في التداول: ' + error.message);
            }

            setLoading(false);
            resetButtons();
        }

        function updateSignals(signals) {
            const container = document.getElementById('signals-container');
            if (!signals || signals.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: rgba(255,255,255,0.7);">لا توجد إشارات متاحة</div>';
                return;
            }

            container.innerHTML = signals.slice(-10).reverse().map(signal => `
                <div class="signal-item signal-${signal.signal_type.toLowerCase()}">
                    <div>
                        <strong>${signal.symbol}</strong> - ${signal.signal_type}
                        <br><small>${signal.reasoning}</small>
                    </div>
                    <div style="text-align: left;">
                        <div><strong>${(signal.confidence * 100).toFixed(0)}%</strong></div>
                        <div><small>$${signal.current_price.toFixed(2)}</small></div>
                        <div><small>${signal.timestamp}</small></div>
                    </div>
                </div>
            `).join('');
        }

        function updateTrades(trades) {
            const container = document.getElementById('trades-container');
            if (!trades || trades.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: rgba(255,255,255,0.7);">لا توجد صفقات منفذة</div>';
                return;
            }

            container.innerHTML = trades.slice(-10).reverse().map(trade => `
                <div class="trade-item">
                    <div>
                        <strong>${trade.symbol}</strong> - ${trade.side}
                        <br><small>Order: ${trade.order_id.substring(0, 8)}...</small>
                    </div>
                    <div style="text-align: left;">
                        <div><strong>$${trade.amount}</strong></div>
                        <div><small>${trade.status}</small></div>
                        <div><small>${trade.timestamp}</small></div>
                    </div>
                </div>
            `).join('');
        }

        function resetButtons() {
            document.querySelector('.btn').innerHTML = '🔍 اختبار الاتصال';
            document.querySelectorAll('.btn')[1].innerHTML = '💰 تحديث المحفظة';
            document.querySelectorAll('.btn')[2].innerHTML = '📊 تحليل السوق';
            document.querySelectorAll('.btn')[3].innerHTML = '⚡ تداول سريع';
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            if (!isLoading) {
                updatePortfolio();
            }
        }, 30000);
    </script>
</body>
</html>
    """
    return render_template_string(html)

@app.route('/api/test')
def api_test():
    """اختبار الاتصال"""
    try:
        success = trader.test_connection()
        return jsonify({"success": success})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/portfolio')
def api_portfolio():
    """الحصول على المحفظة"""
    try:
        portfolio = trader.get_portfolio()
        return jsonify({"success": True, "portfolio": portfolio})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/analyze')
def api_analyze():
    """تحليل السوق"""
    try:
        symbols = ["BTC-USDT", "ETH-USDT", "BNB-USDT"]
        for symbol in symbols:
            trader.analyze_symbol(symbol)

        return jsonify({"success": True, "signals": trader.signals})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/quick_trade', methods=['POST'])
def api_quick_trade():
    """تداول سريع"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'BTC-USDT')
        amount = data.get('amount', 20)

        traded = trader.quick_trade(symbol, amount)
        return jsonify({
            "success": True,
            "traded": traded,
            "trades": trader.trades,
            "signals": trader.signals
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

if __name__ == "__main__":
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        ⚡ متداول KuCoin السريع والفعال                      ║
    ║           Fast & Efficient KuCoin Trader                    ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝

    🌐 الرابط: http://127.0.0.1:5002
    ⚡ منصة تداول سريعة وبسيطة

    ✅ الميزات:
    🚀 سرعة فائقة في التنفيذ
    💰 تداول حقيقي مع KuCoin
    📊 تحليل سريع وفعال
    🎯 واجهة بسيطة وسهلة
    🛡️ إدارة مخاطر ذكية
    """)

    app.run(host='0.0.0.0', port=5002, debug=False)
