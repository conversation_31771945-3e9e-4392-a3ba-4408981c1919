#!/usr/bin/env python3
"""
Quick test script to verify Binance API connection
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.binance_client import BinanceClient, BinanceAPIError
from src.data.data_collector import DataCollector
from src.core.config import settings
from src.core.logger import logger


async def test_binance_connection():
    """Test basic Binance API connection"""
    print("🔍 Testing Binance API Connection...")
    print("=" * 50)
    
    # Test with public API (no credentials required)
    client = BinanceClient(testnet=True)
    
    try:
        async with client:
            print("✅ Connection established successfully")
            
            # Test server time
            server_time = await client.get_server_time()
            print(f"📅 Server time: {server_time['serverTime']}")
            
            # Test exchange info
            exchange_info = await client.get_exchange_info()
            symbols_count = len(exchange_info.get('symbols', []))
            print(f"📊 Available symbols: {symbols_count}")
            
            # Test ticker for BTCUSDT
            ticker = await client.get_symbol_ticker("BTCUSDT")
            print(f"💰 BTCUSDT price: ${float(ticker['lastPrice']):,.2f}")
            print(f"📈 24h change: {float(ticker['priceChangePercent']):.2f}%")
            
            # Test klines data
            klines = await client.get_klines("BTCUSDT", "1h", limit=5)
            print(f"📊 Retrieved {len(klines)} klines for BTCUSDT")
            
            if klines:
                latest_kline = klines[-1]
                print(f"   Latest candle: O:{float(latest_kline[1]):.2f} "
                      f"H:{float(latest_kline[2]):.2f} "
                      f"L:{float(latest_kline[3]):.2f} "
                      f"C:{float(latest_kline[4]):.2f}")
            
            print("\n✅ All public API tests passed!")
            
    except BinanceAPIError as e:
        print(f"❌ Binance API Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True


async def test_data_collector():
    """Test data collector functionality"""
    print("\n🔍 Testing Data Collector...")
    print("=" * 50)
    
    try:
        collector = DataCollector()
        
        # Test ticker collection
        tickers = await collector.collect_all_tickers()
        print(f"📊 Collected ticker data for {len(tickers)} symbols")
        
        if 'BTCUSDT' in tickers:
            btc_ticker = tickers['BTCUSDT']
            print(f"💰 BTC/USDT: ${btc_ticker.last_price:.2f} ({btc_ticker.price_change_percent:+.2f}%)")
        
        # Test historical data collection (small sample)
        print("\n📈 Testing historical data collection...")
        df = await collector.collect_historical_data("BTCUSDT", "1h", days=1)
        print(f"📊 Collected {len(df)} historical records")
        
        if not df.empty:
            print(f"   Date range: {df.index.min()} to {df.index.max()}")
            print(f"   Latest close: ${df['close'].iloc[-1]:.2f}")
        
        print("\n✅ Data collector tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Data collector error: {e}")
        return False


async def test_with_credentials():
    """Test authenticated API calls if credentials are available"""
    print("\n🔍 Testing Authenticated API...")
    print("=" * 50)
    
    if not settings.binance_api_key or not settings.binance_secret_key:
        print("⚠️  No API credentials found in .env file")
        print("   Skipping authenticated API tests")
        return True
    
    client = BinanceClient(
        api_key=settings.binance_api_key,
        secret_key=settings.binance_secret_key,
        testnet=settings.binance_testnet
    )
    
    try:
        async with client:
            # Test account info
            account_info = await client.get_account_info()
            print(f"✅ Account type: {account_info.get('accountType', 'Unknown')}")
            print(f"📊 Can trade: {account_info.get('canTrade', False)}")
            
            # Test balance
            balances = await client.get_balance()
            non_zero_balances = [b for b in balances if float(b['free']) > 0 or float(b['locked']) > 0]
            print(f"💰 Non-zero balances: {len(non_zero_balances)}")
            
            for balance in non_zero_balances[:5]:  # Show first 5
                total = float(balance['free']) + float(balance['locked'])
                if total > 0:
                    print(f"   {balance['asset']}: {total:.8f}")
            
            print("\n✅ Authenticated API tests passed!")
            return True
            
    except BinanceAPIError as e:
        print(f"❌ Authentication failed: {e}")
        print("   Please check your API keys in .env file")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


async def main():
    """Main test function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🤖 AI Crypto Trading Bot - Connection Test           ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    print(f"🔧 Configuration:")
    print(f"   Testnet mode: {settings.binance_testnet}")
    print(f"   Trading pairs: {', '.join(settings.trading_pairs_list[:3])}...")
    print(f"   API keys configured: {bool(settings.binance_api_key and settings.binance_secret_key)}")
    
    # Run tests
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Basic connection
    if await test_binance_connection():
        tests_passed += 1
    
    # Test 2: Data collector
    if await test_data_collector():
        tests_passed += 1
    
    # Test 3: Authenticated API (if credentials available)
    if await test_with_credentials():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Your setup is working correctly.")
        print("\n💡 Next steps:")
        print("   1. Add your Binance API keys to .env file for full functionality")
        print("   2. Configure Telegram bot for notifications")
        print("   3. Run the main trading bot: python main.py")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("\n🔧 Troubleshooting:")
        print("   1. Check your internet connection")
        print("   2. Verify .env file configuration")
        print("   3. Ensure all dependencies are installed: pip install -r requirements.txt")
    
    return tests_passed == total_tests


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
