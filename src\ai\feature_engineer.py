"""
Advanced Feature Engineering for AI Price Prediction
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
# Technical Analysis - using manual implementations
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False

try:
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.decomposition import PCA
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

from ..core.logger import logger


class FeatureEngineer:
    """
    Advanced feature engineering for cryptocurrency price prediction
    """
    
    def __init__(self):
        self.scalers = {}
        self.pca_models = {}
        self.feature_importance = {}
        
        logger.info("Feature Engineer initialized")
    
    def create_technical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create comprehensive technical analysis features"""
        try:
            features_df = df.copy()
            
            # Price-based features
            features_df['price_change'] = features_df['close'].pct_change()
            features_df['price_change_abs'] = features_df['price_change'].abs()
            features_df['high_low_ratio'] = features_df['high'] / features_df['low']
            features_df['open_close_ratio'] = features_df['open'] / features_df['close']
            
            # Volume features
            features_df['volume_change'] = features_df['volume'].pct_change()
            features_df['volume_price_trend'] = features_df['volume'] * features_df['price_change']
            features_df['volume_sma_ratio'] = features_df['volume'] / features_df['volume'].rolling(20).mean()
            
            # Moving averages
            for period in [5, 10, 20, 50, 100, 200]:
                features_df[f'sma_{period}'] = features_df['close'].rolling(period).mean()
                features_df[f'ema_{period}'] = features_df['close'].ewm(span=period).mean()
                features_df[f'price_sma_{period}_ratio'] = features_df['close'] / features_df[f'sma_{period}']
                features_df[f'price_ema_{period}_ratio'] = features_df['close'] / features_df[f'ema_{period}']
            
            # Volatility features
            features_df['volatility_5'] = features_df['close'].rolling(5).std()
            features_df['volatility_20'] = features_df['close'].rolling(20).std()
            features_df['volatility_ratio'] = features_df['volatility_5'] / features_df['volatility_20']
            
            # Momentum indicators
            if TALIB_AVAILABLE:
                features_df['rsi_14'] = talib.RSI(features_df['close'].values, timeperiod=14)
                features_df['rsi_7'] = talib.RSI(features_df['close'].values, timeperiod=7)
                features_df['rsi_21'] = talib.RSI(features_df['close'].values, timeperiod=21)
            else:
                # Manual RSI calculation
                features_df['rsi_14'] = self._calculate_rsi(features_df['close'], 14)
                features_df['rsi_7'] = self._calculate_rsi(features_df['close'], 7)
                features_df['rsi_21'] = self._calculate_rsi(features_df['close'], 21)

            # MACD
            if TALIB_AVAILABLE:
                macd, macd_signal, macd_hist = talib.MACD(features_df['close'].values)
                features_df['macd'] = macd
                features_df['macd_signal'] = macd_signal
                features_df['macd_histogram'] = macd_hist
            else:
                # Manual MACD calculation
                ema12 = features_df['close'].ewm(span=12).mean()
                ema26 = features_df['close'].ewm(span=26).mean()
                features_df['macd'] = ema12 - ema26
                features_df['macd_signal'] = features_df['macd'].ewm(span=9).mean()
                features_df['macd_histogram'] = features_df['macd'] - features_df['macd_signal']

            # Bollinger Bands
            if TALIB_AVAILABLE:
                bb_upper, bb_middle, bb_lower = talib.BBANDS(features_df['close'].values)
                features_df['bb_upper'] = bb_upper
                features_df['bb_middle'] = bb_middle
                features_df['bb_lower'] = bb_lower
            else:
                # Manual Bollinger Bands
                sma20 = features_df['close'].rolling(20).mean()
                std20 = features_df['close'].rolling(20).std()
                features_df['bb_middle'] = sma20
                features_df['bb_upper'] = sma20 + (std20 * 2)
                features_df['bb_lower'] = sma20 - (std20 * 2)

            features_df['bb_width'] = (features_df['bb_upper'] - features_df['bb_lower']) / features_df['bb_middle']
            features_df['bb_position'] = (features_df['close'] - features_df['bb_lower']) / (features_df['bb_upper'] - features_df['bb_lower'])

            # Stochastic
            if TALIB_AVAILABLE:
                stoch_k, stoch_d = talib.STOCH(features_df['high'].values,
                                              features_df['low'].values,
                                              features_df['close'].values)
                features_df['stoch_k'] = stoch_k
                features_df['stoch_d'] = stoch_d
            else:
                # Manual Stochastic
                features_df['stoch_k'] = self._calculate_stochastic(features_df, 14)
                features_df['stoch_d'] = features_df['stoch_k'].rolling(3).mean()

            # Williams %R
            if TALIB_AVAILABLE:
                features_df['williams_r'] = talib.WILLR(features_df['high'].values,
                                                       features_df['low'].values,
                                                       features_df['close'].values)
            else:
                # Manual Williams %R
                features_df['williams_r'] = self._calculate_williams_r(features_df, 14)

            # Average True Range
            if TALIB_AVAILABLE:
                features_df['atr'] = talib.ATR(features_df['high'].values,
                                              features_df['low'].values,
                                              features_df['close'].values)
            else:
                # Manual ATR
                features_df['atr'] = self._calculate_atr(features_df, 14)

            # On Balance Volume
            if TALIB_AVAILABLE:
                features_df['obv'] = talib.OBV(features_df['close'].values, features_df['volume'].values)
            else:
                # Manual OBV
                features_df['obv'] = self._calculate_obv(features_df)

            features_df['obv_sma'] = features_df['obv'].rolling(20).mean()
            
            logger.info(f"Created {len(features_df.columns) - len(df.columns)} technical features")
            return features_df
            
        except Exception as e:
            logger.error(f"Error creating technical features: {e}")
            return df
    
    def create_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create time-based features"""
        try:
            features_df = df.copy()
            
            # Ensure index is datetime
            if not isinstance(features_df.index, pd.DatetimeIndex):
                features_df.index = pd.to_datetime(features_df.index)
            
            # Time components
            features_df['hour'] = features_df.index.hour
            features_df['day_of_week'] = features_df.index.dayofweek
            features_df['day_of_month'] = features_df.index.day
            features_df['month'] = features_df.index.month
            features_df['quarter'] = features_df.index.quarter
            
            # Cyclical encoding
            features_df['hour_sin'] = np.sin(2 * np.pi * features_df['hour'] / 24)
            features_df['hour_cos'] = np.cos(2 * np.pi * features_df['hour'] / 24)
            features_df['day_sin'] = np.sin(2 * np.pi * features_df['day_of_week'] / 7)
            features_df['day_cos'] = np.cos(2 * np.pi * features_df['day_of_week'] / 7)
            features_df['month_sin'] = np.sin(2 * np.pi * features_df['month'] / 12)
            features_df['month_cos'] = np.cos(2 * np.pi * features_df['month'] / 12)
            
            # Market session indicators
            features_df['is_weekend'] = (features_df['day_of_week'] >= 5).astype(int)
            features_df['is_asian_session'] = ((features_df['hour'] >= 0) & (features_df['hour'] < 8)).astype(int)
            features_df['is_european_session'] = ((features_df['hour'] >= 8) & (features_df['hour'] < 16)).astype(int)
            features_df['is_american_session'] = ((features_df['hour'] >= 16) & (features_df['hour'] < 24)).astype(int)
            
            logger.info(f"Created {len(features_df.columns) - len(df.columns)} time features")
            return features_df
            
        except Exception as e:
            logger.error(f"Error creating time features: {e}")
            return df
    
    def create_lag_features(self, df: pd.DataFrame, target_col: str = 'close', lags: List[int] = None) -> pd.DataFrame:
        """Create lagged features for time series prediction"""
        try:
            if lags is None:
                lags = [1, 2, 3, 5, 10, 20, 30]
            
            features_df = df.copy()
            
            for lag in lags:
                features_df[f'{target_col}_lag_{lag}'] = features_df[target_col].shift(lag)
                features_df[f'{target_col}_change_lag_{lag}'] = features_df[target_col].pct_change(lag)
                
                # Rolling statistics
                features_df[f'{target_col}_mean_lag_{lag}'] = features_df[target_col].shift(1).rolling(lag).mean()
                features_df[f'{target_col}_std_lag_{lag}'] = features_df[target_col].shift(1).rolling(lag).std()
                features_df[f'{target_col}_min_lag_{lag}'] = features_df[target_col].shift(1).rolling(lag).min()
                features_df[f'{target_col}_max_lag_{lag}'] = features_df[target_col].shift(1).rolling(lag).max()
            
            logger.info(f"Created lag features for {len(lags)} periods")
            return features_df
            
        except Exception as e:
            logger.error(f"Error creating lag features: {e}")
            return df
    
    def create_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create statistical features"""
        try:
            features_df = df.copy()
            
            # Rolling statistics for different windows
            windows = [5, 10, 20, 50]
            
            for window in windows:
                # Price statistics
                features_df[f'close_mean_{window}'] = features_df['close'].rolling(window).mean()
                features_df[f'close_std_{window}'] = features_df['close'].rolling(window).std()
                features_df[f'close_skew_{window}'] = features_df['close'].rolling(window).skew()
                features_df[f'close_kurt_{window}'] = features_df['close'].rolling(window).kurt()
                
                # Volume statistics
                features_df[f'volume_mean_{window}'] = features_df['volume'].rolling(window).mean()
                features_df[f'volume_std_{window}'] = features_df['volume'].rolling(window).std()
                
                # High-Low statistics
                features_df[f'hl_mean_{window}'] = (features_df['high'] - features_df['low']).rolling(window).mean()
                features_df[f'hl_std_{window}'] = (features_df['high'] - features_df['low']).rolling(window).std()
            
            # Percentile features
            for window in [20, 50]:
                features_df[f'close_percentile_25_{window}'] = features_df['close'].rolling(window).quantile(0.25)
                features_df[f'close_percentile_75_{window}'] = features_df['close'].rolling(window).quantile(0.75)
                features_df[f'close_position_{window}'] = (features_df['close'] - features_df[f'close_percentile_25_{window}']) / \
                                                         (features_df[f'close_percentile_75_{window}'] - features_df[f'close_percentile_25_{window}'])
            
            logger.info(f"Created statistical features")
            return features_df
            
        except Exception as e:
            logger.error(f"Error creating statistical features: {e}")
            return df
    
    def create_market_microstructure_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create market microstructure features"""
        try:
            features_df = df.copy()
            
            # Price impact features
            features_df['price_impact'] = (features_df['close'] - features_df['open']) / features_df['volume']
            features_df['volume_weighted_price'] = (features_df['high'] + features_df['low'] + features_df['close']) / 3
            
            # Spread approximation
            features_df['hl_spread'] = (features_df['high'] - features_df['low']) / features_df['close']
            features_df['oc_spread'] = abs(features_df['open'] - features_df['close']) / features_df['close']
            
            # Tick direction (simplified)
            features_df['tick_direction'] = np.sign(features_df['close'].diff())
            features_df['tick_direction_sum_5'] = features_df['tick_direction'].rolling(5).sum()
            features_df['tick_direction_sum_20'] = features_df['tick_direction'].rolling(20).sum()
            
            # Volume profile features
            features_df['volume_at_price'] = features_df['volume'] / (features_df['high'] - features_df['low'] + 1e-8)
            features_df['volume_imbalance'] = features_df['volume'] - features_df['volume'].rolling(20).mean()
            
            logger.info(f"Created market microstructure features")
            return features_df
            
        except Exception as e:
            logger.error(f"Error creating market microstructure features: {e}")
            return df
    
    def engineer_all_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create all features in one go"""
        try:
            logger.info("Starting comprehensive feature engineering...")
            
            # Start with original data
            features_df = df.copy()
            
            # Create all feature types
            features_df = self.create_technical_features(features_df)
            features_df = self.create_time_features(features_df)
            features_df = self.create_lag_features(features_df)
            features_df = self.create_statistical_features(features_df)
            features_df = self.create_market_microstructure_features(features_df)
            
            # Remove infinite and NaN values
            features_df = features_df.replace([np.inf, -np.inf], np.nan)
            
            # Forward fill and backward fill
            features_df = features_df.fillna(method='ffill').fillna(method='bfill')
            
            # Drop remaining NaN rows
            initial_rows = len(features_df)
            features_df = features_df.dropna()
            final_rows = len(features_df)
            
            logger.info(f"Feature engineering complete: {len(features_df.columns)} features created")
            logger.info(f"Rows: {initial_rows} -> {final_rows} (dropped {initial_rows - final_rows} NaN rows)")
            
            return features_df
            
        except Exception as e:
            logger.error(f"Error in comprehensive feature engineering: {e}")
            return df
    
    def scale_features(self, df: pd.DataFrame, feature_cols: List[str], scaler_type: str = 'standard') -> Tuple[pd.DataFrame, object]:
        """Scale features for ML models"""
        try:
            scaled_df = df.copy()

            if SKLEARN_AVAILABLE:
                if scaler_type == 'standard':
                    scaler = StandardScaler()
                elif scaler_type == 'minmax':
                    scaler = MinMaxScaler()
                else:
                    raise ValueError(f"Unknown scaler type: {scaler_type}")

                # Fit and transform
                scaled_values = scaler.fit_transform(scaled_df[feature_cols])
                scaled_df[feature_cols] = scaled_values
            else:
                # Manual scaling
                scaler = {}
                for col in feature_cols:
                    if scaler_type == 'standard':
                        mean_val = scaled_df[col].mean()
                        std_val = scaled_df[col].std()
                        scaled_df[col] = (scaled_df[col] - mean_val) / (std_val + 1e-8)
                        scaler[col] = {'mean': mean_val, 'std': std_val, 'type': 'standard'}
                    elif scaler_type == 'minmax':
                        min_val = scaled_df[col].min()
                        max_val = scaled_df[col].max()
                        scaled_df[col] = (scaled_df[col] - min_val) / (max_val - min_val + 1e-8)
                        scaler[col] = {'min': min_val, 'max': max_val, 'type': 'minmax'}

            logger.info(f"Scaled {len(feature_cols)} features using {scaler_type} scaler")
            return scaled_df, scaler

        except Exception as e:
            logger.error(f"Error scaling features: {e}")
            return df, None
    
    def select_features(self, df: pd.DataFrame, target_col: str, method: str = 'correlation', top_k: int = 50) -> List[str]:
        """Select most important features"""
        try:
            feature_cols = [col for col in df.columns if col != target_col and not df[col].isna().all()]
            
            if method == 'correlation':
                # Correlation-based selection
                correlations = df[feature_cols].corrwith(df[target_col]).abs().sort_values(ascending=False)
                selected_features = correlations.head(top_k).index.tolist()
                
            elif method == 'variance':
                # Variance-based selection
                variances = df[feature_cols].var().sort_values(ascending=False)
                selected_features = variances.head(top_k).index.tolist()
                
            else:
                # Default: take first top_k features
                selected_features = feature_cols[:top_k]
            
            logger.info(f"Selected {len(selected_features)} features using {method} method")
            return selected_features
            
        except Exception as e:
            logger.error(f"Error selecting features: {e}")
            return feature_cols[:top_k] if len(feature_cols) > top_k else feature_cols
    
    def create_sequences(self, df: pd.DataFrame, feature_cols: List[str], target_col: str, 
                        sequence_length: int = 60, prediction_horizon: int = 1) -> Tuple[np.ndarray, np.ndarray]:
        """Create sequences for LSTM/GRU models"""
        try:
            # Prepare data
            feature_data = df[feature_cols].values
            target_data = df[target_col].values
            
            X, y = [], []
            
            for i in range(sequence_length, len(feature_data) - prediction_horizon + 1):
                # Features: sequence of past observations
                X.append(feature_data[i-sequence_length:i])
                
                # Target: future value(s)
                if prediction_horizon == 1:
                    y.append(target_data[i + prediction_horizon - 1])
                else:
                    y.append(target_data[i:i + prediction_horizon])
            
            X = np.array(X)
            y = np.array(y)
            
            logger.info(f"Created sequences: X shape {X.shape}, y shape {y.shape}")
            return X, y
            
        except Exception as e:
            logger.error(f"Error creating sequences: {e}")
            return np.array([]), np.array([])
    
    def get_feature_importance(self, model, feature_names: List[str]) -> Dict[str, float]:
        """Extract feature importance from trained model"""
        try:
            if hasattr(model, 'feature_importances_'):
                # Tree-based models
                importance = model.feature_importances_
            elif hasattr(model, 'coef_'):
                # Linear models
                importance = np.abs(model.coef_).flatten()
            else:
                logger.warning("Model doesn't support feature importance extraction")
                return {}
            
            # Create importance dictionary
            feature_importance = dict(zip(feature_names, importance))
            
            # Sort by importance
            sorted_importance = dict(sorted(feature_importance.items(), key=lambda x: x[1], reverse=True))
            
            logger.info(f"Extracted feature importance for {len(feature_names)} features")
            return sorted_importance

        except Exception as e:
            logger.error(f"Error extracting feature importance: {e}")
            return {}

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI manually"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except Exception:
            return pd.Series(index=prices.index, dtype=float)

    def _calculate_stochastic(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Stochastic %K manually"""
        try:
            low_min = df['low'].rolling(window=period).min()
            high_max = df['high'].rolling(window=period).max()
            stoch_k = 100 * (df['close'] - low_min) / (high_max - low_min)
            return stoch_k
        except Exception:
            return pd.Series(index=df.index, dtype=float)

    def _calculate_williams_r(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Williams %R manually"""
        try:
            high_max = df['high'].rolling(window=period).max()
            low_min = df['low'].rolling(window=period).min()
            williams_r = -100 * (high_max - df['close']) / (high_max - low_min)
            return williams_r
        except Exception:
            return pd.Series(index=df.index, dtype=float)

    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Calculate Average True Range manually"""
        try:
            high_low = df['high'] - df['low']
            high_close_prev = np.abs(df['high'] - df['close'].shift())
            low_close_prev = np.abs(df['low'] - df['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close_prev, low_close_prev))
            atr = true_range.rolling(window=period).mean()
            return atr
        except Exception:
            return pd.Series(index=df.index, dtype=float)

    def _calculate_obv(self, df: pd.DataFrame) -> pd.Series:
        """Calculate On Balance Volume manually"""
        try:
            obv = pd.Series(index=df.index, dtype=float)
            obv.iloc[0] = df['volume'].iloc[0]

            for i in range(1, len(df)):
                if df['close'].iloc[i] > df['close'].iloc[i-1]:
                    obv.iloc[i] = obv.iloc[i-1] + df['volume'].iloc[i]
                elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                    obv.iloc[i] = obv.iloc[i-1] - df['volume'].iloc[i]
                else:
                    obv.iloc[i] = obv.iloc[i-1]

            return obv
        except Exception:
            return pd.Series(index=df.index, dtype=float)
