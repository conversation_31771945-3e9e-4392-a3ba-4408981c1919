# =============================================================================
# AI Crypto Trading Bot Configuration
# =============================================================================

# Application Settings
APP_NAME=AI Crypto Trading Bot
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO

# =============================================================================
# Trading Configuration
# =============================================================================

# Binance API Configuration
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_TESTNET=true  # Set to false for live trading

# Trading Parameters
DEFAULT_TRADING_PAIRS=BTCUSDT,ETHUSDT,ADAUSDT,DOTUSDT,LINKUSDT
MAX_RISK_PER_TRADE=0.02  # 2% of portfolio per trade
STOP_LOSS_PERCENTAGE=0.05  # 5% stop loss
TAKE_PROFIT_PERCENTAGE=0.10  # 10% take profit
MIN_TRADE_AMOUNT=10  # Minimum trade amount in USDT

# =============================================================================
# Database Configuration
# =============================================================================

# PostgreSQL Database
DATABASE_URL=postgresql://username:password@localhost:5432/crypto_trading_bot
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# AI & Machine Learning Configuration
# =============================================================================

# Model Settings
AI_MODEL_UPDATE_INTERVAL=3600  # Update models every hour
PREDICTION_CONFIDENCE_THRESHOLD=0.7
SENTIMENT_ANALYSIS_ENABLED=true
TECHNICAL_INDICATORS_ENABLED=true

# Data Collection
HISTORICAL_DATA_DAYS=365  # Days of historical data to collect
REAL_TIME_DATA_INTERVAL=60  # Seconds between real-time data updates

# =============================================================================
# Notification Configuration
# =============================================================================

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here
TELEGRAM_NOTIFICATIONS_ENABLED=true

# Email Notifications
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here
EMAIL_NOTIFICATIONS_ENABLED=false
NOTIFICATION_EMAIL=<EMAIL>

# =============================================================================
# Security Configuration
# =============================================================================

# Encryption
SECRET_KEY=your_secret_key_for_encryption_here_make_it_very_long_and_random
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Security
API_KEY_ENCRYPTION_ENABLED=true
RATE_LIMITING_ENABLED=true
MAX_REQUESTS_PER_MINUTE=60

# =============================================================================
# Web Interface Configuration
# =============================================================================

# FastAPI Settings
HOST=0.0.0.0
PORT=8000
RELOAD=true

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000
ALLOWED_METHODS=GET,POST,PUT,DELETE
ALLOWED_HEADERS=*

# =============================================================================
# Monitoring & Logging
# =============================================================================

# Logging
LOG_FILE_PATH=logs/trading_bot.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days
LOG_FORMAT=json

# Monitoring
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
HEALTH_CHECK_INTERVAL=300  # 5 minutes

# =============================================================================
# Advanced Trading Settings
# =============================================================================

# Risk Management
MAX_OPEN_POSITIONS=5
MAX_DAILY_LOSS=0.10  # 10% of portfolio
PORTFOLIO_REBALANCE_INTERVAL=86400  # 24 hours

# Market Analysis
MARKET_VOLATILITY_THRESHOLD=0.05
TREND_CONFIRMATION_PERIODS=3
VOLUME_ANALYSIS_ENABLED=true

# Backtesting
BACKTEST_START_DATE=2023-01-01
BACKTEST_END_DATE=2024-01-01
BACKTEST_INITIAL_BALANCE=10000

# =============================================================================
# External APIs (Optional)
# =============================================================================

# News & Sentiment APIs
NEWS_API_KEY=your_news_api_key_here
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here

# Additional Data Sources
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
COINMARKETCAP_API_KEY=your_coinmarketcap_key_here
