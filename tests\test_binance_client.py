"""
Tests for Binance API Client
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.data.binance_client import BinanceClient, BinanceAPIError


class TestBinanceClient:
    """Test cases for BinanceClient"""
    
    @pytest.fixture
    def client(self):
        """Create a test client"""
        return BinanceClient(
            api_key="test_api_key",
            secret_key="test_secret_key",
            testnet=True
        )
    
    @pytest.mark.unit
    def test_client_initialization(self, client):
        """Test client initialization"""
        assert client.api_key == "test_api_key"
        assert client.secret_key == "test_secret_key"
        assert client.testnet is True
        assert client.base_url == "https://testnet.binance.vision"
        assert not client.connected
    
    @pytest.mark.unit
    def test_signature_generation(self, client):
        """Test HMAC signature generation"""
        query_string = "symbol=BTCUSDT&side=BUY&type=LIMIT&timeInForce=GTC&quantity=1&price=50000&timestamp=1234567890"
        signature = client._generate_signature(query_string)
        
        assert isinstance(signature, str)
        assert len(signature) == 64  # SHA256 hex digest length
    
    @pytest.mark.unit
    def test_timestamp_generation(self, client):
        """Test timestamp generation"""
        timestamp = client._get_timestamp()
        assert isinstance(timestamp, int)
        assert timestamp > 0
    
    @pytest.mark.unit
    async def test_rate_limiting(self, client):
        """Test rate limiting functionality"""
        # This should not raise an exception
        await client._rate_limit_check()
        
        # Simulate hitting rate limit
        client.request_count = 100
        client.last_request_time = asyncio.get_event_loop().time()
        
        # This should work (we're not actually enforcing the limit in tests)
        await client._rate_limit_check()
    
    @pytest.mark.integration
    async def test_server_time_sync(self, client):
        """Test server time synchronization"""
        with patch.object(client, '_make_request') as mock_request:
            mock_request.return_value = {"serverTime": 1234567890000}
            
            await client._sync_server_time()
            
            assert client.server_time_offset != 0
            mock_request.assert_called_once_with("GET", "/api/v3/time")
    
    @pytest.mark.integration
    async def test_get_server_time(self, client):
        """Test getting server time"""
        with patch.object(client, '_make_request') as mock_request:
            expected_response = {"serverTime": 1234567890000}
            mock_request.return_value = expected_response
            
            result = await client.get_server_time()
            
            assert result == expected_response
            mock_request.assert_called_once_with("GET", "/api/v3/time")
    
    @pytest.mark.integration
    async def test_get_symbol_ticker(self, client):
        """Test getting symbol ticker"""
        with patch.object(client, '_make_request') as mock_request:
            expected_response = {
                "symbol": "BTCUSDT",
                "priceChange": "1000.00",
                "priceChangePercent": "2.00",
                "lastPrice": "51000.00"
            }
            mock_request.return_value = expected_response
            
            result = await client.get_symbol_ticker("BTCUSDT")
            
            assert result == expected_response
            mock_request.assert_called_once_with(
                "GET", "/api/v3/ticker/24hr", {"symbol": "BTCUSDT"}
            )
    
    @pytest.mark.integration
    async def test_get_klines(self, client):
        """Test getting klines data"""
        with patch.object(client, '_make_request') as mock_request:
            expected_response = [
                [1234567890000, "50000.00", "51000.00", "49000.00", "50500.00", "100.00", 1234567950000, "5050000.00", 1000, "50.00", "2525000.00", "0"]
            ]
            mock_request.return_value = expected_response
            
            result = await client.get_klines("BTCUSDT", "1h", limit=1)
            
            assert result == expected_response
            mock_request.assert_called_once_with(
                "GET", "/api/v3/klines", 
                {"symbol": "BTCUSDT", "interval": "1h", "limit": 1}
            )
    
    @pytest.mark.integration
    async def test_api_error_handling(self, client):
        """Test API error handling"""
        with patch.object(client, '_make_request') as mock_request:
            mock_request.side_effect = BinanceAPIError("Test error", 400)
            
            with pytest.raises(BinanceAPIError):
                await client.get_server_time()
    
    @pytest.mark.integration
    async def test_connection_context_manager(self, client):
        """Test using client as async context manager"""
        with patch.object(client, 'connect') as mock_connect:
            with patch.object(client, 'disconnect') as mock_disconnect:
                
                async with client:
                    pass
                
                mock_connect.assert_called_once()
                mock_disconnect.assert_called_once()
    
    @pytest.mark.unit
    def test_symbol_info_methods(self, client):
        """Test symbol information utility methods"""
        # Test price formatting
        formatted_price = client.format_price(50000.123456, "BTCUSDT", 2)
        assert formatted_price == "50000.12"
        
        # Test quantity formatting
        formatted_qty = client.format_quantity(1.123456789, "BTCUSDT", 6)
        assert formatted_qty == "1.123457"


@pytest.mark.integration
class TestBinanceClientIntegration:
    """Integration tests that require actual API connection"""
    
    @pytest.fixture
    def live_client(self):
        """Create a client for live testing (testnet)"""
        return BinanceClient(testnet=True)
    
    @pytest.mark.api
    async def test_public_api_calls(self, live_client):
        """Test public API calls that don't require authentication"""
        async with live_client:
            # Test server time
            server_time = await live_client.get_server_time()
            assert "serverTime" in server_time
            
            # Test exchange info
            exchange_info = await live_client.get_exchange_info()
            assert "symbols" in exchange_info
            
            # Test ticker for BTCUSDT
            ticker = await live_client.get_symbol_ticker("BTCUSDT")
            assert ticker["symbol"] == "BTCUSDT"
            assert "lastPrice" in ticker
    
    @pytest.mark.api
    @pytest.mark.slow
    async def test_klines_data_collection(self, live_client):
        """Test collecting klines data"""
        async with live_client:
            klines = await live_client.get_klines("BTCUSDT", "1h", limit=10)
            
            assert len(klines) <= 10
            assert len(klines[0]) == 12  # Standard kline format
            
            # Verify data types
            for kline in klines:
                assert isinstance(kline[0], int)  # Open time
                assert isinstance(float(kline[1]), float)  # Open price
                assert isinstance(float(kline[4]), float)  # Close price
                assert isinstance(float(kline[5]), float)  # Volume


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
