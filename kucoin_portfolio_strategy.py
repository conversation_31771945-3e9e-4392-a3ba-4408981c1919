#!/usr/bin/env python3
"""
KuCoin Portfolio Strategy - Custom Trading Strategy for Your Portfolio
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime
import pandas as pd

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.kucoin_client import KuCoinClient, KuCoinAPIError
from src.trading.order_manager import OrderManager, OrderRequest, OrderType
from src.analysis import MarketAnalyzer
from src.core.config import settings
from src.core.logger import logger


async def analyze_portfolio_holdings():
    """Analyze current portfolio holdings with technical analysis"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🎯 Custom KuCoin Portfolio Strategy Analysis         ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    client = KuCoinClient()
    analyzer = MarketAnalyzer()
    
    # Your current holdings
    holdings = {
        'HIGH': 26.77,
        'STG': 87.49,
        'CELR': 1828.28,
        'VOXEL': 219.24,
        'PIXEL': 274.25,
        'PORTAL': 133.63,
        'LTO': 287.27,
        'KCS': 0.002
    }
    
    try:
        async with client:
            print("💼 Analyzing Your Current Portfolio Holdings...")
            print("=" * 70)
            
            portfolio_analysis = {}
            total_portfolio_value = 0
            
            for currency, quantity in holdings.items():
                if quantity < 0.001:  # Skip very small amounts
                    continue
                
                symbol = f"{currency}-USDT"
                
                try:
                    print(f"\n📊 Analyzing {symbol}...")
                    
                    # Get current price
                    ticker = await client.get_ticker(symbol)
                    current_price = float(ticker.get('last', 0))
                    change_24h = float(ticker.get('changeRate', 0)) * 100
                    volume_24h = float(ticker.get('vol', 0))
                    
                    if current_price <= 0:
                        print(f"   ⚠️  No price data available for {symbol}")
                        continue
                    
                    # Calculate position value
                    position_value = quantity * current_price
                    total_portfolio_value += position_value
                    
                    print(f"   💰 Holdings: {quantity:,.2f} {currency}")
                    print(f"   💵 Current Price: ${current_price:,.6f}")
                    print(f"   📈 24h Change: {change_24h:+.2f}%")
                    print(f"   💎 Position Value: ${position_value:,.2f}")
                    print(f"   📊 24h Volume: {volume_24h:,.0f}")
                    
                    # Get historical data for technical analysis
                    try:
                        klines = await client.get_klines(symbol, '1hour')
                        
                        if len(klines) >= 50:
                            # Convert to DataFrame
                            data = []
                            for kline in reversed(klines[:100]):
                                data.append({
                                    'open': float(kline[1]),
                                    'high': float(kline[3]),
                                    'low': float(kline[4]),
                                    'close': float(kline[2]),
                                    'volume': float(kline[5]),
                                    'quote_volume': float(kline[6]),
                                    'trades_count': 100
                                })
                            
                            df = pd.DataFrame(data)
                            df.index = pd.date_range(end=datetime.now(), periods=len(df), freq='h')
                            
                            # Perform technical analysis
                            signal = await analyzer.analyze_symbol(currency + 'USDT', df)
                            
                            print(f"   🎯 Technical Analysis:")
                            print(f"      Signal: {signal.signal_type}")
                            print(f"      Confidence: {signal.confidence:.1%}")
                            print(f"      Risk Level: {signal.risk_level}")
                            
                            if signal.target_price:
                                potential_return = ((signal.target_price - current_price) / current_price) * 100
                                print(f"      Target: ${signal.target_price:,.6f} ({potential_return:+.2f}%)")
                            
                            if signal.stop_loss:
                                risk_percent = ((signal.stop_loss - current_price) / current_price) * 100
                                print(f"      Stop Loss: ${signal.stop_loss:,.6f} ({risk_percent:+.2f}%)")
                            
                            # Store analysis
                            portfolio_analysis[currency] = {
                                'symbol': symbol,
                                'quantity': quantity,
                                'current_price': current_price,
                                'position_value': position_value,
                                'change_24h': change_24h,
                                'volume_24h': volume_24h,
                                'signal': signal.signal_type,
                                'confidence': signal.confidence,
                                'risk_level': signal.risk_level,
                                'target_price': signal.target_price,
                                'stop_loss': signal.stop_loss,
                                'reasoning': signal.reasoning
                            }
                        else:
                            print(f"   ⚠️  Insufficient historical data for technical analysis")
                            
                    except Exception as e:
                        print(f"   ❌ Technical analysis failed: {e}")
                
                except Exception as e:
                    print(f"   ❌ Error analyzing {symbol}: {e}")
            
            # Portfolio Summary
            print(f"\n💼 PORTFOLIO SUMMARY")
            print("=" * 70)
            print(f"📊 Total Portfolio Value: ${total_portfolio_value:,.2f} USDT")
            print(f"📈 Number of Holdings: {len(portfolio_analysis)}")
            
            # Categorize holdings by signals
            buy_signals = []
            sell_signals = []
            hold_signals = []
            
            for currency, analysis in portfolio_analysis.items():
                if analysis['signal'] == 'BUY':
                    buy_signals.append((currency, analysis))
                elif analysis['signal'] == 'SELL':
                    sell_signals.append((currency, analysis))
                else:
                    hold_signals.append((currency, analysis))
            
            # Trading Recommendations
            print(f"\n🎯 TRADING RECOMMENDATIONS")
            print("=" * 70)
            
            if sell_signals:
                print(f"\n🔴 CONSIDER SELLING ({len(sell_signals)} positions):")
                for currency, analysis in sorted(sell_signals, key=lambda x: x[1]['confidence'], reverse=True):
                    print(f"   {currency}: {analysis['confidence']:.1%} confidence")
                    print(f"      Current Value: ${analysis['position_value']:,.2f}")
                    print(f"      24h Change: {analysis['change_24h']:+.2f}%")
                    print(f"      Risk Level: {analysis['risk_level']}")
                    print(f"      Reason: {analysis['reasoning']}")
                    print()
            
            if buy_signals:
                print(f"\n🟢 CONSIDER HOLDING/BUYING MORE ({len(buy_signals)} positions):")
                for currency, analysis in sorted(buy_signals, key=lambda x: x[1]['confidence'], reverse=True):
                    print(f"   {currency}: {analysis['confidence']:.1%} confidence")
                    print(f"      Current Value: ${analysis['position_value']:,.2f}")
                    print(f"      24h Change: {analysis['change_24h']:+.2f}%")
                    print(f"      Risk Level: {analysis['risk_level']}")
                    if analysis['target_price']:
                        potential = ((analysis['target_price'] - analysis['current_price']) / analysis['current_price']) * 100
                        print(f"      Potential Upside: +{potential:.2f}%")
                    print(f"      Reason: {analysis['reasoning']}")
                    print()
            
            if hold_signals:
                print(f"\n⚪ NEUTRAL/HOLD ({len(hold_signals)} positions):")
                for currency, analysis in hold_signals:
                    print(f"   {currency}: ${analysis['position_value']:,.2f} ({analysis['change_24h']:+.2f}%)")
            
            # Risk Analysis
            print(f"\n⚠️  RISK ANALYSIS")
            print("=" * 50)
            
            high_risk_positions = [a for a in portfolio_analysis.values() if a['risk_level'] == 'HIGH']
            medium_risk_positions = [a for a in portfolio_analysis.values() if a['risk_level'] == 'MEDIUM']
            low_risk_positions = [a for a in portfolio_analysis.values() if a['risk_level'] == 'LOW']
            
            print(f"🔴 High Risk: {len(high_risk_positions)} positions")
            print(f"🟡 Medium Risk: {len(medium_risk_positions)} positions")
            print(f"🟢 Low Risk: {len(low_risk_positions)} positions")
            
            # Portfolio allocation
            print(f"\n📊 PORTFOLIO ALLOCATION")
            print("=" * 50)
            
            for currency, analysis in sorted(portfolio_analysis.items(), 
                                           key=lambda x: x[1]['position_value'], reverse=True):
                allocation = (analysis['position_value'] / total_portfolio_value) * 100
                print(f"{currency}: {allocation:.1f}% (${analysis['position_value']:,.2f})")
            
            return portfolio_analysis, total_portfolio_value
            
    except Exception as e:
        print(f"❌ Portfolio analysis failed: {e}")
        return {}, 0


async def create_trading_strategy():
    """Create custom trading strategy based on portfolio analysis"""
    print(f"\n🎯 CREATING CUSTOM TRADING STRATEGY")
    print("=" * 70)
    
    portfolio_analysis, total_value = await analyze_portfolio_holdings()
    
    if not portfolio_analysis:
        print("❌ Cannot create strategy without portfolio analysis")
        return
    
    # Strategy recommendations
    print(f"\n📋 STRATEGY RECOMMENDATIONS:")
    
    # 1. Diversification Strategy
    print(f"\n1. 🎯 DIVERSIFICATION STRATEGY:")
    print(f"   Current Holdings: {len(portfolio_analysis)} different tokens")
    
    # Check concentration risk
    largest_position = max(portfolio_analysis.values(), key=lambda x: x['position_value'])
    largest_allocation = (largest_position['position_value'] / total_value) * 100
    
    if largest_allocation > 30:
        print(f"   ⚠️  High concentration risk: {largest_allocation:.1f}% in one asset")
        print(f"   💡 Consider reducing position in {largest_position['symbol']}")
    else:
        print(f"   ✅ Good diversification: Largest position is {largest_allocation:.1f}%")
    
    # 2. Risk Management Strategy
    print(f"\n2. 🛡️  RISK MANAGEMENT STRATEGY:")
    
    high_risk_value = sum(a['position_value'] for a in portfolio_analysis.values() if a['risk_level'] == 'HIGH')
    high_risk_percent = (high_risk_value / total_value) * 100 if total_value > 0 else 0
    
    if high_risk_percent > 20:
        print(f"   ⚠️  High risk exposure: {high_risk_percent:.1f}% of portfolio")
        print(f"   💡 Consider reducing high-risk positions")
    else:
        print(f"   ✅ Acceptable risk level: {high_risk_percent:.1f}% high-risk exposure")
    
    # 3. Rebalancing Strategy
    print(f"\n3. ⚖️  REBALANCING STRATEGY:")
    
    sell_candidates = [a for a in portfolio_analysis.values() if a['signal'] == 'SELL' and a['confidence'] > 0.6]
    buy_candidates = [a for a in portfolio_analysis.values() if a['signal'] == 'BUY' and a['confidence'] > 0.6]
    
    if sell_candidates:
        print(f"   🔴 Strong sell signals: {len(sell_candidates)} positions")
        for candidate in sell_candidates:
            print(f"      - {candidate['symbol']}: {candidate['confidence']:.1%} confidence")
    
    if buy_candidates:
        print(f"   🟢 Strong buy signals: {len(buy_candidates)} positions")
        for candidate in buy_candidates:
            print(f"      - {candidate['symbol']}: {candidate['confidence']:.1%} confidence")
    
    # 4. Market Timing Strategy
    print(f"\n4. ⏰ MARKET TIMING STRATEGY:")
    
    avg_24h_change = sum(a['change_24h'] for a in portfolio_analysis.values()) / len(portfolio_analysis)
    
    if avg_24h_change > 5:
        print(f"   📈 Strong bullish momentum: +{avg_24h_change:.2f}% average")
        print(f"   💡 Consider holding or adding to strong positions")
    elif avg_24h_change < -5:
        print(f"   📉 Bearish trend: {avg_24h_change:.2f}% average")
        print(f"   💡 Consider defensive positioning or buying dips")
    else:
        print(f"   ⚖️  Neutral market: {avg_24h_change:.2f}% average")
        print(f"   💡 Focus on individual token analysis")
    
    # 5. Action Plan
    print(f"\n5. 📋 IMMEDIATE ACTION PLAN:")
    
    # Priority actions
    priority_sells = [a for a in portfolio_analysis.values() 
                     if a['signal'] == 'SELL' and a['confidence'] > 0.7 and a['risk_level'] in ['HIGH', 'MEDIUM']]
    
    priority_holds = [a for a in portfolio_analysis.values() 
                     if a['signal'] == 'BUY' and a['confidence'] > 0.7 and a['risk_level'] == 'LOW']
    
    if priority_sells:
        print(f"\n   🚨 URGENT: Consider selling these positions:")
        for pos in priority_sells:
            print(f"      {pos['symbol']}: {pos['confidence']:.1%} sell confidence, {pos['risk_level']} risk")
    
    if priority_holds:
        print(f"\n   💎 STRONG HOLDS: Keep these positions:")
        for pos in priority_holds:
            print(f"      {pos['symbol']}: {pos['confidence']:.1%} buy confidence, {pos['risk_level']} risk")
    
    print(f"\n✅ Custom trading strategy created successfully!")


async def main():
    """Main function"""
    try:
        # Analyze portfolio and create strategy
        await create_trading_strategy()
        
        print(f"\n🎉 Portfolio Analysis and Strategy Creation Complete!")
        print(f"\n💡 Next Steps:")
        print(f"   1. ✅ Review the analysis and recommendations")
        print(f"   2. 🎯 Implement the suggested strategy gradually")
        print(f"   3. 📊 Monitor positions regularly")
        print(f"   4. ⚖️  Rebalance based on market conditions")
        print(f"   5. 🛡️  Always use proper risk management")
        
        print(f"\n⚠️  Important Reminders:")
        print(f"   • Start with small position sizes")
        print(f"   • Use stop-losses to limit downside")
        print(f"   • Take profits gradually")
        print(f"   • Never invest more than you can afford to lose")
        
        return True
        
    except KeyboardInterrupt:
        print("\n👋 Analysis interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
