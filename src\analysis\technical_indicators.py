"""
Technical Indicators for AI Crypto Trading Bot
Comprehensive collection of technical analysis indicators
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from datetime import datetime
import warnings

from ..core.logger import logger


@dataclass
class IndicatorResult:
    """Result structure for technical indicators"""
    name: str
    value: Union[float, np.ndarray, pd.Series]
    signal: str  # 'BUY', 'SELL', 'HOLD'
    strength: float  # 0-1
    timestamp: datetime
    parameters: Dict
    
    def to_dict(self) -> Dict:
        return {
            'name': self.name,
            'value': self.value if isinstance(self.value, (int, float)) else self.value.tolist() if hasattr(self.value, 'tolist') else str(self.value),
            'signal': self.signal,
            'strength': self.strength,
            'timestamp': self.timestamp.isoformat(),
            'parameters': self.parameters
        }


class TechnicalIndicators:
    """
    Comprehensive technical indicators calculator
    """
    
    def __init__(self):
        self.indicators_cache = {}
        logger.info("Technical Indicators calculator initialized")
    
    # Moving Averages
    def sma(self, data: pd.Series, period: int = 20) -> pd.Series:
        """Simple Moving Average"""
        return data.rolling(window=period).mean()
    
    def ema(self, data: pd.Series, period: int = 20) -> pd.Series:
        """Exponential Moving Average"""
        return data.ewm(span=period).mean()
    
    def wma(self, data: pd.Series, period: int = 20) -> pd.Series:
        """Weighted Moving Average"""
        weights = np.arange(1, period + 1)
        return data.rolling(window=period).apply(
            lambda x: np.dot(x, weights) / weights.sum(), raw=True
        )
    
    def dema(self, data: pd.Series, period: int = 20) -> pd.Series:
        """Double Exponential Moving Average"""
        ema1 = self.ema(data, period)
        ema2 = self.ema(ema1, period)
        return 2 * ema1 - ema2
    
    def tema(self, data: pd.Series, period: int = 20) -> pd.Series:
        """Triple Exponential Moving Average"""
        ema1 = self.ema(data, period)
        ema2 = self.ema(ema1, period)
        ema3 = self.ema(ema2, period)
        return 3 * ema1 - 3 * ema2 + ema3
    
    # Oscillators
    def rsi(self, data: pd.Series, period: int = 14) -> pd.Series:
        """Relative Strength Index"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                   k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """Stochastic Oscillator"""
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return k_percent, d_percent
    
    def williams_r(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Williams %R"""
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        wr = -100 * ((highest_high - close) / (highest_high - lowest_low))
        return wr
    
    def cci(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
        """Commodity Channel Index"""
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(window=period).mean()
        mean_deviation = typical_price.rolling(window=period).apply(
            lambda x: np.mean(np.abs(x - x.mean())), raw=True
        )
        
        cci = (typical_price - sma_tp) / (0.015 * mean_deviation)
        return cci
    
    # MACD
    def macd(self, data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD (Moving Average Convergence Divergence)"""
        ema_fast = self.ema(data, fast)
        ema_slow = self.ema(data, slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = self.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram
    
    # Bollinger Bands
    def bollinger_bands(self, data: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Bollinger Bands"""
        sma = self.sma(data, period)
        std = data.rolling(window=period).std()
        
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        
        return upper_band, sma, lower_band
    
    # Volume Indicators
    def obv(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """On-Balance Volume"""
        obv = pd.Series(index=close.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]
        
        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]
        
        return obv
    
    def vwap(self, high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> pd.Series:
        """Volume Weighted Average Price"""
        typical_price = (high + low + close) / 3
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        return vwap
    
    def ad_line(self, high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> pd.Series:
        """Accumulation/Distribution Line"""
        clv = ((close - low) - (high - close)) / (high - low)
        clv = clv.fillna(0)  # Handle division by zero
        ad = (clv * volume).cumsum()
        return ad
    
    # Volatility Indicators
    def atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Average True Range"""
        high_low = high - low
        high_close_prev = np.abs(high - close.shift(1))
        low_close_prev = np.abs(low - close.shift(1))
        
        true_range = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        return atr
    
    def keltner_channels(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                        period: int = 20, multiplier: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Keltner Channels"""
        ema = self.ema(close, period)
        atr = self.atr(high, low, close, period)
        
        upper_channel = ema + (multiplier * atr)
        lower_channel = ema - (multiplier * atr)
        
        return upper_channel, ema, lower_channel
    
    # Trend Indicators
    def adx(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Average Directional Index"""
        # Calculate True Range
        tr = self.atr(high, low, close, 1)
        
        # Calculate Directional Movement
        dm_plus = pd.Series(index=high.index, dtype=float)
        dm_minus = pd.Series(index=high.index, dtype=float)
        
        for i in range(1, len(high)):
            high_diff = high.iloc[i] - high.iloc[i-1]
            low_diff = low.iloc[i-1] - low.iloc[i]
            
            if high_diff > low_diff and high_diff > 0:
                dm_plus.iloc[i] = high_diff
            else:
                dm_plus.iloc[i] = 0
                
            if low_diff > high_diff and low_diff > 0:
                dm_minus.iloc[i] = low_diff
            else:
                dm_minus.iloc[i] = 0
        
        # Smooth the values
        tr_smooth = tr.rolling(window=period).mean()
        dm_plus_smooth = dm_plus.rolling(window=period).mean()
        dm_minus_smooth = dm_minus.rolling(window=period).mean()
        
        # Calculate DI+ and DI-
        di_plus = 100 * (dm_plus_smooth / tr_smooth)
        di_minus = 100 * (dm_minus_smooth / tr_smooth)
        
        # Calculate ADX
        dx = 100 * np.abs(di_plus - di_minus) / (di_plus + di_minus)
        adx = dx.rolling(window=period).mean()
        
        return adx, di_plus, di_minus
    
    def parabolic_sar(self, high: pd.Series, low: pd.Series, close: pd.Series, 
                      af_start: float = 0.02, af_increment: float = 0.02, af_max: float = 0.2) -> pd.Series:
        """Parabolic SAR"""
        sar = pd.Series(index=close.index, dtype=float)
        trend = pd.Series(index=close.index, dtype=int)
        af = pd.Series(index=close.index, dtype=float)
        ep = pd.Series(index=close.index, dtype=float)
        
        # Initialize
        sar.iloc[0] = low.iloc[0]
        trend.iloc[0] = 1  # 1 for uptrend, -1 for downtrend
        af.iloc[0] = af_start
        ep.iloc[0] = high.iloc[0]
        
        for i in range(1, len(close)):
            if trend.iloc[i-1] == 1:  # Uptrend
                sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])
                
                if low.iloc[i] <= sar.iloc[i]:
                    trend.iloc[i] = -1
                    sar.iloc[i] = ep.iloc[i-1]
                    ep.iloc[i] = low.iloc[i]
                    af.iloc[i] = af_start
                else:
                    trend.iloc[i] = 1
                    if high.iloc[i] > ep.iloc[i-1]:
                        ep.iloc[i] = high.iloc[i]
                        af.iloc[i] = min(af.iloc[i-1] + af_increment, af_max)
                    else:
                        ep.iloc[i] = ep.iloc[i-1]
                        af.iloc[i] = af.iloc[i-1]
            else:  # Downtrend
                sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])
                
                if high.iloc[i] >= sar.iloc[i]:
                    trend.iloc[i] = 1
                    sar.iloc[i] = ep.iloc[i-1]
                    ep.iloc[i] = high.iloc[i]
                    af.iloc[i] = af_start
                else:
                    trend.iloc[i] = -1
                    if low.iloc[i] < ep.iloc[i-1]:
                        ep.iloc[i] = low.iloc[i]
                        af.iloc[i] = min(af.iloc[i-1] + af_increment, af_max)
                    else:
                        ep.iloc[i] = ep.iloc[i-1]
                        af.iloc[i] = af.iloc[i-1]
        
        return sar

    # Signal Generation Methods
    def generate_rsi_signal(self, rsi: pd.Series, oversold: float = 30, overbought: float = 70) -> IndicatorResult:
        """Generate trading signal from RSI"""
        current_rsi = rsi.iloc[-1]

        if current_rsi < oversold:
            signal = 'BUY'
            strength = (oversold - current_rsi) / oversold
        elif current_rsi > overbought:
            signal = 'SELL'
            strength = (current_rsi - overbought) / (100 - overbought)
        else:
            signal = 'HOLD'
            strength = 0.5

        return IndicatorResult(
            name='RSI',
            value=current_rsi,
            signal=signal,
            strength=min(strength, 1.0),
            timestamp=datetime.now(),
            parameters={'period': 14, 'oversold': oversold, 'overbought': overbought}
        )

    def generate_macd_signal(self, macd_line: pd.Series, signal_line: pd.Series, histogram: pd.Series) -> IndicatorResult:
        """Generate trading signal from MACD"""
        current_macd = macd_line.iloc[-1]
        current_signal = signal_line.iloc[-1]
        current_histogram = histogram.iloc[-1]
        prev_histogram = histogram.iloc[-2] if len(histogram) > 1 else 0

        # MACD crossover signals
        if current_macd > current_signal and prev_histogram < 0 < current_histogram:
            signal = 'BUY'
            strength = min(abs(current_histogram) / abs(current_macd), 1.0)
        elif current_macd < current_signal and prev_histogram > 0 > current_histogram:
            signal = 'SELL'
            strength = min(abs(current_histogram) / abs(current_macd), 1.0)
        else:
            signal = 'HOLD'
            strength = 0.5

        return IndicatorResult(
            name='MACD',
            value={'macd': current_macd, 'signal': current_signal, 'histogram': current_histogram},
            signal=signal,
            strength=strength,
            timestamp=datetime.now(),
            parameters={'fast': 12, 'slow': 26, 'signal': 9}
        )

    def generate_bollinger_signal(self, close: pd.Series, upper: pd.Series, lower: pd.Series, sma: pd.Series) -> IndicatorResult:
        """Generate trading signal from Bollinger Bands"""
        current_price = close.iloc[-1]
        current_upper = upper.iloc[-1]
        current_lower = lower.iloc[-1]
        current_sma = sma.iloc[-1]

        band_width = current_upper - current_lower
        price_position = (current_price - current_lower) / band_width

        if current_price <= current_lower:
            signal = 'BUY'
            strength = 1.0 - price_position
        elif current_price >= current_upper:
            signal = 'SELL'
            strength = price_position
        else:
            signal = 'HOLD'
            strength = 0.5

        return IndicatorResult(
            name='Bollinger_Bands',
            value={'price': current_price, 'upper': current_upper, 'lower': current_lower, 'sma': current_sma},
            signal=signal,
            strength=min(strength, 1.0),
            timestamp=datetime.now(),
            parameters={'period': 20, 'std_dev': 2}
        )

    def generate_stochastic_signal(self, k_percent: pd.Series, d_percent: pd.Series,
                                 oversold: float = 20, overbought: float = 80) -> IndicatorResult:
        """Generate trading signal from Stochastic Oscillator"""
        current_k = k_percent.iloc[-1]
        current_d = d_percent.iloc[-1]
        prev_k = k_percent.iloc[-2] if len(k_percent) > 1 else current_k
        prev_d = d_percent.iloc[-2] if len(d_percent) > 1 else current_d

        # Stochastic crossover in oversold/overbought regions
        if current_k < oversold and current_d < oversold and current_k > current_d and prev_k <= prev_d:
            signal = 'BUY'
            strength = (oversold - min(current_k, current_d)) / oversold
        elif current_k > overbought and current_d > overbought and current_k < current_d and prev_k >= prev_d:
            signal = 'SELL'
            strength = (min(current_k, current_d) - overbought) / (100 - overbought)
        else:
            signal = 'HOLD'
            strength = 0.5

        return IndicatorResult(
            name='Stochastic',
            value={'k': current_k, 'd': current_d},
            signal=signal,
            strength=min(strength, 1.0),
            timestamp=datetime.now(),
            parameters={'k_period': 14, 'd_period': 3, 'oversold': oversold, 'overbought': overbought}
        )

    # Comprehensive Analysis
    def analyze_all_indicators(self, df: pd.DataFrame) -> Dict[str, IndicatorResult]:
        """Analyze all technical indicators for a given DataFrame"""
        results = {}

        try:
            # Ensure we have required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_columns):
                logger.error(f"Missing required columns. Available: {df.columns.tolist()}")
                return results

            # Calculate indicators
            rsi = self.rsi(df['close'])
            macd_line, signal_line, histogram = self.macd(df['close'])
            upper_bb, sma_bb, lower_bb = self.bollinger_bands(df['close'])
            k_percent, d_percent = self.stochastic(df['high'], df['low'], df['close'])

            # Generate signals
            if not rsi.empty and not pd.isna(rsi.iloc[-1]):
                results['RSI'] = self.generate_rsi_signal(rsi)

            if not macd_line.empty and not pd.isna(macd_line.iloc[-1]):
                results['MACD'] = self.generate_macd_signal(macd_line, signal_line, histogram)

            if not upper_bb.empty and not pd.isna(upper_bb.iloc[-1]):
                results['Bollinger_Bands'] = self.generate_bollinger_signal(df['close'], upper_bb, lower_bb, sma_bb)

            if not k_percent.empty and not pd.isna(k_percent.iloc[-1]):
                results['Stochastic'] = self.generate_stochastic_signal(k_percent, d_percent)

            logger.info(f"Generated signals for {len(results)} indicators")

        except Exception as e:
            logger.error(f"Error in analyze_all_indicators: {e}")

        return results

    def get_trend_strength(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate overall trend strength using multiple indicators"""
        try:
            # Calculate trend indicators
            sma_20 = self.sma(df['close'], 20)
            sma_50 = self.sma(df['close'], 50)
            ema_12 = self.ema(df['close'], 12)
            ema_26 = self.ema(df['close'], 26)

            current_price = df['close'].iloc[-1]

            # Trend strength factors
            factors = {}

            # Price vs Moving Averages
            if not pd.isna(sma_20.iloc[-1]):
                factors['price_vs_sma20'] = (current_price - sma_20.iloc[-1]) / sma_20.iloc[-1]

            if not pd.isna(sma_50.iloc[-1]):
                factors['price_vs_sma50'] = (current_price - sma_50.iloc[-1]) / sma_50.iloc[-1]

            # Moving Average Alignment
            if not pd.isna(sma_20.iloc[-1]) and not pd.isna(sma_50.iloc[-1]):
                factors['ma_alignment'] = (sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]

            # EMA Momentum
            if not pd.isna(ema_12.iloc[-1]) and not pd.isna(ema_26.iloc[-1]):
                factors['ema_momentum'] = (ema_12.iloc[-1] - ema_26.iloc[-1]) / ema_26.iloc[-1]

            # Overall trend strength (-1 to 1)
            trend_strength = np.mean(list(factors.values())) if factors else 0

            return {
                'trend_strength': trend_strength,
                'factors': factors,
                'trend_direction': 'BULLISH' if trend_strength > 0.01 else 'BEARISH' if trend_strength < -0.01 else 'NEUTRAL'
            }

        except Exception as e:
            logger.error(f"Error calculating trend strength: {e}")
            return {'trend_strength': 0, 'factors': {}, 'trend_direction': 'NEUTRAL'}
