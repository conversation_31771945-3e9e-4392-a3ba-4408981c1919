#!/usr/bin/env python3
"""
Simple KuCoin Test - Public API Only
"""

import asyncio
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.kucoin_client import KuCoinClient, KuCoinAPIError


class SimpleKuCoinClient(KuCoinClient):
    """Simplified KuCoin client for public API only"""
    
    def __init__(self):
        # Initialize without credentials for public API only
        super().__init__(api_key="", secret_key="", passphrase="", sandbox=False)
    
    async def connect(self):
        """Connect without testing credentials"""
        if self.session is None:
            import aiohttp
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
        
        self.connected = True
        print("✅ Connected to KuCoin Public API")


async def test_public_api_and_create_strategy():
    """Test public API and create trading strategy"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║     🤖 KuCoin Trading Strategy Demo - Public API            ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    client = SimpleKuCoinClient()
    
    try:
        async with client:
            print("🔍 Testing KuCoin Public API and Creating Trading Strategy...")
            
            # Get market data
            print("\n📊 Getting Market Data...")
            
            # Get BTC ticker
            btc_ticker = await client.get_ticker('BTC-USDT')
            btc_price = float(btc_ticker.get('last', 0))
            btc_change = float(btc_ticker.get('changeRate', 0)) * 100
            
            print(f"   BTC-USDT: ${btc_price:,.2f} ({btc_change:+.2f}%)")
            
            # Get ETH ticker
            eth_ticker = await client.get_ticker('ETH-USDT')
            eth_price = float(eth_ticker.get('last', 0))
            eth_change = float(eth_ticker.get('changeRate', 0)) * 100
            
            print(f"   ETH-USDT: ${eth_price:,.2f} ({eth_change:+.2f}%)")
            
            # Get all tickers for analysis
            print("\n📈 Analyzing Market Trends...")
            all_tickers = await client.get_all_tickers()
            tickers_data = all_tickers.get('ticker', [])
            
            # Filter USDT pairs
            usdt_pairs = [t for t in tickers_data if t.get('symbol', '').endswith('-USDT')]
            
            # Find top gainers and losers
            gainers = sorted(usdt_pairs, key=lambda x: float(x.get('changeRate', 0)), reverse=True)[:10]
            losers = sorted(usdt_pairs, key=lambda x: float(x.get('changeRate', 0)))[:10]
            
            print(f"   📊 Analyzed {len(usdt_pairs)} USDT pairs")
            
            print(f"\n🚀 Top 5 Gainers:")
            for i, gainer in enumerate(gainers[:5], 1):
                change = float(gainer.get('changeRate', 0)) * 100
                price = float(gainer.get('last', 0))
                volume = float(gainer.get('vol', 0))
                print(f"   {i}. {gainer['symbol']}: ${price:,.4f} (+{change:.2f}%) Vol: {volume:,.0f}")
            
            print(f"\n📉 Top 5 Losers:")
            for i, loser in enumerate(losers[:5], 1):
                change = float(loser.get('changeRate', 0)) * 100
                price = float(loser.get('last', 0))
                volume = float(loser.get('vol', 0))
                print(f"   {i}. {loser['symbol']}: ${price:,.4f} ({change:.2f}%) Vol: {volume:,.0f}")
            
            # Create simple trading strategy
            print(f"\n🎯 Creating Simple Trading Strategy...")
            
            strategy_signals = []
            
            # Strategy 1: Momentum Trading
            print(f"\n📈 Momentum Strategy Analysis:")
            for ticker in gainers[:5]:
                symbol = ticker['symbol']
                change = float(ticker.get('changeRate', 0)) * 100
                volume = float(ticker.get('vol', 0))
                price = float(ticker.get('last', 0))
                
                # Simple momentum criteria
                if change > 5 and volume > 1000:  # >5% gain with good volume
                    signal = {
                        'symbol': symbol,
                        'strategy': 'Momentum',
                        'signal': 'BUY',
                        'price': price,
                        'change': change,
                        'volume': volume,
                        'reason': f'Strong momentum: +{change:.1f}% with volume {volume:,.0f}'
                    }
                    strategy_signals.append(signal)
                    print(f"   🟢 BUY {symbol}: {signal['reason']}")
            
            # Strategy 2: Mean Reversion
            print(f"\n📉 Mean Reversion Strategy Analysis:")
            for ticker in losers[:5]:
                symbol = ticker['symbol']
                change = float(ticker.get('changeRate', 0)) * 100
                volume = float(ticker.get('vol', 0))
                price = float(ticker.get('last', 0))
                
                # Simple mean reversion criteria
                if change < -5 and volume > 1000:  # >5% drop with good volume
                    signal = {
                        'symbol': symbol,
                        'strategy': 'Mean Reversion',
                        'signal': 'BUY',
                        'price': price,
                        'change': change,
                        'volume': volume,
                        'reason': f'Oversold: {change:.1f}% with volume {volume:,.0f}'
                    }
                    strategy_signals.append(signal)
                    print(f"   🟢 BUY {symbol}: {signal['reason']}")
            
            # Strategy Summary
            print(f"\n📋 Strategy Summary:")
            print(f"   Total Signals Generated: {len(strategy_signals)}")
            
            if strategy_signals:
                print(f"\n🎯 Trading Recommendations:")
                for i, signal in enumerate(strategy_signals, 1):
                    print(f"   {i}. {signal['signal']} {signal['symbol']} @ ${signal['price']:,.4f}")
                    print(f"      Strategy: {signal['strategy']}")
                    print(f"      Reason: {signal['reason']}")
                    print()
                
                # Risk management suggestions
                print(f"⚠️  Risk Management Recommendations:")
                print(f"   • Use only 1-2% of portfolio per trade")
                print(f"   • Set stop-loss at 3-5% below entry")
                print(f"   • Take profits at 10-15% gains")
                print(f"   • Monitor volume and market conditions")
                
            else:
                print(f"   ℹ️  No signals generated with current criteria")
                print(f"   💡 Consider adjusting strategy parameters")
            
            # Market overview
            print(f"\n🌍 Market Overview:")
            avg_change = sum(float(t.get('changeRate', 0)) for t in usdt_pairs) / len(usdt_pairs) * 100
            
            if avg_change > 1:
                market_sentiment = "BULLISH 📈"
            elif avg_change < -1:
                market_sentiment = "BEARISH 📉"
            else:
                market_sentiment = "NEUTRAL ⚖️"
            
            print(f"   Market Sentiment: {market_sentiment}")
            print(f"   Average Change: {avg_change:+.2f}%")
            print(f"   Active Pairs: {len(usdt_pairs)}")
            
            print(f"\n✅ KuCoin Public API integration successful!")
            print(f"🎯 Trading strategy created and ready for implementation!")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


async def create_trading_bot_demo():
    """Demo of a simple trading bot logic"""
    print(f"\n🤖 Trading Bot Demo - Simulation Mode")
    print("=" * 60)
    
    # Simulated portfolio
    portfolio = {
        'USDT': 1000.0,  # Starting with $1000
        'positions': {}
    }
    
    print(f"💰 Starting Portfolio: ${portfolio['USDT']:,.2f} USDT")
    
    client = SimpleKuCoinClient()
    
    try:
        async with client:
            # Get current market data
            btc_ticker = await client.get_ticker('BTC-USDT')
            eth_ticker = await client.get_ticker('ETH-USDT')
            
            btc_price = float(btc_ticker.get('last', 0))
            eth_price = float(eth_ticker.get('last', 0))
            
            btc_change = float(btc_ticker.get('changeRate', 0)) * 100
            eth_change = float(eth_ticker.get('changeRate', 0)) * 100
            
            print(f"\n📊 Current Market Prices:")
            print(f"   BTC-USDT: ${btc_price:,.2f} ({btc_change:+.2f}%)")
            print(f"   ETH-USDT: ${eth_price:,.2f} ({eth_change:+.2f}%)")
            
            # Simple trading logic simulation
            print(f"\n🎯 Trading Bot Logic:")
            
            # Example trade 1: BTC
            if btc_change > 2:  # If BTC is up more than 2%
                trade_amount = portfolio['USDT'] * 0.1  # Use 10% of portfolio
                btc_quantity = trade_amount / btc_price
                
                print(f"   🟢 SIMULATED BUY: {btc_quantity:.6f} BTC @ ${btc_price:,.2f}")
                print(f"      Reason: BTC momentum (+{btc_change:.2f}%)")
                print(f"      Investment: ${trade_amount:,.2f}")
                
                # Update simulated portfolio
                portfolio['USDT'] -= trade_amount
                portfolio['positions']['BTC'] = {
                    'quantity': btc_quantity,
                    'entry_price': btc_price,
                    'value': trade_amount
                }
            
            # Example trade 2: ETH
            if eth_change < -3:  # If ETH is down more than 3% (buy the dip)
                trade_amount = portfolio['USDT'] * 0.1  # Use 10% of portfolio
                eth_quantity = trade_amount / eth_price
                
                print(f"   🟢 SIMULATED BUY: {eth_quantity:.6f} ETH @ ${eth_price:,.2f}")
                print(f"      Reason: ETH oversold ({eth_change:.2f}%)")
                print(f"      Investment: ${trade_amount:,.2f}")
                
                # Update simulated portfolio
                portfolio['USDT'] -= trade_amount
                portfolio['positions']['ETH'] = {
                    'quantity': eth_quantity,
                    'entry_price': eth_price,
                    'value': trade_amount
                }
            
            # Portfolio summary
            print(f"\n💼 Updated Portfolio:")
            print(f"   Cash (USDT): ${portfolio['USDT']:,.2f}")
            
            total_value = portfolio['USDT']
            for symbol, position in portfolio['positions'].items():
                current_price = btc_price if symbol == 'BTC' else eth_price
                current_value = position['quantity'] * current_price
                pnl = current_value - position['value']
                pnl_percent = (pnl / position['value']) * 100
                
                print(f"   {symbol}: {position['quantity']:.6f} @ ${position['entry_price']:,.2f}")
                print(f"        Current Value: ${current_value:,.2f} (PnL: ${pnl:+.2f} / {pnl_percent:+.2f}%)")
                
                total_value += current_value
            
            print(f"\n📊 Portfolio Summary:")
            print(f"   Total Value: ${total_value:,.2f}")
            print(f"   Total PnL: ${total_value - 1000:+.2f}")
            print(f"   Return: {((total_value - 1000) / 1000) * 100:+.2f}%")
            
            print(f"\n✅ Trading bot simulation completed!")
            
    except Exception as e:
        print(f"❌ Trading bot demo failed: {e}")


async def main():
    """Main function"""
    try:
        # Test public API and create strategy
        success = await test_public_api_and_create_strategy()
        
        if success:
            # Run trading bot demo
            await create_trading_bot_demo()
            
            print(f"\n🎉 All tests completed successfully!")
            print(f"\n💡 Next Steps:")
            print(f"   1. ✅ KuCoin public API integration working")
            print(f"   2. ✅ Market data collection working")
            print(f"   3. ✅ Trading strategy logic created")
            print(f"   4. ✅ Trading bot simulation working")
            print(f"   5. 🔧 Add correct KUCOIN_PASSPHRASE for live trading")
            
            print(f"\n🔐 For Live Trading:")
            print(f"   • Verify your KuCoin API passphrase")
            print(f"   • Ensure API keys have trading permissions")
            print(f"   • Start with small amounts for testing")
            
        return success
        
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
