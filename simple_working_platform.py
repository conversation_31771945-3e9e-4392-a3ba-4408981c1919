#!/usr/bin/env python3
"""
Simple Working Trading Platform
A functional trading platform that actually works
"""

import sys
import os
from pathlib import Path
import json
import sqlite3
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import uuid
import threading
import time
import logging
from dataclasses import dataclass, asdict
import asyncio
import warnings
warnings.filterwarnings('ignore')

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Web framework
try:
    from flask import Flask, render_template, jsonify, request
    from flask_socketio import SocketIO, emit
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

# Trading components
from src.data.kucoin_client import KuCoinClient
from src.analysis import MarketAnalyzer
from src.core.logger import logger

@dataclass
class SimpleTradingSignal:
    symbol: str
    signal_type: str  # BUY, SELL, HOLD
    confidence: float
    current_price: float
    target_price: Optional[float]
    stop_loss: Optional[float]
    reasoning: str
    timestamp: str

@dataclass
class MarketData:
    symbol: str
    price: float
    change_24h: float
    volume_24h: float
    high_24h: float
    low_24h: float
    timestamp: str

class SimpleWorkingPlatform:
    """
    Simple Working Trading Platform - Actually Functional
    """
    
    def __init__(self):
        if not FLASK_AVAILABLE:
            print("❌ Flask not available. Install with:")
            print("   pip install flask flask-socketio")
            sys.exit(1)
        
        # Initialize Flask app
        self.app = Flask(__name__)
        self.app.config.update({
            'SECRET_KEY': 'simple_working_platform_secret'
        })
        
        self.socketio = SocketIO(self.app, cors_allowed_origins="*", async_mode='threading')
        
        # Trading components
        self.kucoin_client = KuCoinClient()
        self.market_analyzer = MarketAnalyzer()
        
        # Data storage
        self.market_data = {}
        self.trading_signals = {}
        self.portfolio_data = {
            'total_value_usdt': 10000.0,
            'assets': {},
            'trades_today': 0,
            'profit_today': 0.0
        }
        
        # Trading configuration
        self.trading_config = {
            'auto_trading_enabled': False,
            'risk_per_trade': 0.02,  # 2%
            'min_confidence': 0.7,   # 70%
            'max_daily_trades': 20,
            'symbols': ['BTC-USDT', 'ETH-USDT', 'BNB-USDT', 'ADA-USDT', 'XRP-USDT']
        }
        
        # Performance metrics
        self.performance = {
            'total_trades': 0,
            'successful_trades': 0,
            'total_profit': 0.0,
            'win_rate': 0.0,
            'last_updated': datetime.now().isoformat()
        }
        
        # Background tasks
        self.running = False
        self.background_tasks = {}
        
        # Setup routes and events
        self._setup_routes()
        self._setup_socketio_events()
        
        logger.info("Simple Working Platform initialized")
    
    def _setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            return self._render_platform()
        
        @self.app.route('/api/market_data')
        def get_market_data():
            return jsonify({
                'data': list(self.market_data.values()),
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/trading_signals')
        def get_trading_signals():
            return jsonify({
                'signals': [asdict(signal) for signal in self.trading_signals.values()],
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/portfolio')
        def get_portfolio():
            return jsonify(self.portfolio_data)
        
        @self.app.route('/api/performance')
        def get_performance():
            return jsonify(self.performance)
        
        @self.app.route('/api/config', methods=['GET', 'POST'])
        def trading_config():
            if request.method == 'POST':
                config_update = request.json
                self.trading_config.update(config_update)
                return jsonify({'status': 'updated', 'config': self.trading_config})
            return jsonify(self.trading_config)
        
        @self.app.route('/api/start_trading', methods=['POST'])
        def start_trading():
            self.trading_config['auto_trading_enabled'] = True
            return jsonify({'status': 'started', 'message': 'Auto trading started'})
        
        @self.app.route('/api/stop_trading', methods=['POST'])
        def stop_trading():
            self.trading_config['auto_trading_enabled'] = False
            return jsonify({'status': 'stopped', 'message': 'Auto trading stopped'})
        
        @self.app.route('/api/analyze/<symbol>')
        def analyze_symbol(symbol):
            try:
                signal = self._analyze_symbol_sync(symbol)
                if signal:
                    self.trading_signals[symbol] = signal
                    return jsonify({'signal': asdict(signal), 'status': 'success'})
                else:
                    return jsonify({'status': 'no_signal', 'message': 'No trading signal generated'})
            except Exception as e:
                return jsonify({'status': 'error', 'message': str(e)})
    
    def _setup_socketio_events(self):
        """Setup SocketIO events"""
        
        @self.socketio.on('connect')
        def handle_connect():
            emit('connected', {
                'message': 'Connected to Simple Working Platform',
                'timestamp': datetime.now().isoformat()
            })
        
        @self.socketio.on('request_market_update')
        def handle_market_update():
            self._update_market_data()
            emit('market_data_updated', {
                'data': list(self.market_data.values()),
                'timestamp': datetime.now().isoformat()
            })
        
        @self.socketio.on('request_analysis')
        def handle_analysis_request(data):
            symbol = data.get('symbol', 'BTC-USDT')
            try:
                signal = self._analyze_symbol_sync(symbol)
                if signal:
                    self.trading_signals[symbol] = signal
                    emit('analysis_complete', {
                        'symbol': symbol,
                        'signal': asdict(signal)
                    })
                else:
                    emit('analysis_complete', {
                        'symbol': symbol,
                        'signal': None,
                        'message': 'No signal generated'
                    })
            except Exception as e:
                emit('analysis_error', {
                    'symbol': symbol,
                    'error': str(e)
                })
    
    def _update_market_data(self):
        """Update market data synchronously"""
        try:
            # Simulate market data update
            for symbol in self.trading_config['symbols']:
                try:
                    # Get real data from KuCoin
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    async def get_ticker():
                        async with self.kucoin_client:
                            return await self.kucoin_client.get_ticker(symbol)
                    
                    ticker = loop.run_until_complete(get_ticker())
                    loop.close()
                    
                    if ticker:
                        market_data = MarketData(
                            symbol=symbol,
                            price=float(ticker.get('price', 0)),
                            change_24h=float(ticker.get('changeRate', 0)) * 100,
                            volume_24h=float(ticker.get('vol', 0)),
                            high_24h=float(ticker.get('high', 0)),
                            low_24h=float(ticker.get('low', 0)),
                            timestamp=datetime.now().isoformat()
                        )
                        self.market_data[symbol] = market_data
                        
                except Exception as e:
                    logger.error(f"Error updating market data for {symbol}: {e}")
                    # Use simulated data as fallback
                    base_price = {'BTC-USDT': 45000, 'ETH-USDT': 3000, 'BNB-USDT': 300, 'ADA-USDT': 0.5, 'XRP-USDT': 0.6}
                    price = base_price.get(symbol, 100) * (1 + np.random.uniform(-0.05, 0.05))
                    
                    market_data = MarketData(
                        symbol=symbol,
                        price=price,
                        change_24h=np.random.uniform(-10, 10),
                        volume_24h=np.random.uniform(1000000, 10000000),
                        high_24h=price * 1.05,
                        low_24h=price * 0.95,
                        timestamp=datetime.now().isoformat()
                    )
                    self.market_data[symbol] = market_data
            
            logger.info(f"Market data updated for {len(self.market_data)} symbols")
            
        except Exception as e:
            logger.error(f"Market data update failed: {e}")
    
    def _analyze_symbol_sync(self, symbol: str) -> Optional[SimpleTradingSignal]:
        """Analyze symbol and generate trading signal synchronously"""
        try:
            # Get market data
            if symbol not in self.market_data:
                self._update_market_data()
            
            if symbol not in self.market_data:
                return None
            
            market_data = self.market_data[symbol]
            current_price = market_data.price
            
            # Simple technical analysis
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                async def get_analysis():
                    async with self.kucoin_client:
                        klines = await self.kucoin_client.get_klines(symbol, '1hour', 50)
                        if klines and len(klines) >= 20:
                            # Convert to DataFrame
                            data = []
                            for kline in reversed(klines[-20:]):  # Last 20 candles
                                data.append({
                                    'close': float(kline[2]),
                                    'high': float(kline[3]),
                                    'low': float(kline[4]),
                                    'volume': float(kline[5])
                                })
                            
                            df = pd.DataFrame(data)
                            
                            # Simple moving averages
                            df['sma_5'] = df['close'].rolling(5).mean()
                            df['sma_10'] = df['close'].rolling(10).mean()
                            
                            # Simple RSI
                            delta = df['close'].diff()
                            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                            rs = gain / loss
                            df['rsi'] = 100 - (100 / (1 + rs))
                            
                            return df
                        return None
                
                df = loop.run_until_complete(get_analysis())
                loop.close()
                
                if df is not None and len(df) >= 10:
                    # Generate signal based on simple rules
                    latest = df.iloc[-1]
                    sma_5 = latest['sma_5']
                    sma_10 = latest['sma_10']
                    rsi = latest['rsi']
                    
                    # Simple signal logic
                    if pd.notna(sma_5) and pd.notna(sma_10) and pd.notna(rsi):
                        if current_price > sma_5 > sma_10 and rsi < 70:
                            signal_type = "BUY"
                            confidence = min(0.9, (current_price - sma_10) / sma_10 * 10)
                            target_price = current_price * 1.03  # 3% target
                            stop_loss = current_price * 0.98     # 2% stop loss
                            reasoning = f"Price above SMA5 ({sma_5:.2f}) and SMA10 ({sma_10:.2f}), RSI: {rsi:.1f}"
                            
                        elif current_price < sma_5 < sma_10 and rsi > 30:
                            signal_type = "SELL"
                            confidence = min(0.9, (sma_10 - current_price) / sma_10 * 10)
                            target_price = current_price * 0.97  # 3% target
                            stop_loss = current_price * 1.02     # 2% stop loss
                            reasoning = f"Price below SMA5 ({sma_5:.2f}) and SMA10 ({sma_10:.2f}), RSI: {rsi:.1f}"
                            
                        else:
                            signal_type = "HOLD"
                            confidence = 0.5
                            target_price = None
                            stop_loss = None
                            reasoning = f"No clear trend, SMA5: {sma_5:.2f}, SMA10: {sma_10:.2f}, RSI: {rsi:.1f}"
                        
                        return SimpleTradingSignal(
                            symbol=symbol,
                            signal_type=signal_type,
                            confidence=max(0.1, min(0.95, confidence)),
                            current_price=current_price,
                            target_price=target_price,
                            stop_loss=stop_loss,
                            reasoning=reasoning,
                            timestamp=datetime.now().isoformat()
                        )
                
            except Exception as e:
                logger.error(f"Technical analysis failed for {symbol}: {e}")
            
            # Fallback simple signal based on price change
            change_24h = market_data.change_24h
            if change_24h > 5:
                signal_type = "BUY"
                confidence = min(0.8, change_24h / 10)
                reasoning = f"Strong positive momentum: +{change_24h:.1f}%"
            elif change_24h < -5:
                signal_type = "SELL"
                confidence = min(0.8, abs(change_24h) / 10)
                reasoning = f"Strong negative momentum: {change_24h:.1f}%"
            else:
                signal_type = "HOLD"
                confidence = 0.5
                reasoning = f"Neutral momentum: {change_24h:.1f}%"
            
            return SimpleTradingSignal(
                symbol=symbol,
                signal_type=signal_type,
                confidence=confidence,
                current_price=current_price,
                target_price=current_price * 1.02 if signal_type == "BUY" else current_price * 0.98 if signal_type == "SELL" else None,
                stop_loss=current_price * 0.98 if signal_type == "BUY" else current_price * 1.02 if signal_type == "SELL" else None,
                reasoning=reasoning,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.error(f"Symbol analysis failed for {symbol}: {e}")
            return None
