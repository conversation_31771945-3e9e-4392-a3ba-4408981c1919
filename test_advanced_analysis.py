#!/usr/bin/env python3
"""
Advanced Technical Analysis Testing - Real Market Conditions
"""

import asyncio
import sys
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.analysis import TechnicalIndicators, PatternRecognition, MarketAnalyzer
from src.data import BinanceClient, DataCollector
from src.core.logger import logger


async def comprehensive_market_analysis():
    """Comprehensive real-time market analysis"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║     🤖 AI Crypto Trading Bot - Advanced System Test         ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Initialize components
    binance_client = BinanceClient(testnet=True)
    collector = DataCollector(binance_client)
    analyzer = MarketAnalyzer()
    
    # Disable database/Redis for testing
    collector.use_database = False
    collector.use_redis = False
    
    try:
        await binance_client.connect()
        print("✅ Connected to Binance Testnet")
        
        # Major cryptocurrency pairs
        major_pairs = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'DOTUSDT']
        
        print(f"\n📊 Analyzing {len(major_pairs)} major cryptocurrency pairs...")
        print("=" * 70)
        
        # Collect real-time data
        symbols_data = {}
        for symbol in major_pairs:
            try:
                print(f"📈 Collecting data for {symbol}...")
                df = await collector.collect_historical_data(symbol, "1h", days=14)
                if not df.empty:
                    symbols_data[symbol] = df
                    print(f"   ✅ {len(df)} data points | Range: ${df['low'].min():,.0f} - ${df['high'].max():,.0f}")
                else:
                    print(f"   ❌ No data available")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        if not symbols_data:
            print("❌ No data collected. Exiting.")
            return False
        
        print(f"\n🔍 Performing advanced technical analysis...")
        print("=" * 70)
        
        # Analyze all symbols
        signals = await analyzer.analyze_multiple_symbols(symbols_data)
        
        # Display comprehensive analysis
        print(f"\n📊 COMPREHENSIVE ANALYSIS RESULTS")
        print("=" * 70)
        
        for symbol, signal in signals.items():
            print(f"\n🪙 {symbol} Analysis:")
            print(f"   📈 Signal: {signal.signal_type}")
            print(f"   🎯 Confidence: {signal.confidence:.1%}")
            print(f"   💪 Strength: {signal.strength:.3f}")
            print(f"   ⚠️  Risk Level: {signal.risk_level}")
            print(f"   💰 Current Price: ${signal.entry_price:,.2f}")
            
            # Price targets and risk management
            if signal.target_price:
                potential_return = ((signal.target_price - signal.entry_price) / signal.entry_price) * 100
                print(f"   🎯 Target Price: ${signal.target_price:,.2f} ({potential_return:+.2f}%)")
            
            if signal.stop_loss:
                risk_percent = ((signal.stop_loss - signal.entry_price) / signal.entry_price) * 100
                print(f"   🛡️  Stop Loss: ${signal.stop_loss:,.2f} ({risk_percent:+.2f}%)")
                
                # Risk/Reward ratio
                if signal.target_price and signal.stop_loss:
                    potential_gain = abs(signal.target_price - signal.entry_price)
                    potential_loss = abs(signal.entry_price - signal.stop_loss)
                    rr_ratio = potential_gain / potential_loss if potential_loss > 0 else 0
                    print(f"   ⚖️  Risk/Reward: 1:{rr_ratio:.2f}")
            
            # Technical indicators breakdown
            active_indicators = []
            for name, tech_signal in signal.technical_signals.items():
                if tech_signal.signal != 'HOLD':
                    active_indicators.append(f"{name}:{tech_signal.signal}({tech_signal.strength:.2f})")
            
            if active_indicators:
                print(f"   📊 Active Indicators: {', '.join(active_indicators)}")
            else:
                print(f"   📊 All indicators neutral")
            
            # Pattern analysis
            if signal.pattern_signals:
                bullish_patterns = [p for p in signal.pattern_signals if p.pattern_type == 'BULLISH']
                bearish_patterns = [p for p in signal.pattern_signals if p.pattern_type == 'BEARISH']
                neutral_patterns = [p for p in signal.pattern_signals if p.pattern_type == 'NEUTRAL']
                
                print(f"   🔍 Patterns: {len(bullish_patterns)}🟢 {len(bearish_patterns)}🔴 {len(neutral_patterns)}⚪")
            
            # Trend analysis
            trend_info = signal.trend_analysis
            if trend_info:
                trend_direction = trend_info.get('trend_direction', 'NEUTRAL')
                trend_strength = trend_info.get('trend_strength', 0)
                print(f"   📈 Trend: {trend_direction} (strength: {trend_strength:.4f})")
            
            # Support and resistance
            support_resistance = signal.support_resistance
            if support_resistance:
                supports = support_resistance.get('support', [])
                resistances = support_resistance.get('resistance', [])
                
                if supports:
                    nearest_support = max([s for s in supports if s < signal.entry_price], default=None)
                    if nearest_support:
                        support_distance = ((signal.entry_price - nearest_support) / signal.entry_price) * 100
                        print(f"   🔻 Nearest Support: ${nearest_support:,.2f} (-{support_distance:.1f}%)")
                
                if resistances:
                    nearest_resistance = min([r for r in resistances if r > signal.entry_price], default=None)
                    if nearest_resistance:
                        resistance_distance = ((nearest_resistance - signal.entry_price) / signal.entry_price) * 100
                        print(f"   🔺 Nearest Resistance: ${nearest_resistance:,.2f} (+{resistance_distance:.1f}%)")
            
            print(f"   💭 Reasoning: {signal.reasoning}")
        
        # Market overview and insights
        print(f"\n🌍 MARKET OVERVIEW")
        print("=" * 70)
        
        overview = analyzer.get_market_overview(signals)
        
        print(f"📊 Market Statistics:")
        print(f"   Total Symbols: {overview['total_symbols']}")
        print(f"   Market Sentiment: {overview['market_sentiment']}")
        print(f"   Average Confidence: {overview['average_confidence']:.1%}")
        print(f"")
        print(f"📈 Signal Distribution:")
        print(f"   🟢 Buy Signals: {overview['buy_signals']} ({overview['buy_signals']/overview['total_symbols']*100:.0f}%)")
        print(f"   🔴 Sell Signals: {overview['sell_signals']} ({overview['sell_signals']/overview['total_symbols']*100:.0f}%)")
        print(f"   ⚪ Hold Signals: {overview['hold_signals']} ({overview['hold_signals']/overview['total_symbols']*100:.0f}%)")
        print(f"   🎯 High Confidence: {overview['high_confidence_signals']}")
        
        # Top trading opportunities
        if overview['top_opportunities']:
            print(f"\n🎯 TOP TRADING OPPORTUNITIES")
            print("-" * 50)
            
            for i, opp in enumerate(overview['top_opportunities'], 1):
                signal_emoji = "🟢" if opp['signal'] == 'BUY' else "🔴" if opp['signal'] == 'SELL' else "⚪"
                print(f"{i}. {signal_emoji} {opp['symbol']}: {opp['signal']}")
                print(f"   Confidence: {opp['confidence']:.1%} | Strength: {opp['strength']:.3f}")
        
        # Risk analysis
        print(f"\n⚠️  RISK ANALYSIS")
        print("-" * 50)
        
        risk_distribution = {'LOW': 0, 'MEDIUM': 0, 'HIGH': 0}
        total_potential_return = 0
        total_potential_risk = 0
        valid_trades = 0
        
        for signal in signals.values():
            risk_distribution[signal.risk_level] += 1
            
            if signal.target_price and signal.stop_loss:
                potential_return = ((signal.target_price - signal.entry_price) / signal.entry_price) * 100
                potential_risk = abs(((signal.stop_loss - signal.entry_price) / signal.entry_price) * 100)
                total_potential_return += potential_return
                total_potential_risk += potential_risk
                valid_trades += 1
        
        print(f"Risk Distribution:")
        print(f"   🟢 Low Risk: {risk_distribution['LOW']} signals")
        print(f"   🟡 Medium Risk: {risk_distribution['MEDIUM']} signals")
        print(f"   🔴 High Risk: {risk_distribution['HIGH']} signals")
        
        if valid_trades > 0:
            avg_potential_return = total_potential_return / valid_trades
            avg_potential_risk = total_potential_risk / valid_trades
            avg_rr_ratio = avg_potential_return / avg_potential_risk if avg_potential_risk > 0 else 0
            
            print(f"\nPortfolio Metrics:")
            print(f"   📈 Avg Potential Return: {avg_potential_return:+.2f}%")
            print(f"   📉 Avg Potential Risk: {avg_potential_risk:.2f}%")
            print(f"   ⚖️  Avg Risk/Reward: 1:{avg_rr_ratio:.2f}")
        
        # Trading recommendations
        print(f"\n💡 TRADING RECOMMENDATIONS")
        print("-" * 50)
        
        high_confidence_buys = [s for s in signals.values() 
                               if s.signal_type == 'BUY' and s.confidence > 0.6 and s.risk_level in ['LOW', 'MEDIUM']]
        high_confidence_sells = [s for s in signals.values() 
                                if s.signal_type == 'SELL' and s.confidence > 0.6 and s.risk_level in ['LOW', 'MEDIUM']]
        
        if high_confidence_buys:
            print("🟢 RECOMMENDED BUYS:")
            for signal in sorted(high_confidence_buys, key=lambda x: x.confidence, reverse=True):
                print(f"   {signal.symbol}: {signal.confidence:.1%} confidence, {signal.risk_level} risk")
                if signal.target_price:
                    potential = ((signal.target_price - signal.entry_price) / signal.entry_price) * 100
                    print(f"      Target: +{potential:.1f}% potential")
        
        if high_confidence_sells:
            print("🔴 RECOMMENDED SELLS:")
            for signal in sorted(high_confidence_sells, key=lambda x: x.confidence, reverse=True):
                print(f"   {signal.symbol}: {signal.confidence:.1%} confidence, {signal.risk_level} risk")
        
        if not high_confidence_buys and not high_confidence_sells:
            print("⚪ No high-confidence, low-risk opportunities detected.")
            print("   Consider waiting for better market conditions.")
        
        # Market sentiment analysis
        print(f"\n🌡️  MARKET SENTIMENT ANALYSIS")
        print("-" * 50)
        
        if overview['market_sentiment'] == 'BULLISH':
            print("📈 BULLISH MARKET DETECTED")
            print("   • Consider long positions")
            print("   • Look for breakout opportunities")
            print("   • Focus on momentum strategies")
        elif overview['market_sentiment'] == 'BEARISH':
            print("📉 BEARISH MARKET DETECTED")
            print("   • Consider short positions")
            print("   • Look for breakdown patterns")
            print("   • Focus on defensive strategies")
        else:
            print("📊 NEUTRAL/CONSOLIDATING MARKET")
            print("   • Consider range trading")
            print("   • Wait for clear directional signals")
            print("   • Focus on support/resistance levels")
        
        print(f"\n✅ ANALYSIS COMPLETE!")
        print(f"📊 Analyzed {len(signals)} symbols with {overview['average_confidence']:.1%} average confidence")
        print(f"🎯 Found {len(overview['top_opportunities'])} trading opportunities")
        
        return True
        
    except Exception as e:
        logger.error(f"Analysis error: {e}")
        print(f"❌ Analysis failed: {e}")
        return False
    
    finally:
        await binance_client.disconnect()
        print("\n🔌 Disconnected from Binance API")


async def stress_test_analysis():
    """Stress test the analysis system with edge cases"""
    print(f"\n🧪 STRESS TESTING ANALYSIS SYSTEM")
    print("=" * 70)
    
    indicators = TechnicalIndicators()
    patterns = PatternRecognition()
    
    # Test with minimal data
    print("🔬 Testing with minimal data...")
    minimal_df = pd.DataFrame({
        'open': [100, 101, 102],
        'high': [105, 106, 107],
        'low': [95, 96, 97],
        'close': [103, 104, 105],
        'volume': [1000, 1100, 1200],
        'quote_volume': [103000, 114400, 126000],
        'trades_count': [50, 55, 60]
    })
    
    try:
        signals = indicators.analyze_all_indicators(minimal_df)
        print(f"   ✅ Minimal data test passed: {len(signals)} signals generated")
    except Exception as e:
        print(f"   ❌ Minimal data test failed: {e}")
    
    # Test with extreme volatility
    print("🔬 Testing with extreme volatility...")
    volatile_data = []
    base_price = 50000
    for i in range(50):
        volatility = np.random.uniform(-0.1, 0.1)  # ±10% volatility
        price = base_price * (1 + volatility)
        volatile_data.append({
            'open': price * 0.99,
            'high': price * 1.05,
            'low': price * 0.95,
            'close': price,
            'volume': np.random.uniform(100, 1000),
            'quote_volume': price * np.random.uniform(100, 1000),
            'trades_count': int(np.random.uniform(50, 200))
        })
        base_price = price
    
    volatile_df = pd.DataFrame(volatile_data)
    
    try:
        signals = indicators.analyze_all_indicators(volatile_df)
        pattern_results = patterns.analyze_all_patterns(volatile_df)
        print(f"   ✅ Volatility test passed: {len(signals)} signals, {len(pattern_results.get('candlestick_patterns', []))} patterns")
    except Exception as e:
        print(f"   ❌ Volatility test failed: {e}")
    
    # Test with trending data
    print("🔬 Testing with strong trending data...")
    trend_data = []
    base_price = 40000
    for i in range(100):
        trend = 0.01  # 1% uptrend per period
        noise = np.random.uniform(-0.005, 0.005)  # ±0.5% noise
        price = base_price * (1 + trend + noise)
        trend_data.append({
            'open': base_price,
            'high': max(base_price, price) * 1.01,
            'low': min(base_price, price) * 0.99,
            'close': price,
            'volume': np.random.uniform(500, 1500),
            'quote_volume': price * np.random.uniform(500, 1500),
            'trades_count': int(np.random.uniform(100, 300))
        })
        base_price = price
    
    trend_df = pd.DataFrame(trend_data)
    
    try:
        trend_analysis = indicators.get_trend_strength(trend_df)
        signals = indicators.analyze_all_indicators(trend_df)
        print(f"   ✅ Trend test passed: {trend_analysis['trend_direction']} trend detected")
        print(f"       Trend strength: {trend_analysis['trend_strength']:.4f}")
    except Exception as e:
        print(f"   ❌ Trend test failed: {e}")
    
    print("✅ Stress testing completed!")


async def performance_benchmark():
    """Benchmark the performance of the analysis system"""
    print(f"\n⚡ PERFORMANCE BENCHMARK")
    print("=" * 70)
    
    analyzer = MarketAnalyzer()
    
    # Generate test data of different sizes
    sizes = [50, 100, 500, 1000]
    
    for size in sizes:
        print(f"📊 Benchmarking with {size} data points...")
        
        # Generate test data
        test_data = []
        base_price = 50000
        for i in range(size):
            change = np.random.normal(0, 0.02)  # 2% standard deviation
            price = base_price * (1 + change)
            test_data.append({
                'open': base_price,
                'high': max(base_price, price) * (1 + abs(np.random.normal(0, 0.01))),
                'low': min(base_price, price) * (1 - abs(np.random.normal(0, 0.01))),
                'close': price,
                'volume': np.random.uniform(100, 1000),
                'quote_volume': price * np.random.uniform(100, 1000),
                'trades_count': int(np.random.uniform(50, 200))
            })
            base_price = price
        
        df = pd.DataFrame(test_data)
        
        # Benchmark analysis time
        start_time = datetime.now()
        signal = await analyzer.analyze_symbol(f"TEST{size}USDT", df)
        end_time = datetime.now()
        
        duration = (end_time - start_time).total_seconds()
        
        print(f"   ⏱️  Analysis time: {duration:.3f} seconds")
        print(f"   📈 Signal: {signal.signal_type} (confidence: {signal.confidence:.2f})")
        print(f"   🔍 Technical signals: {len(signal.technical_signals)}")
        print(f"   📊 Patterns detected: {len(signal.pattern_signals)}")
    
    print("✅ Performance benchmark completed!")


async def main():
    """Main testing function"""
    try:
        # Run comprehensive market analysis
        success = await comprehensive_market_analysis()
        
        if success:
            # Run stress tests
            await stress_test_analysis()
            
            # Run performance benchmark
            await performance_benchmark()
            
            print(f"\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
            print("✨ Your AI Crypto Trading Bot is performing excellently!")
        
    except KeyboardInterrupt:
        print("\n👋 Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Testing error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
