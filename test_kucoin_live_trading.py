#!/usr/bin/env python3
"""
KuCoin Live Trading Test - Real Account Integration
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.kucoin_client import KuCoinClient, KuCoinAPIError
from src.trading.order_manager import OrderManager, OrderRequest, OrderType
from src.core.config import settings
from src.core.logger import logger


async def test_kucoin_authentication():
    """Test KuCoin authentication with real credentials"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🤖 KuCoin Live Trading Test - Real Account           ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    print(f"🔧 Configuration:")
    print(f"   API Key: {settings.kucoin_api_key[:10]}...")
    print(f"   Secret Key: {settings.kucoin_secret_key[:10]}...")
    print(f"   Passphrase: {settings.kucoin_passphrase[:10]}...")
    print(f"   Sandbox Mode: {settings.kucoin_sandbox}")
    
    client = KuCoinClient()
    
    try:
        async with client:
            print("\n✅ Connected to KuCoin API with authentication")
            
            # Test 1: Account Information
            print("\n🔍 Testing Account Information...")
            accounts = await client.get_account_info()
            
            print(f"   ✅ Account information retrieved")
            print(f"   📈 Total accounts: {len(accounts)}")
            
            # Show trading accounts
            trading_accounts = [acc for acc in accounts if acc['type'] == 'trade']
            print(f"   💼 Trading accounts: {len(trading_accounts)}")
            
            # Show balances
            total_value_usdt = 0
            currencies_with_balance = []
            
            for account in trading_accounts:
                balance = float(account['balance'])
                available = float(account['available'])
                currency = account['currency']
                
                if balance > 0:
                    currencies_with_balance.append({
                        'currency': currency,
                        'balance': balance,
                        'available': available
                    })
                    
                    print(f"      {currency}: {balance:.8f} (Available: {available:.8f})")
                    
                    # Estimate USDT value
                    if currency == 'USDT':
                        total_value_usdt += balance
                    elif currency in ['BTC', 'ETH', 'BNB']:
                        try:
                            ticker = await client.get_ticker(f"{currency}-USDT")
                            price = float(ticker.get('last', 0))
                            value_usdt = balance * price
                            total_value_usdt += value_usdt
                            print(f"         ≈ ${value_usdt:.2f} USDT @ ${price:,.2f}")
                        except:
                            pass
            
            print(f"\n   💵 Estimated total portfolio value: ≈ ${total_value_usdt:.2f} USDT")
            
            if not currencies_with_balance:
                print("   ⚠️  No balances found. Account may be empty.")
                return False
            
            # Test 2: Active Orders
            print("\n📋 Testing Active Orders...")
            try:
                active_orders = await client.get_orders(status="active")
                print(f"   ✅ Active orders: {len(active_orders)}")
                
                if active_orders:
                    for order in active_orders[:5]:  # Show first 5
                        print(f"      {order['symbol']}: {order['side']} {order['size']} @ {order['price']}")
                        print(f"         Status: {order['isActive']} | Created: {order['createdAt']}")
                else:
                    print(f"   ℹ️  No active orders")
                
            except Exception as e:
                print(f"   ⚠️  Could not retrieve orders: {e}")
            
            # Test 3: Order History (recent)
            print("\n📜 Testing Order History...")
            try:
                filled_orders = await client.get_orders(status="done")
                print(f"   ✅ Recent filled orders: {len(filled_orders)}")
                
                if filled_orders:
                    print("   📊 Recent trades:")
                    for order in filled_orders[:3]:  # Show last 3
                        deal_size = float(order.get('dealSize', 0))
                        deal_funds = float(order.get('dealFunds', 0))
                        avg_price = deal_funds / deal_size if deal_size > 0 else 0
                        
                        print(f"      {order['symbol']}: {order['side']} {deal_size:.6f} @ ${avg_price:.2f}")
                        print(f"         Filled: {order.get('dealSize')} | Funds: ${deal_funds:.2f}")
                else:
                    print(f"   ℹ️  No recent filled orders")
                
            except Exception as e:
                print(f"   ⚠️  Could not retrieve order history: {e}")
            
            print("\n✅ Authentication and account access successful!")
            return True, currencies_with_balance
            
    except KuCoinAPIError as e:
        print(f"❌ KuCoin API Error: {e}")
        if "Invalid KC-API-PASSPHRASE" in str(e):
            print("   🔧 Check your KUCOIN_PASSPHRASE in .env file")
        elif "Invalid KC-API-KEY" in str(e):
            print("   🔧 Check your KUCOIN_API_KEY in .env file")
        elif "Invalid KC-API-SIGN" in str(e):
            print("   🔧 Check your KUCOIN_SECRET_KEY in .env file")
        return False, []
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False, []


async def test_dry_run_trading():
    """Test trading system in dry run mode"""
    print(f"\n🧪 Testing Dry Run Trading System...")
    print("=" * 60)
    
    client = KuCoinClient()
    order_manager = OrderManager(client)
    
    # Enable dry run mode for safety
    order_manager.set_dry_run(True)
    
    try:
        async with client:
            # Start order monitoring
            await order_manager.start_monitoring()
            
            print("✅ Order manager initialized in DRY RUN mode")
            
            # Test 1: Market Buy Order (Simulated)
            print("\n📊 Testing Market Buy Order (Simulated)...")
            
            buy_order_request = OrderRequest(
                symbol="BTC-USDT",
                side="buy",
                order_type=OrderType.MARKET,
                quantity=0.001,  # Small amount for testing
                client_order_id="TEST_BUY_001"
            )
            
            buy_order = await order_manager.place_order(buy_order_request)
            
            if buy_order:
                print(f"   ✅ Simulated buy order placed:")
                print(f"      Order ID: {buy_order.order_id}")
                print(f"      Symbol: {buy_order.symbol}")
                print(f"      Side: {buy_order.side}")
                print(f"      Quantity: {buy_order.quantity}")
                print(f"      Price: ${buy_order.price:,.2f}")
                print(f"      Status: {buy_order.status.value}")
            else:
                print("   ❌ Failed to place simulated buy order")
            
            # Test 2: Limit Sell Order (Simulated)
            print("\n📊 Testing Limit Sell Order (Simulated)...")
            
            # Get current BTC price
            ticker = await client.get_ticker("BTC-USDT")
            current_price = float(ticker.get('last', 0))
            sell_price = current_price * 1.05  # 5% above current price
            
            sell_order_request = OrderRequest(
                symbol="BTC-USDT",
                side="sell",
                order_type=OrderType.LIMIT,
                quantity=0.001,
                price=sell_price,
                client_order_id="TEST_SELL_001"
            )
            
            sell_order = await order_manager.place_order(sell_order_request)
            
            if sell_order:
                print(f"   ✅ Simulated sell order placed:")
                print(f"      Order ID: {sell_order.order_id}")
                print(f"      Symbol: {sell_order.symbol}")
                print(f"      Side: {sell_order.side}")
                print(f"      Quantity: {sell_order.quantity}")
                print(f"      Price: ${sell_order.price:,.2f}")
                print(f"      Status: {sell_order.status.value}")
                
                # Test order cancellation
                print(f"\n❌ Testing Order Cancellation...")
                cancelled = await order_manager.cancel_order(sell_order.order_id)
                if cancelled:
                    print(f"   ✅ Order cancelled successfully")
                else:
                    print(f"   ❌ Failed to cancel order")
            else:
                print("   ❌ Failed to place simulated sell order")
            
            # Test 3: Order Status and History
            print("\n📋 Testing Order Management...")
            
            active_orders = order_manager.get_active_orders()
            order_history = order_manager.get_order_history()
            
            print(f"   📊 Active orders: {len(active_orders)}")
            print(f"   📜 Order history: {len(order_history)}")
            
            if order_history:
                print("   📋 Recent orders:")
                for order in order_history[-3:]:  # Last 3 orders
                    print(f"      {order.symbol}: {order.side} {order.quantity} @ ${order.price:.2f} ({order.status.value})")
            
            # Stop monitoring
            await order_manager.stop_monitoring()
            
            print("\n✅ Dry run trading tests completed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Dry run trading test failed: {e}")
        return False


async def test_trading_validation():
    """Test trading validation and safety checks"""
    print(f"\n🛡️  Testing Trading Validation and Safety...")
    print("=" * 60)
    
    client = KuCoinClient()
    order_manager = OrderManager(client)
    order_manager.set_dry_run(True)  # Keep in dry run for safety
    
    try:
        async with client:
            print("✅ Testing order validation system")
            
            # Test 1: Invalid Symbol
            print("\n🔍 Testing invalid symbol validation...")
            invalid_order = OrderRequest(
                symbol="INVALID-USDT",
                side="buy",
                order_type=OrderType.MARKET,
                quantity=0.001
            )
            
            result = await order_manager.place_order(invalid_order)
            if result is None:
                print("   ✅ Invalid symbol correctly rejected")
            else:
                print("   ⚠️  Invalid symbol was not rejected")
            
            # Test 2: Zero Quantity
            print("\n🔍 Testing zero quantity validation...")
            zero_qty_order = OrderRequest(
                symbol="BTC-USDT",
                side="buy",
                order_type=OrderType.MARKET,
                quantity=0
            )
            
            result = await order_manager.place_order(zero_qty_order)
            if result is None:
                print("   ✅ Zero quantity correctly rejected")
            else:
                print("   ⚠️  Zero quantity was not rejected")
            
            # Test 3: Limit Order Without Price
            print("\n🔍 Testing limit order without price...")
            no_price_order = OrderRequest(
                symbol="BTC-USDT",
                side="buy",
                order_type=OrderType.LIMIT,
                quantity=0.001
                # Missing price for limit order
            )
            
            result = await order_manager.place_order(no_price_order)
            if result is None:
                print("   ✅ Limit order without price correctly rejected")
            else:
                print("   ⚠️  Limit order without price was not rejected")
            
            # Test 4: Minimum Size Validation
            print("\n🔍 Testing minimum size validation...")
            
            # Get symbol info for BTC-USDT
            symbol_info = await client.get_symbol_info("BTC-USDT")
            if symbol_info:
                min_size = float(symbol_info.get('baseMinSize', 0))
                print(f"   📊 BTC-USDT minimum size: {min_size}")
                
                if min_size > 0:
                    small_order = OrderRequest(
                        symbol="BTC-USDT",
                        side="buy",
                        order_type=OrderType.MARKET,
                        quantity=min_size / 2  # Below minimum
                    )
                    
                    result = await order_manager.place_order(small_order)
                    if result is None:
                        print("   ✅ Below minimum size correctly rejected")
                    else:
                        print("   ⚠️  Below minimum size was not rejected")
            
            print("\n✅ Trading validation tests completed!")
            return True
            
    except Exception as e:
        print(f"❌ Trading validation test failed: {e}")
        return False


async def main():
    """Main test function"""
    try:
        print("🚀 Starting KuCoin Live Trading Integration Test...")
        
        # Test 1: Authentication and Account Access
        auth_success, balances = await test_kucoin_authentication()
        
        if not auth_success:
            print("\n❌ Authentication failed. Cannot proceed with trading tests.")
            print("\n🔧 Please check:")
            print("   1. KUCOIN_API_KEY is correct")
            print("   2. KUCOIN_SECRET_KEY is correct") 
            print("   3. KUCOIN_PASSPHRASE is correct")
            print("   4. API keys have trading permissions")
            return False
        
        # Test 2: Dry Run Trading System
        trading_success = await test_dry_run_trading()
        
        # Test 3: Trading Validation
        validation_success = await test_trading_validation()
        
        # Summary
        total_tests = 3
        passed_tests = sum([auth_success, trading_success, validation_success])
        
        print(f"\n" + "=" * 70)
        print(f"📊 KuCoin Live Trading Test Results: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 All KuCoin live trading tests passed!")
            print("\n✨ Your KuCoin trading system is fully operational!")
            
            print(f"\n💰 Account Summary:")
            if balances:
                for balance_info in balances:
                    currency = balance_info['currency']
                    balance = balance_info['balance']
                    available = balance_info['available']
                    print(f"   {currency}: {balance:.8f} (Available: {available:.8f})")
            
            print(f"\n🎯 Ready for:")
            print("   ✅ Real-time account monitoring")
            print("   ✅ Automated order placement")
            print("   ✅ Portfolio management")
            print("   ✅ Risk management")
            print("   ✅ Trading strategy execution")
            
            print(f"\n⚠️  Important Notes:")
            print("   🧪 System tested in DRY RUN mode for safety")
            print("   🔒 All validations and safety checks working")
            print("   💡 Ready to enable live trading when needed")
            
        else:
            print("⚠️  Some tests failed. Please check the errors above.")
        
        return passed_tests == total_tests
        
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
