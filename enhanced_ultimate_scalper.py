#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
المضارب النهائي المحسن - Enhanced Ultimate Scalper
نظام مضاربة ذكي ومتقدم مع تحسينات شاملة

الميزات المحسنة:
- فلترة العملات القابلة للتداول
- تحويل تلقائي للعملات إلى USDT
- مؤشرات فنية متقدمة (RSI, MACD)
- مضاربة متعددة المستويات
- إدارة مخاطر محسنة
- معالجة أخطاء متقدمة
"""

import requests
import json
import time
import hashlib
import hmac
import base64
import threading
import math
from datetime import datetime, timedelta
from collections import defaultdict, deque
import statistics

# KuCoin API Configuration
API_KEY = "686a4e782301b10001e7457c"
SECRET_KEY = "61718954-dc69-4b89-b21c-dff5b80fff15"
PASSPHRASE = "Eslam*17*3*1999"
BASE_URL = "https://api.kucoin.com"

class TechnicalIndicators:
    """مؤشرات فنية متقدمة"""
    
    @staticmethod
    def calculate_rsi(prices, period=14):
        """حساب مؤشر القوة النسبية RSI"""
        if len(prices) < period + 1:
            return 50  # قيمة افتراضية
        
        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]
        
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    @staticmethod
    def calculate_macd(prices, fast=12, slow=26, signal=9):
        """حساب مؤشر MACD"""
        if len(prices) < slow:
            return 0, 0, 0  # MACD, Signal, Histogram
        
        # حساب المتوسطات المتحركة الأسية
        def ema(data, period):
            multiplier = 2 / (period + 1)
            ema_values = [data[0]]
            for price in data[1:]:
                ema_values.append((price * multiplier) + (ema_values[-1] * (1 - multiplier)))
            return ema_values
        
        ema_fast = ema(prices, fast)
        ema_slow = ema(prices, slow)
        
        macd_line = ema_fast[-1] - ema_slow[-1]
        
        # حساب خط الإشارة (متوسط متحرك للـ MACD)
        if len(prices) >= slow + signal:
            macd_values = [ema_fast[i] - ema_slow[i] for i in range(len(ema_slow))]
            signal_line = ema(macd_values[-signal:], signal)[-1]
            histogram = macd_line - signal_line
            return macd_line, signal_line, histogram
        
        return macd_line, 0, 0

class EnhancedUltimateScalper:
    """المضارب النهائي المحسن"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 10  # زيادة timeout
        self.price_history = defaultdict(lambda: deque(maxlen=100))  # تخزين أكثر كفاءة
        self.volume_history = defaultdict(lambda: deque(maxlen=50))
        self.opportunities = []
        self.hunting = False
        self.tradeable_symbols = set()  # كاش للعملات القابلة للتداول
        self.last_tradeable_check = 0
        self.indicators = TechnicalIndicators()
        
        print("🚀 Enhanced Ultimate Scalper initialized")
        print("🔧 Loading tradeable symbols...")
        self._update_tradeable_symbols()
    
    def _sign_request(self, timestamp, method, endpoint, body=""):
        """توقيع الطلب"""
        message = timestamp + method.upper() + endpoint + body
        signature = base64.b64encode(
            hmac.new(SECRET_KEY.encode(), message.encode(), hashlib.sha256).digest()
        ).decode()
        
        passphrase = base64.b64encode(
            hmac.new(SECRET_KEY.encode(), PASSPHRASE.encode(), hashlib.sha256).digest()
        ).decode()
        
        return {
            "KC-API-KEY": API_KEY,
            "KC-API-SIGN": signature,
            "KC-API-TIMESTAMP": timestamp,
            "KC-API-PASSPHRASE": passphrase,
            "KC-API-KEY-VERSION": "2",
            "Content-Type": "application/json"
        }
    
    def _api_call(self, method, endpoint, data=None, signed=False, retries=3):
        """استدعاء API محسن مع إعادة المحاولة"""
        url = BASE_URL + endpoint
        headers = {}
        
        if signed:
            timestamp = str(int(time.time() * 1000))
            body = json.dumps(data) if data else ""
            headers = self._sign_request(timestamp, method, endpoint, body)
            data = body if body else None
        
        for attempt in range(retries):
            try:
                response = self.session.request(method, url, data=data, headers=headers)
                result = response.json()
                
                if response.status_code == 200 and result.get("code") == "200000":
                    return result.get("data")
                elif response.status_code == 429:  # Rate limit
                    time.sleep(1)
                    continue
                else:
                    return None
            except Exception as e:
                if attempt == retries - 1:
                    print(f"❌ API call failed after {retries} attempts: {e}")
                    return None
                time.sleep(0.5)
        
        return None
    
    def _update_tradeable_symbols(self):
        """تحديث قائمة العملات القابلة للتداول"""
        current_time = time.time()
        
        # تحديث كل 5 دقائق
        if current_time - self.last_tradeable_check < 300:
            return
        
        print("🔄 Updating tradeable symbols...")
        
        # الحصول على جميع الرموز
        symbols_data = self._api_call("GET", "/api/v1/symbols")
        if not symbols_data:
            print("❌ Failed to get symbols data")
            return
        
        tradeable_count = 0
        for symbol_info in symbols_data:
            symbol = symbol_info.get("symbol", "")
            
            # فلترة العملات
            if (symbol.endswith("-USDT") and 
                symbol_info.get("enableTrading", False) and
                not any(stable in symbol for stable in ["USDT", "USDC", "BUSD", "DAI", "TUSD", "FDUSD"])):
                
                self.tradeable_symbols.add(symbol)
                tradeable_count += 1
        
        self.last_tradeable_check = current_time
        print(f"✅ Found {tradeable_count} tradeable symbols")
    
    def get_price(self, symbol):
        """الحصول على السعر مع معالجة أخطاء محسنة"""
        try:
            ticker = self._api_call("GET", f"/api/v1/market/stats?symbol={symbol}")
            if ticker and "last" in ticker:
                price = float(ticker["last"])
                if price > 0:
                    return price
        except (ValueError, TypeError):
            pass
        return None
    
    def get_balance(self, currency="USDT"):
        """الحصول على الرصيد"""
        accounts = self._api_call("GET", "/api/v1/accounts", signed=True)
        if accounts:
            for account in accounts:
                if account["currency"] == currency and account["type"] == "trade":
                    return float(account["available"])
        return 0.0
    
    def get_all_balances(self):
        """الحصول على جميع الأرصدة"""
        accounts = self._api_call("GET", "/api/v1/accounts", signed=True)
        balances = {}
        
        if accounts:
            for account in accounts:
                if account["type"] == "trade" and float(account["available"]) > 0:
                    balances[account["currency"]] = float(account["available"])
        
        return balances
    
    def auto_convert_to_usdt(self, target_amount=20, exclude_currencies=None):
        """تحويل العملات تلقائياً إلى USDT"""
        if exclude_currencies is None:
            exclude_currencies = ["USDT", "BTC", "ETH"]  # عملات لا نريد بيعها
        
        print(f"🔄 Converting assets to USDT (target: ${target_amount})")
        
        balances = self.get_all_balances()
        total_converted = 0
        
        for currency, amount in balances.items():
            if currency in exclude_currencies:
                continue
            
            if total_converted >= target_amount:
                break
            
            symbol = f"{currency}-USDT"
            price = self.get_price(symbol)
            
            if price and price > 0:
                value = amount * price
                
                if value > 1:  # فقط العملات التي تستحق أكثر من $1
                    print(f"💰 Converting {amount:.4f} {currency} (${value:.2f}) to USDT")
                    
                    sell_order = self.sell_market(symbol, amount * 0.99)  # بيع 99% لتجنب مشاكل الرصيد
                    
                    if sell_order:
                        total_converted += value
                        print(f"✅ Converted ${value:.2f} | Total: ${total_converted:.2f}")
                        time.sleep(2)  # انتظار بين الصفقات
        
        print(f"🎯 Total converted: ${total_converted:.2f}")
        return total_converted

    def buy_market(self, symbol, amount):
        """شراء بسعر السوق"""
        data = {
            "clientOid": f"enhanced_buy_{int(time.time())}",
            "symbol": symbol,
            "side": "buy",
            "type": "market",
            "funds": str(amount)
        }

        result = self._api_call("POST", "/api/v1/orders", data=data, signed=True)
        return result.get('orderId') if result else None

    def sell_market(self, symbol, amount):
        """بيع بسعر السوق"""
        data = {
            "clientOid": f"enhanced_sell_{int(time.time())}",
            "symbol": symbol,
            "side": "sell",
            "type": "market",
            "size": str(amount)
        }

        result = self._api_call("POST", "/api/v1/orders", data=data, signed=True)
        return result.get('orderId') if result else None

    def enhanced_analyze_coin(self, symbol, ticker_data):
        """تحليل محسن للعملة مع مؤشرات فنية متقدمة"""
        try:
            current_price = float(ticker_data['last'])
            change_rate = float(ticker_data['changeRate']) * 100
            volume_24h = float(ticker_data['vol'])
            high_24h = float(ticker_data['high'])
            low_24h = float(ticker_data['low'])

            # تحديث التاريخ
            self.price_history[symbol].append(current_price)
            self.volume_history[symbol].append(volume_24h)

            score = 0
            signals = []
            confidence_multiplier = 1.0

            # 1. تحليل حركة السعر (30 نقطة)
            if abs(change_rate) > 15:
                score += 30
                signals.append(f"Extreme move: {change_rate:+.1f}%")
                confidence_multiplier *= 1.3
            elif abs(change_rate) > 8:
                score += 25
                signals.append(f"Strong move: {change_rate:+.1f}%")
                confidence_multiplier *= 1.2
            elif abs(change_rate) > 3:
                score += 15
                signals.append(f"Good move: {change_rate:+.1f}%")
                confidence_multiplier *= 1.1

            # 2. تحليل الحجم (25 نقطة)
            if len(self.volume_history[symbol]) > 3:
                avg_volume = statistics.mean(list(self.volume_history[symbol])[:-1])
                volume_ratio = volume_24h / avg_volume if avg_volume > 0 else 1

                if volume_ratio > 3:
                    score += 25
                    signals.append(f"Volume spike: {volume_ratio:.1f}x")
                elif volume_ratio > 2:
                    score += 20
                    signals.append(f"High volume: {volume_ratio:.1f}x")
                elif volume_ratio > 1.5:
                    score += 15
                    signals.append(f"Increased volume: {volume_ratio:.1f}x")
            else:
                if volume_24h > 1000000:
                    score += 20
                    signals.append("Very high volume")
                elif volume_24h > 100000:
                    score += 15
                    signals.append("High volume")

            # 3. مؤشر RSI (20 نقطة)
            if len(self.price_history[symbol]) >= 15:
                rsi = self.indicators.calculate_rsi(list(self.price_history[symbol]))

                if rsi > 70:
                    score += 10
                    signals.append(f"RSI overbought: {rsi:.1f}")
                elif rsi < 30:
                    score += 15
                    signals.append(f"RSI oversold: {rsi:.1f}")
                elif 40 <= rsi <= 60:
                    score += 20
                    signals.append(f"RSI neutral: {rsi:.1f}")

            # 4. مؤشر MACD (15 نقطة)
            if len(self.price_history[symbol]) >= 26:
                macd, signal_line, histogram = self.indicators.calculate_macd(list(self.price_history[symbol]))

                if histogram > 0 and change_rate > 0:
                    score += 15
                    signals.append("MACD bullish")
                elif histogram < 0 and change_rate < 0:
                    score += 15
                    signals.append("MACD bearish")
                elif abs(histogram) > abs(macd) * 0.1:
                    score += 10
                    signals.append("MACD momentum")

            # 5. تحليل النطاق السعري (10 نقطة)
            if high_24h > 0 and low_24h > 0:
                price_range = ((high_24h - low_24h) / low_24h) * 100
                position = ((current_price - low_24h) / (high_24h - low_24h)) * 100

                if price_range > 12:
                    score += 10
                    signals.append(f"Wide range: {price_range:.1f}%")
                elif price_range > 6:
                    score += 8
                    signals.append(f"Good range: {price_range:.1f}%")

                # موقع السعر في النطاق
                if 20 <= position <= 80:
                    confidence_multiplier *= 1.1

            # تحديد نوع الإشارة
            final_score = min(100, int(score * confidence_multiplier))

            if final_score >= 80:
                signal_type = "VERY_STRONG_BUY" if change_rate > 0 else "VERY_STRONG_SELL"
            elif final_score >= 65:
                signal_type = "STRONG_BUY" if change_rate > 0 else "STRONG_SELL"
            elif final_score >= 45:
                signal_type = "BUY" if change_rate > 0 else "SELL"
            elif final_score >= 25:
                signal_type = "WEAK_BUY" if change_rate > 0 else "WEAK_SELL"
            else:
                signal_type = "HOLD"

            # مستوى المخاطر
            if abs(change_rate) > 10 or final_score >= 80:
                risk_level = "VERY_HIGH"
            elif abs(change_rate) > 6 or final_score >= 60:
                risk_level = "HIGH"
            elif abs(change_rate) > 3 or final_score >= 40:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"

            return {
                'symbol': symbol,
                'signal_type': signal_type,
                'score': final_score,
                'confidence': min(95, final_score),
                'current_price': current_price,
                'change_24h': change_rate,
                'volume_24h': volume_24h,
                'risk_level': risk_level,
                'signals': signals,
                'reasoning': " | ".join(signals[:3]),
                'timestamp': datetime.now().strftime("%H:%M:%S")
            }

        except Exception as e:
            print(f"❌ Analysis error for {symbol}: {e}")
            return None

    def enhanced_market_scan(self):
        """مسح محسن للسوق مع فلترة العملات القابلة للتداول"""
        print("🔍 Enhanced market scanning...")

        # تحديث العملات القابلة للتداول
        self._update_tradeable_symbols()

        # الحصول على جميع البيانات
        all_tickers = self._api_call("GET", "/api/v1/market/allTickers")

        if not all_tickers or "ticker" not in all_tickers:
            print("❌ Failed to get market data")
            return []

        opportunities = []
        processed_count = 0

        for ticker in all_tickers["ticker"]:
            symbol = ticker["symbol"]

            # فلترة العملات القابلة للتداول فقط
            if symbol not in self.tradeable_symbols:
                continue

            processed_count += 1

            # تحليل العملة
            analysis = self.enhanced_analyze_coin(symbol, ticker)
            if analysis and analysis['score'] >= 25:  # حد أدنى للنقاط
                opportunities.append(analysis)

        # ترتيب حسب النقاط
        opportunities.sort(key=lambda x: x['score'], reverse=True)

        print(f"\n📊 ENHANCED MARKET SCAN ({processed_count} tradeable coins analyzed):")
        print("=" * 120)
        print(f"{'#':<3} {'Symbol':<15} {'Signal':<16} {'Score':<5} {'Price':<12} {'Change':<8} {'Volume':<10} {'Risk':<10} {'Reasoning'}")
        print("=" * 120)

        for i, opp in enumerate(opportunities[:25]):  # أفضل 25
            volume_k = opp['volume_24h'] / 1000
            print(f"{i+1:<3} {opp['symbol']:<15} {opp['signal_type']:<16} "
                  f"{opp['score']:<5} ${opp['current_price']:<11.6f} "
                  f"{opp['change_24h']:+7.2f}% {volume_k:>8.0f}K {opp['risk_level']:<10} {opp['reasoning'][:40]}")

        self.opportunities = opportunities
        return opportunities

    def multi_level_scalping(self, symbol, base_amount, analysis=None):
        """مضاربة متعددة المستويات"""
        print(f"⚡ Multi-level scalping {symbol} with ${base_amount}")

        if analysis:
            print(f"📊 Analysis: {analysis['signal_type']} (Score: {analysis['score']}) - {analysis['reasoning']}")

        # الحصول على السعر الحالي
        current_price = self.get_price(symbol)
        if not current_price:
            print("❌ Cannot get current price")
            return False

        # التحقق من الرصيد
        usdt_balance = self.get_balance("USDT")
        if usdt_balance < base_amount:
            print(f"❌ Insufficient USDT: ${usdt_balance:.2f}")
            return False

        # تحديد استراتيجية المضاربة حسب التحليل
        if analysis:
            if analysis['score'] >= 80:
                # مضاربة عدوانية للفرص القوية جداً
                levels = [
                    {"amount": base_amount * 0.4, "target": 0.015, "stop": 0.006, "timeout": 900},   # 1.5% هدف
                    {"amount": base_amount * 0.3, "target": 0.010, "stop": 0.004, "timeout": 600},   # 1.0% هدف
                    {"amount": base_amount * 0.3, "target": 0.006, "stop": 0.003, "timeout": 300}    # 0.6% هدف
                ]
            elif analysis['score'] >= 60:
                # مضاربة متوسطة
                levels = [
                    {"amount": base_amount * 0.5, "target": 0.010, "stop": 0.004, "timeout": 600},
                    {"amount": base_amount * 0.5, "target": 0.006, "stop": 0.003, "timeout": 450}
                ]
            else:
                # مضاربة محافظة
                levels = [
                    {"amount": base_amount, "target": 0.005, "stop": 0.0025, "timeout": 300}
                ]
        else:
            # استراتيجية افتراضية
            levels = [
                {"amount": base_amount, "target": 0.005, "stop": 0.0025, "timeout": 300}
            ]

        successful_levels = 0
        total_profit = 0

        for i, level in enumerate(levels):
            print(f"\n🎯 Level {i+1}: ${level['amount']:.2f} | Target: {level['target']*100:.1f}% | Stop: {level['stop']*100:.1f}%")

            # تنفيذ المضاربة لهذا المستوى
            result = self._execute_scalp_level(symbol, level['amount'], level['target'], level['stop'], level['timeout'])

            if result['success']:
                successful_levels += 1
                total_profit += result['profit']
                print(f"✅ Level {i+1} successful! Profit: ${result['profit']:.3f}")
            else:
                print(f"❌ Level {i+1} failed. Loss: ${abs(result['profit']):.3f}")

                # إيقاف المضاربة إذا فشل مستوى
                if result['profit'] < -level['amount'] * 0.01:  # خسارة أكثر من 1%
                    print("🛑 Stopping multi-level scalping due to significant loss")
                    break

            # انتظار بين المستويات
            if i < len(levels) - 1:
                time.sleep(10)

        print(f"\n📊 Multi-level scalping completed:")
        print(f"   Successful levels: {successful_levels}/{len(levels)}")
        print(f"   Total profit: ${total_profit:.3f}")
        print(f"   Success rate: {(successful_levels/len(levels))*100:.1f}%")

        return successful_levels > 0

    def _execute_scalp_level(self, symbol, amount, profit_target, stop_loss, timeout):
        """تنفيذ مستوى مضاربة واحد"""
        start_time = time.time()

        # الحصول على السعر الحالي
        entry_price = self.get_price(symbol)
        if not entry_price:
            return {"success": False, "profit": 0, "reason": "Cannot get price"}

        # تنفيذ أمر الشراء
        buy_order = self.buy_market(symbol, amount)
        if not buy_order:
            return {"success": False, "profit": 0, "reason": "Buy order failed"}

        print(f"✅ Position opened at ${entry_price:.6f} | Order: {buy_order}")

        # انتظار تنفيذ الأمر
        time.sleep(3)

        # حساب كمية العملة المشتراة
        crypto_amount = amount / entry_price

        # تحديد الأهداف
        target_price = entry_price * (1 + profit_target)
        stop_price = entry_price * (1 - stop_loss)

        print(f"🎯 Target: ${target_price:.6f} | Stop: ${stop_price:.6f}")

        # متغيرات التتبع
        best_price = entry_price
        trailing_stop = stop_price

        while time.time() - start_time < timeout:
            current_price = self.get_price(symbol)
            if not current_price:
                time.sleep(2)
                continue

            # تحديث أفضل سعر و trailing stop
            if current_price > best_price:
                best_price = current_price
                # تحديث trailing stop
                new_trailing = best_price * (1 - stop_loss)
                if new_trailing > trailing_stop:
                    trailing_stop = new_trailing

            # عرض التقدم
            profit_pct = ((current_price - entry_price) / entry_price) * 100
            elapsed = time.time() - start_time
            progress = (elapsed / timeout) * 100

            print(f"📊 ${current_price:.6f} | P&L: {profit_pct:+.2f}% | Best: ${best_price:.6f} | Trail: ${trailing_stop:.6f} | {progress:.0f}%", end="\r")

            # فحص شروط الخروج
            if current_price >= target_price:
                print(f"\n🎉 TARGET HIT! Selling at ${current_price:.6f}")
                sell_order = self.sell_market(symbol, crypto_amount * 0.995)
                if sell_order:
                    profit = (current_price - entry_price) * crypto_amount
                    return {"success": True, "profit": profit, "reason": "Target achieved"}
                break

            elif current_price <= trailing_stop:
                print(f"\n🛑 TRAILING STOP! Selling at ${current_price:.6f}")
                sell_order = self.sell_market(symbol, crypto_amount * 0.995)
                if sell_order:
                    profit = (current_price - entry_price) * crypto_amount
                    return {"success": profit > 0, "profit": profit, "reason": "Trailing stop"}
                break

            time.sleep(3)  # فحص كل 3 ثوان

        # انتهاء الوقت المحدد
        print(f"\n⏰ Timeout! Selling at market...")
        final_price = self.get_price(symbol)
        sell_order = self.sell_market(symbol, crypto_amount * 0.995)

        if sell_order and final_price:
            profit = (final_price - entry_price) * crypto_amount
            return {"success": profit > 0, "profit": profit, "reason": "Timeout"}

        return {"success": False, "profit": 0, "reason": "Failed to sell"}

    def smart_auto_hunt(self, amount_per_trade=15, max_trades=5):
        """صيد ذكي تلقائي مع إدارة مخاطر"""
        print(f"🎯 Starting smart auto hunt | ${amount_per_trade} per trade | Max {max_trades} trades")

        self.hunting = True
        trades_executed = 0
        successful_trades = 0
        total_profit = 0

        while self.hunting and trades_executed < max_trades:
            try:
                # مسح السوق
                opportunities = self.enhanced_market_scan()

                # فلترة الفرص عالية الجودة
                high_quality_opps = [opp for opp in opportunities if opp['score'] >= 70]

                if high_quality_opps:
                    best_opp = high_quality_opps[0]
                    print(f"\n🚀 Auto-trading: {best_opp['symbol']} (Score: {best_opp['score']})")

                    # تنفيذ المضاربة متعددة المستويات
                    success = self.multi_level_scalping(best_opp['symbol'], amount_per_trade, best_opp)

                    trades_executed += 1

                    if success:
                        successful_trades += 1
                        print(f"✅ Auto-trade #{trades_executed} successful!")
                    else:
                        print(f"❌ Auto-trade #{trades_executed} failed")

                    # انتظار بين الصفقات
                    if self.hunting and trades_executed < max_trades:
                        print("⏳ Waiting before next trade...")
                        time.sleep(60)  # انتظار دقيقة

                else:
                    print("⏳ No high-quality opportunities found. Waiting...")
                    time.sleep(30)  # انتظار 30 ثانية

            except KeyboardInterrupt:
                self.hunting = False
                break
            except Exception as e:
                print(f"❌ Auto-hunt error: {e}")
                time.sleep(30)

        # إحصائيات نهائية
        success_rate = (successful_trades / trades_executed * 100) if trades_executed > 0 else 0
        print(f"\n📊 Auto-hunt completed:")
        print(f"   Trades executed: {trades_executed}")
        print(f"   Successful trades: {successful_trades}")
        print(f"   Success rate: {success_rate:.1f}%")
        print(f"   Total profit: ${total_profit:.2f}")

        self.hunting = False

    def stop_hunting(self):
        """إيقاف الصيد"""
        self.hunting = False
        print("🛑 Hunting stopped")

def main():
    """الواجهة الرئيسية المحسنة"""
    scalper = EnhancedUltimateScalper()

    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🚀 المضارب النهائي المحسن                            ║
    ║           Enhanced Ultimate Scalper v2.0                    ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝

    🔧 التحسينات الجديدة:
    ✅ فلترة العملات القابلة للتداول فقط
    ✅ تحويل تلقائي للعملات إلى USDT
    ✅ مؤشرات فنية متقدمة (RSI, MACD)
    ✅ مضاربة متعددة المستويات
    ✅ إدارة مخاطر محسنة
    ✅ معالجة أخطاء متقدمة

    الأوامر المحسنة:
    1. scan                     - مسح محسن للأسواق
    2. convert <AMOUNT>         - تحويل العملات إلى USDT
    3. scalp <SYMBOL> <AMOUNT>  - مضاربة متعددة المستويات
    4. hunt <AMOUNT>            - صيد ذكي تلقائي
    5. balance <CURRENCY>       - فحص الرصيد
    6. balances                 - جميع الأرصدة
    7. price <SYMBOL>           - سعر العملة
    8. stop                     - إيقاف الصيد
    9. quit                     - خروج

    أمثلة محسنة:
    convert 20           - تحويل العملات للحصول على $20 USDT
    scalp BTC-USDT 15    - مضاربة متعددة المستويات
    hunt 10              - صيد تلقائي بـ $10 لكل صفقة
    """)

    while True:
        try:
            command = input("\n🚀 Enhanced Command: ").strip().split()

            if not command:
                continue

            cmd = command[0].lower()

            if cmd == "quit":
                scalper.stop_hunting()
                print("👋 Enhanced Ultimate Scalper stopped!")
                break

            elif cmd == "scan":
                scalper.enhanced_market_scan()

            elif cmd == "convert":
                amount = float(command[1]) if len(command) > 1 else 20
                scalper.auto_convert_to_usdt(amount)

            elif cmd == "scalp":
                symbol = command[1].upper() if len(command) > 1 else "BTC-USDT"
                amount = float(command[2]) if len(command) > 2 else 10

                # الحصول على التحليل أولاً
                ticker = scalper._api_call("GET", f"/api/v1/market/stats?symbol={symbol}")
                analysis = None
                if ticker:
                    analysis = scalper.enhanced_analyze_coin(symbol, ticker)

                scalper.multi_level_scalping(symbol, amount, analysis)

            elif cmd == "hunt":
                amount = float(command[1]) if len(command) > 1 else 15
                max_trades = int(command[2]) if len(command) > 2 else 5

                hunting_thread = threading.Thread(
                    target=scalper.smart_auto_hunt,
                    args=(amount, max_trades),
                    daemon=True
                )
                hunting_thread.start()
                print(f"🎯 Smart auto-hunting started: ${amount} per trade, max {max_trades} trades")

            elif cmd == "balance":
                currency = command[1].upper() if len(command) > 1 else "USDT"
                balance = scalper.get_balance(currency)
                print(f"💰 {currency}: {balance}")

            elif cmd == "balances":
                balances = scalper.get_all_balances()
                print("\n💰 All Balances:")
                total_value = 0

                for currency, amount in balances.items():
                    if currency == "USDT":
                        value = amount
                    else:
                        price = scalper.get_price(f"{currency}-USDT")
                        value = amount * price if price else 0

                    total_value += value
                    print(f"   {currency}: {amount:.6f} (${value:.2f})")

                print(f"   Total Value: ${total_value:.2f}")

            elif cmd == "price":
                symbol = command[1].upper() if len(command) > 1 else "BTC-USDT"
                price = scalper.get_price(symbol)
                if price:
                    print(f"💰 {symbol}: ${price}")
                else:
                    print(f"❌ Cannot get price for {symbol}")

            elif cmd == "stop":
                scalper.stop_hunting()

            else:
                print("❌ Invalid command. Type 'quit' to exit.")

        except KeyboardInterrupt:
            scalper.stop_hunting()
            print("\n👋 Enhanced Ultimate Scalper stopped!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
