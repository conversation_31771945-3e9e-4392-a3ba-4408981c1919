#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
المضارب النهائي المحسن - Enhanced Ultimate Scalper
نظام مضاربة ذكي ومتقدم مع تحسينات شاملة

الميزات المحسنة:
- فلترة العملات القابلة للتداول
- تحويل تلقائي للعملات إلى USDT
- مؤشرات فنية متقدمة (RSI, MACD)
- مضاربة متعددة المستويات
- إدارة مخاطر محسنة
- معالجة أخطاء متقدمة
"""

import requests
import json
import time
import hashlib
import hmac
import base64
import threading
from datetime import datetime
from collections import defaultdict, deque
import statistics

# KuCoin API Configuration
API_KEY = "686a4e782301b10001e7457c"
SECRET_KEY = "61718954-dc69-4b89-b21c-dff5b80fff15"
PASSPHRASE = "Eslam*17*3*1999"
BASE_URL = "https://api.kucoin.com"

class AdvancedTechnicalIndicators:
    """مؤشرات فنية متقدمة ومخصصة"""

    @staticmethod
    def calculate_rsi(prices, period=14):
        """حساب مؤشر القوة النسبية RSI التقليدي"""
        if len(prices) < period + 1:
            return 50

        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]

        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period

        if avg_loss == 0:
            return 100

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    @staticmethod
    def calculate_volume_weighted_rsi(prices, volumes, period=14):
        """مؤشر RSI مرجح بالحجم - مؤشر مخصص"""
        if len(prices) < period + 1 or len(volumes) < period + 1:
            return 50

        weighted_changes = []
        for i in range(1, len(prices)):
            price_change = prices[i] - prices[i-1]
            volume_weight = volumes[i] / max(volumes[max(0, i-period):i+1])
            weighted_changes.append(price_change * volume_weight)

        gains = [d if d > 0 else 0 for d in weighted_changes]
        losses = [-d if d < 0 else 0 for d in weighted_changes]

        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period

        if avg_loss == 0:
            return 100

        rs = avg_gain / avg_loss
        vw_rsi = 100 - (100 / (1 + rs))
        return vw_rsi

    @staticmethod
    def calculate_adaptive_macd(prices, volatility_factor=1.0):
        """مؤشر MACD متكيف مع التقلبات"""
        if len(prices) < 26:
            return 0, 0, 0

        # تكييف الفترات حسب التقلبات
        base_fast = 12
        base_slow = 26
        base_signal = 9

        # زيادة الفترات في الأسواق المتقلبة
        fast = max(8, int(base_fast / volatility_factor))
        slow = max(20, int(base_slow / volatility_factor))
        signal = max(6, int(base_signal / volatility_factor))

        def ema(data, period):
            multiplier = 2 / (period + 1)
            ema_values = [data[0]]
            for price in data[1:]:
                ema_values.append((price * multiplier) + (ema_values[-1] * (1 - multiplier)))
            return ema_values

        ema_fast = ema(prices, fast)
        ema_slow = ema(prices, slow)

        macd_line = ema_fast[-1] - ema_slow[-1]

        if len(prices) >= slow + signal:
            macd_values = [ema_fast[i] - ema_slow[i] for i in range(len(ema_slow))]
            signal_line = ema(macd_values[-signal:], signal)[-1]
            histogram = macd_line - signal_line
            return macd_line, signal_line, histogram

        return macd_line, 0, 0

    @staticmethod
    def calculate_smart_momentum(prices, volumes, period=20):
        """مؤشر الزخم الذكي - مؤشر مخصص يجمع السعر والحجم"""
        if len(prices) < period or len(volumes) < period:
            return 0

        recent_prices = prices[-period:]
        recent_volumes = volumes[-period:]

        # حساب قوة الاتجاه
        price_momentum = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]

        # حساب متوسط الحجم المرجح
        avg_volume = sum(recent_volumes) / len(recent_volumes)
        volume_strength = recent_volumes[-1] / avg_volume if avg_volume > 0 else 1

        # حساب الاستقرار (عكس التقلبات)
        price_changes = [abs(recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1]
                        for i in range(1, len(recent_prices))]
        volatility = sum(price_changes) / len(price_changes)
        stability = 1 / (1 + volatility * 100)

        # دمج المؤشرات
        smart_momentum = (price_momentum * 100) * volume_strength * stability

        # تطبيع النتيجة بين -100 و +100
        return max(-100, min(100, smart_momentum * 10))

    @staticmethod
    def calculate_market_sentiment(prices, volumes, high_prices, low_prices, period=15):
        """مؤشر مشاعر السوق - مؤشر مخصص"""
        if len(prices) < period:
            return 50

        recent_data = {
            'prices': prices[-period:],
            'volumes': volumes[-period:] if len(volumes) >= period else [1] * period,
            'highs': high_prices[-period:] if len(high_prices) >= period else prices[-period:],
            'lows': low_prices[-period:] if len(low_prices) >= period else prices[-period:]
        }

        sentiment_score = 0

        # 1. تحليل اتجاه السعر (30 نقطة)
        price_trend = (recent_data['prices'][-1] - recent_data['prices'][0]) / recent_data['prices'][0]
        sentiment_score += price_trend * 3000  # تحويل إلى نقاط

        # 2. تحليل قوة الحجم (25 نقطة)
        if len(recent_data['volumes']) > 5:
            recent_volume_avg = sum(recent_data['volumes'][-5:]) / 5
            older_volume_avg = sum(recent_data['volumes'][-10:-5]) / 5 if len(recent_data['volumes']) >= 10 else recent_volume_avg
            volume_change = (recent_volume_avg - older_volume_avg) / older_volume_avg if older_volume_avg > 0 else 0
            sentiment_score += volume_change * 2500

        # 3. تحليل موقع السعر في النطاق (20 نقطة)
        current_price = recent_data['prices'][-1]
        period_high = max(recent_data['highs'])
        period_low = min(recent_data['lows'])

        if period_high > period_low:
            price_position = (current_price - period_low) / (period_high - period_low)
            sentiment_score += (price_position - 0.5) * 4000  # -20 إلى +20

        # 4. تحليل الاستمرارية (15 نقطة)
        consecutive_moves = 0
        last_direction = None
        for i in range(1, len(recent_data['prices'])):
            current_direction = 1 if recent_data['prices'][i] > recent_data['prices'][i-1] else -1
            if last_direction == current_direction:
                consecutive_moves += 1
            else:
                consecutive_moves = 0
            last_direction = current_direction

        sentiment_score += consecutive_moves * 300

        # 5. تحليل التقلبات (10 نقاط)
        price_changes = [abs(recent_data['prices'][i] - recent_data['prices'][i-1]) / recent_data['prices'][i-1]
                        for i in range(1, len(recent_data['prices']))]
        avg_volatility = sum(price_changes) / len(price_changes)

        # التقلبات المعتدلة إيجابية، الشديدة سلبية
        if 0.01 <= avg_volatility <= 0.05:  # 1-5% تقلبات معتدلة
            sentiment_score += 1000
        elif avg_volatility > 0.1:  # أكثر من 10% تقلبات شديدة
            sentiment_score -= 1000

        # تطبيع النتيجة بين 0-100
        normalized_sentiment = 50 + (sentiment_score / 200)
        return max(0, min(100, normalized_sentiment))

    @staticmethod
    def calculate_volatility_breakout(prices, volumes, period=20, breakout_threshold=2.0):
        """مؤشر اختراق التقلبات - مؤشر مخصص"""
        if len(prices) < period:
            return 0, "NONE"

        recent_prices = prices[-period:]
        recent_volumes = volumes[-period:] if len(volumes) >= period else [1] * period

        # حساب التقلبات التاريخية
        price_changes = [abs(recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1]
                        for i in range(1, len(recent_prices))]
        avg_volatility = sum(price_changes) / len(price_changes)
        std_volatility = statistics.stdev(price_changes) if len(price_changes) > 1 else 0

        # حساب التقلبات الحالية
        current_change = abs(recent_prices[-1] - recent_prices[-2]) / recent_prices[-2]

        # حساب قوة الاختراق
        if std_volatility > 0:
            breakout_strength = (current_change - avg_volatility) / std_volatility
        else:
            breakout_strength = 0

        # تحديد نوع الاختراق
        breakout_type = "NONE"
        if breakout_strength > breakout_threshold:
            if recent_prices[-1] > recent_prices[-2]:
                breakout_type = "BULLISH_BREAKOUT"
            else:
                breakout_type = "BEARISH_BREAKOUT"
        elif breakout_strength > breakout_threshold * 0.5:
            breakout_type = "POTENTIAL_BREAKOUT"

        # حساب قوة الحجم المصاحبة
        if len(recent_volumes) >= 5:
            volume_avg = sum(recent_volumes[-5:]) / 5
            current_volume = recent_volumes[-1]
            volume_confirmation = current_volume / volume_avg if volume_avg > 0 else 1

            # تعديل قوة الاختراق بناء على الحجم
            breakout_strength *= min(2.0, volume_confirmation)

        return breakout_strength, breakout_type

    @staticmethod
    def calculate_dynamic_bollinger_bands(prices, period=20, std_multiplier=2.0):
        """نطاقات بولينجر ديناميكية تتكيف مع ظروف السوق"""
        if len(prices) < period:
            return prices[-1], prices[-1], prices[-1], 0

        recent_prices = prices[-period:]

        # حساب المتوسط المتحرك
        sma = sum(recent_prices) / len(recent_prices)

        # حساب الانحراف المعياري
        variance = sum((price - sma) ** 2 for price in recent_prices) / len(recent_prices)
        std_dev = variance ** 0.5

        # تكييف مضاعف الانحراف المعياري حسب التقلبات
        price_changes = [abs(recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1]
                        for i in range(1, len(recent_prices))]
        avg_volatility = sum(price_changes) / len(price_changes)

        # زيادة النطاق في الأسواق المتقلبة
        if avg_volatility > 0.05:  # تقلبات عالية
            dynamic_multiplier = std_multiplier * 1.5
        elif avg_volatility < 0.01:  # تقلبات منخفضة
            dynamic_multiplier = std_multiplier * 0.7
        else:
            dynamic_multiplier = std_multiplier

        # حساب النطاقات
        upper_band = sma + (std_dev * dynamic_multiplier)
        lower_band = sma - (std_dev * dynamic_multiplier)

        # حساب موقع السعر الحالي
        current_price = prices[-1]
        if upper_band > lower_band:
            bb_position = (current_price - lower_band) / (upper_band - lower_band)
        else:
            bb_position = 0.5

        return upper_band, sma, lower_band, bb_position

    @staticmethod
    def calculate_support_resistance(prices, period=50, min_touches=3):
        """نظام تحديد الدعم والمقاومة الذكي"""
        if len(prices) < period:
            return [], []

        recent_prices = prices[-period:]

        # العثور على القمم والقيعان المحلية
        peaks = []
        valleys = []

        for i in range(2, len(recent_prices) - 2):
            # قمة محلية
            if (recent_prices[i] > recent_prices[i-1] and
                recent_prices[i] > recent_prices[i+1] and
                recent_prices[i] > recent_prices[i-2] and
                recent_prices[i] > recent_prices[i+2]):
                peaks.append(recent_prices[i])

            # قاع محلي
            if (recent_prices[i] < recent_prices[i-1] and
                recent_prices[i] < recent_prices[i+1] and
                recent_prices[i] < recent_prices[i-2] and
                recent_prices[i] < recent_prices[i+2]):
                valleys.append(recent_prices[i])

        # تجميع المستويات المتقاربة
        def cluster_levels(levels, tolerance=0.02):
            if not levels:
                return []

            levels.sort()
            clusters = []
            current_cluster = [levels[0]]

            for level in levels[1:]:
                if abs(level - current_cluster[-1]) / current_cluster[-1] <= tolerance:
                    current_cluster.append(level)
                else:
                    if len(current_cluster) >= min_touches:
                        clusters.append(sum(current_cluster) / len(current_cluster))
                    current_cluster = [level]

            if len(current_cluster) >= min_touches:
                clusters.append(sum(current_cluster) / len(current_cluster))

            return clusters

        resistance_levels = cluster_levels(peaks)
        support_levels = cluster_levels(valleys)

        return resistance_levels, support_levels

class AdvancedAnalysisEngine:
    """محرك التحليل المتقدم الشامل"""

    def __init__(self):
        self.indicators = AdvancedTechnicalIndicators()
        self.historical_data = defaultdict(lambda: {
            'prices': deque(maxlen=200),
            'volumes': deque(maxlen=200),
            'highs': deque(maxlen=200),
            'lows': deque(maxlen=200),
            'timestamps': deque(maxlen=200)
        })
        self.analysis_cache = {}
        self.strategy_performance = defaultdict(lambda: {
            'total_trades': 0,
            'successful_trades': 0,
            'total_profit': 0.0,
            'avg_profit': 0.0,
            'max_profit': 0.0,
            'max_loss': 0.0,
            'success_rate': 0.0
        })

    def update_historical_data(self, symbol, price, volume, high, low):
        """تحديث البيانات التاريخية"""
        data = self.historical_data[symbol]
        data['prices'].append(price)
        data['volumes'].append(volume)
        data['highs'].append(high)
        data['lows'].append(low)
        data['timestamps'].append(time.time())

    def comprehensive_analysis(self, symbol, ticker_data):
        """تحليل شامل متعدد الطبقات"""
        try:
            current_price = float(ticker_data['last'])
            change_rate = float(ticker_data['changeRate']) * 100
            volume_24h = float(ticker_data['vol'])
            high_24h = float(ticker_data['high'])
            low_24h = float(ticker_data['low'])

            # تحديث البيانات التاريخية
            self.update_historical_data(symbol, current_price, volume_24h, high_24h, low_24h)

            data = self.historical_data[symbol]
            prices = list(data['prices'])
            volumes = list(data['volumes'])
            highs = list(data['highs'])
            lows = list(data['lows'])

            if len(prices) < 20:
                return self._basic_analysis(symbol, ticker_data)

            # حساب جميع المؤشرات المتقدمة
            analysis_results = {}

            # 1. المؤشرات التقليدية المحسنة
            rsi = self.indicators.calculate_rsi(prices)
            vw_rsi = self.indicators.calculate_volume_weighted_rsi(prices, volumes)

            volatility = self._calculate_volatility(prices)
            adaptive_macd = self.indicators.calculate_adaptive_macd(prices, volatility)

            analysis_results['traditional'] = {
                'rsi': rsi,
                'volume_weighted_rsi': vw_rsi,
                'adaptive_macd': adaptive_macd,
                'volatility': volatility
            }

            # 2. المؤشرات المخصصة الجديدة
            smart_momentum = self.indicators.calculate_smart_momentum(prices, volumes)
            market_sentiment = self.indicators.calculate_market_sentiment(prices, volumes, highs, lows)
            breakout_strength, breakout_type = self.indicators.calculate_volatility_breakout(prices, volumes)

            analysis_results['custom'] = {
                'smart_momentum': smart_momentum,
                'market_sentiment': market_sentiment,
                'breakout_strength': breakout_strength,
                'breakout_type': breakout_type
            }

            # 3. تحليل الدعم والمقاومة
            resistance_levels, support_levels = self.indicators.calculate_support_resistance(prices)
            bb_upper, bb_middle, bb_lower, bb_position = self.indicators.calculate_dynamic_bollinger_bands(prices)

            analysis_results['levels'] = {
                'resistance': resistance_levels,
                'support': support_levels,
                'bb_upper': bb_upper,
                'bb_middle': bb_middle,
                'bb_lower': bb_lower,
                'bb_position': bb_position
            }

            # 4. تحليل متعدد الإطارات الزمنية
            multi_timeframe = self._multi_timeframe_analysis(prices, volumes)
            analysis_results['timeframes'] = multi_timeframe

            # 5. حساب النقاط الشاملة (0-100)
            comprehensive_score = self._calculate_comprehensive_score(analysis_results, change_rate, volume_24h)

            # 6. تصنيف المخاطر الذكي
            risk_classification = self._classify_risk(analysis_results, volatility, volume_24h)

            # 7. تحديد أفضل استراتيجية
            best_strategy = self._determine_best_strategy(analysis_results, symbol)

            # 8. حساب احتمالية النجاح
            success_probability = self._calculate_success_probability(analysis_results, symbol)

            # تجميع النتائج النهائية
            final_analysis = {
                'symbol': symbol,
                'timestamp': datetime.now().strftime("%H:%M:%S"),
                'current_price': current_price,
                'change_24h': change_rate,
                'volume_24h': volume_24h,

                # النتائج الأساسية
                'comprehensive_score': comprehensive_score,
                'risk_classification': risk_classification,
                'success_probability': success_probability,
                'best_strategy': best_strategy,

                # تفاصيل المؤشرات
                'indicators': analysis_results,

                # توصيات التداول
                'signal_type': self._determine_signal_type(comprehensive_score, risk_classification),
                'entry_points': self._calculate_entry_points(current_price, analysis_results),
                'exit_points': self._calculate_exit_points(current_price, analysis_results),
                'position_size': self._calculate_optimal_position_size(risk_classification, success_probability),

                # معلومات إضافية
                'market_phase': self._identify_market_phase(analysis_results),
                'trend_strength': self._calculate_trend_strength(analysis_results),
                'momentum_direction': 'BULLISH' if smart_momentum > 0 else 'BEARISH',
                'volatility_level': 'HIGH' if volatility > 0.05 else 'MEDIUM' if volatility > 0.02 else 'LOW'
            }

            # حفظ في الكاش
            self.analysis_cache[symbol] = final_analysis

            return final_analysis

        except Exception as e:
            print(f"❌ Advanced analysis error for {symbol}: {e}")
            return self._basic_analysis(symbol, ticker_data)

    def _calculate_volatility(self, prices, period=20):
        """حساب التقلبات"""
        if len(prices) < period:
            return 0.02

        recent_prices = prices[-period:]
        price_changes = [abs(recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1]
                        for i in range(1, len(recent_prices))]
        return sum(price_changes) / len(price_changes)

    def _multi_timeframe_analysis(self, prices, volumes):
        """تحليل متعدد الإطارات الزمنية"""
        timeframes = {
            'short': prices[-10:] if len(prices) >= 10 else prices,
            'medium': prices[-30:] if len(prices) >= 30 else prices,
            'long': prices[-60:] if len(prices) >= 60 else prices
        }

        analysis = {}
        for tf_name, tf_prices in timeframes.items():
            if len(tf_prices) >= 5:
                trend = (tf_prices[-1] - tf_prices[0]) / tf_prices[0]
                rsi = self.indicators.calculate_rsi(tf_prices, min(14, len(tf_prices)//2))

                analysis[tf_name] = {
                    'trend': trend,
                    'rsi': rsi,
                    'direction': 'BULLISH' if trend > 0 else 'BEARISH',
                    'strength': abs(trend) * 100
                }

        return analysis

    def _calculate_comprehensive_score(self, analysis_results, change_rate, volume_24h):
        """حساب النقاط الشاملة (0-100)"""
        score = 0

        # 1. المؤشرات التقليدية (25 نقطة)
        traditional = analysis_results.get('traditional', {})
        rsi = traditional.get('rsi', 50)
        vw_rsi = traditional.get('volume_weighted_rsi', 50)

        # RSI التقليدي
        if 30 <= rsi <= 70:
            score += 10
        elif rsi < 30 or rsi > 70:
            score += 15  # مناطق ذروة البيع/الشراء

        # RSI المرجح بالحجم
        if abs(vw_rsi - rsi) < 10:  # تأكيد بين المؤشرين
            score += 10

        # 2. المؤشرات المخصصة (30 نقطة)
        custom = analysis_results.get('custom', {})
        smart_momentum = custom.get('smart_momentum', 0)
        market_sentiment = custom.get('market_sentiment', 50)
        breakout_strength = custom.get('breakout_strength', 0)

        # الزخم الذكي
        if abs(smart_momentum) > 20:
            score += 15
        elif abs(smart_momentum) > 10:
            score += 10

        # مشاعر السوق
        if market_sentiment > 70 or market_sentiment < 30:
            score += 10
        elif 40 <= market_sentiment <= 60:
            score += 5

        # قوة الاختراق
        if breakout_strength > 2:
            score += 15
        elif breakout_strength > 1:
            score += 10

        # 3. تحليل الدعم والمقاومة (20 نقطة)
        levels = analysis_results.get('levels', {})
        bb_position = levels.get('bb_position', 0.5)

        # موقع البولينجر باند
        if bb_position > 0.8 or bb_position < 0.2:
            score += 15  # قرب الحدود
        elif 0.3 <= bb_position <= 0.7:
            score += 10  # منطقة آمنة

        # 4. التحليل متعدد الإطارات (15 نقطة)
        timeframes = analysis_results.get('timeframes', {})
        alignment_score = 0

        for tf_data in timeframes.values():
            if tf_data.get('strength', 0) > 2:  # اتجاه قوي
                alignment_score += 1

        if alignment_score >= 2:  # اتفاق إطارين زمنيين على الأقل
            score += 15
        elif alignment_score == 1:
            score += 10

        # 5. حركة السعر والحجم (10 نقطة)
        if abs(change_rate) > 5:
            score += 10
        elif abs(change_rate) > 2:
            score += 5

        return min(100, score)

    def _classify_risk(self, analysis_results, volatility, volume_24h):
        """تصنيف المخاطر الذكي"""
        risk_factors = []
        risk_score = 0

        # عوامل التقلبات
        if volatility > 0.1:
            risk_score += 30
            risk_factors.append("High volatility")
        elif volatility > 0.05:
            risk_score += 20
            risk_factors.append("Medium volatility")

        # عوامل الحجم
        if volume_24h < 10000:
            risk_score += 25
            risk_factors.append("Low volume")
        elif volume_24h < 100000:
            risk_score += 15
            risk_factors.append("Medium volume")

        # عوامل المؤشرات
        custom = analysis_results.get('custom', {})
        breakout_strength = custom.get('breakout_strength', 0)

        if breakout_strength > 3:
            risk_score += 20
            risk_factors.append("Extreme breakout")

        # تصنيف المخاطر
        if risk_score >= 60:
            return {"level": "EXTREME", "score": risk_score, "factors": risk_factors}
        elif risk_score >= 40:
            return {"level": "HIGH", "score": risk_score, "factors": risk_factors}
        elif risk_score >= 20:
            return {"level": "MEDIUM", "score": risk_score, "factors": risk_factors}
        else:
            return {"level": "LOW", "score": risk_score, "factors": risk_factors}

    def _determine_best_strategy(self, analysis_results, symbol):
        """تحديد أفضل استراتيجية"""
        custom = analysis_results.get('custom', {})
        traditional = analysis_results.get('traditional', {})

        smart_momentum = custom.get('smart_momentum', 0)
        market_sentiment = custom.get('market_sentiment', 50)
        breakout_type = custom.get('breakout_type', 'NONE')
        volatility = traditional.get('volatility', 0.02)

        # استراتيجية الاختراق
        if breakout_type in ['BULLISH_BREAKOUT', 'BEARISH_BREAKOUT']:
            return {
                'name': 'BREAKOUT_STRATEGY',
                'confidence': 85,
                'description': f'{breakout_type} detected with strong momentum'
            }

        # استراتيجية الزخم
        elif abs(smart_momentum) > 30:
            return {
                'name': 'MOMENTUM_STRATEGY',
                'confidence': 80,
                'description': f'Strong momentum: {smart_momentum:.1f}'
            }

        # استراتيجية المشاعر
        elif market_sentiment > 75 or market_sentiment < 25:
            return {
                'name': 'SENTIMENT_STRATEGY',
                'confidence': 70,
                'description': f'Extreme sentiment: {market_sentiment:.1f}'
            }

        # استراتيجية التقلبات المنخفضة
        elif volatility < 0.01:
            return {
                'name': 'LOW_VOLATILITY_STRATEGY',
                'confidence': 60,
                'description': 'Low volatility environment'
            }

        # استراتيجية افتراضية
        else:
            return {
                'name': 'BALANCED_STRATEGY',
                'confidence': 50,
                'description': 'Balanced market conditions'
            }

    def _calculate_success_probability(self, analysis_results, symbol):
        """حساب احتمالية النجاح"""
        # الحصول على الأداء التاريخي
        performance = self.strategy_performance.get(symbol, {})
        historical_success = performance.get('success_rate', 50)

        # عوامل التحليل الحالي
        custom = analysis_results.get('custom', {})
        levels = analysis_results.get('levels', {})

        smart_momentum = abs(custom.get('smart_momentum', 0))
        market_sentiment = custom.get('market_sentiment', 50)
        breakout_strength = custom.get('breakout_strength', 0)
        bb_position = levels.get('bb_position', 0.5)

        # حساب احتمالية بناء على المؤشرات
        technical_probability = 50

        # تأثير الزخم
        if smart_momentum > 30:
            technical_probability += 20
        elif smart_momentum > 15:
            technical_probability += 10

        # تأثير المشاعر
        if market_sentiment > 70 or market_sentiment < 30:
            technical_probability += 15

        # تأثير الاختراق
        if breakout_strength > 2:
            technical_probability += 20
        elif breakout_strength > 1:
            technical_probability += 10

        # تأثير موقع البولينجر
        if 0.2 <= bb_position <= 0.8:
            technical_probability += 10

        # دمج الاحتمالية التاريخية والتقنية
        final_probability = (historical_success * 0.3) + (technical_probability * 0.7)

        return min(95, max(5, final_probability))

    def _basic_analysis(self, symbol, ticker_data):
        """تحليل أساسي للعملات الجديدة"""
        current_price = float(ticker_data['last'])
        change_rate = float(ticker_data['changeRate']) * 100
        volume_24h = float(ticker_data['vol'])

        score = 50  # نقطة بداية

        if abs(change_rate) > 5:
            score += 20
        elif abs(change_rate) > 2:
            score += 10

        if volume_24h > 100000:
            score += 15
        elif volume_24h > 10000:
            score += 10

        return {
            'symbol': symbol,
            'comprehensive_score': score,
            'risk_classification': {'level': 'MEDIUM', 'score': 30, 'factors': ['Limited data']},
            'success_probability': 50,
            'signal_type': 'HOLD',
            'current_price': current_price,
            'change_24h': change_rate,
            'volume_24h': volume_24h
        }

    def _determine_signal_type(self, comprehensive_score, risk_classification):
        """تحديد نوع الإشارة"""
        risk_level = risk_classification.get('level', 'MEDIUM')

        if comprehensive_score >= 80:
            if risk_level in ['LOW', 'MEDIUM']:
                return "VERY_STRONG_BUY"
            else:
                return "STRONG_BUY"
        elif comprehensive_score >= 65:
            return "STRONG_BUY"
        elif comprehensive_score >= 45:
            return "BUY"
        elif comprehensive_score >= 25:
            return "WEAK_BUY"
        else:
            return "HOLD"

    def _calculate_entry_points(self, current_price, analysis_results):
        """حساب نقاط الدخول المثلى"""
        levels = analysis_results.get('levels', {})
        bb_lower = levels.get('bb_lower', current_price * 0.99)
        support_levels = levels.get('support', [])

        entry_points = {
            'immediate': current_price,
            'conservative': bb_lower,
            'support_levels': support_levels[:3] if support_levels else [current_price * 0.98]
        }

        return entry_points

    def _calculate_exit_points(self, current_price, analysis_results):
        """حساب نقاط الخروج المثلى"""
        levels = analysis_results.get('levels', {})
        bb_upper = levels.get('bb_upper', current_price * 1.01)
        resistance_levels = levels.get('resistance', [])

        custom = analysis_results.get('custom', {})
        smart_momentum = custom.get('smart_momentum', 0)

        # تحديد أهداف متدرجة
        if abs(smart_momentum) > 30:
            targets = [current_price * 1.015, current_price * 1.025, current_price * 1.04]
        elif abs(smart_momentum) > 15:
            targets = [current_price * 1.01, current_price * 1.02, current_price * 1.03]
        else:
            targets = [current_price * 1.005, current_price * 1.01, current_price * 1.015]

        exit_points = {
            'targets': targets,
            'resistance_levels': resistance_levels[:3] if resistance_levels else targets,
            'bb_upper': bb_upper,
            'stop_loss': current_price * 0.98
        }

        return exit_points

    def _calculate_optimal_position_size(self, risk_classification, success_probability):
        """حساب حجم المركز الأمثل"""
        risk_level = risk_classification.get('level', 'MEDIUM')

        base_size = 1.0

        # تعديل حسب مستوى المخاطر
        if risk_level == 'LOW':
            risk_multiplier = 1.5
        elif risk_level == 'MEDIUM':
            risk_multiplier = 1.0
        elif risk_level == 'HIGH':
            risk_multiplier = 0.7
        else:  # EXTREME
            risk_multiplier = 0.4

        # تعديل حسب احتمالية النجاح
        if success_probability > 80:
            confidence_multiplier = 1.3
        elif success_probability > 60:
            confidence_multiplier = 1.1
        elif success_probability < 40:
            confidence_multiplier = 0.8
        else:
            confidence_multiplier = 1.0

        optimal_size = base_size * risk_multiplier * confidence_multiplier
        return max(0.2, min(2.0, optimal_size))

    def _identify_market_phase(self, analysis_results):
        """تحديد مرحلة السوق"""
        custom = analysis_results.get('custom', {})
        traditional = analysis_results.get('traditional', {})

        market_sentiment = custom.get('market_sentiment', 50)
        volatility = traditional.get('volatility', 0.02)
        breakout_type = custom.get('breakout_type', 'NONE')

        if breakout_type != 'NONE':
            return "BREAKOUT_PHASE"
        elif market_sentiment > 70:
            return "BULLISH_PHASE"
        elif market_sentiment < 30:
            return "BEARISH_PHASE"
        elif volatility < 0.01:
            return "CONSOLIDATION_PHASE"
        elif volatility > 0.05:
            return "VOLATILE_PHASE"
        else:
            return "NEUTRAL_PHASE"

    def _calculate_trend_strength(self, analysis_results):
        """حساب قوة الاتجاه"""
        timeframes = analysis_results.get('timeframes', {})
        custom = analysis_results.get('custom', {})

        smart_momentum = abs(custom.get('smart_momentum', 0))

        # حساب اتفاق الإطارات الزمنية
        alignment_score = 0
        total_timeframes = len(timeframes)

        if total_timeframes > 0:
            for tf_data in timeframes.values():
                if tf_data.get('strength', 0) > 2:
                    alignment_score += 1

            alignment_ratio = alignment_score / total_timeframes
        else:
            alignment_ratio = 0.5

        # دمج الزخم والاتفاق
        if smart_momentum > 30 and alignment_ratio > 0.6:
            return "VERY_STRONG"
        elif smart_momentum > 20 and alignment_ratio > 0.5:
            return "STRONG"
        elif smart_momentum > 10 or alignment_ratio > 0.3:
            return "MODERATE"
        else:
            return "WEAK"

class EnhancedUltimateScalper:
    """المضارب النهائي المحسن"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 10  # زيادة timeout
        self.price_history = defaultdict(lambda: deque(maxlen=100))  # تخزين أكثر كفاءة
        self.volume_history = defaultdict(lambda: deque(maxlen=50))
        self.opportunities = []
        self.hunting = False
        self.tradeable_symbols = set()  # كاش للعملات القابلة للتداول
        self.last_tradeable_check = 0
        self.analysis_engine = AdvancedAnalysisEngine()
        
        print("🚀 Enhanced Ultimate Scalper initialized")
        print("🔧 Loading tradeable symbols...")
        self._update_tradeable_symbols()
    
    def _sign_request(self, timestamp, method, endpoint, body=""):
        """توقيع الطلب"""
        message = timestamp + method.upper() + endpoint + body
        signature = base64.b64encode(
            hmac.new(SECRET_KEY.encode(), message.encode(), hashlib.sha256).digest()
        ).decode()
        
        passphrase = base64.b64encode(
            hmac.new(SECRET_KEY.encode(), PASSPHRASE.encode(), hashlib.sha256).digest()
        ).decode()
        
        return {
            "KC-API-KEY": API_KEY,
            "KC-API-SIGN": signature,
            "KC-API-TIMESTAMP": timestamp,
            "KC-API-PASSPHRASE": passphrase,
            "KC-API-KEY-VERSION": "2",
            "Content-Type": "application/json"
        }
    
    def _api_call(self, method, endpoint, data=None, signed=False, retries=3):
        """استدعاء API محسن مع إعادة المحاولة"""
        url = BASE_URL + endpoint
        headers = {}
        
        if signed:
            timestamp = str(int(time.time() * 1000))
            body = json.dumps(data) if data else ""
            headers = self._sign_request(timestamp, method, endpoint, body)
            data = body if body else None
        
        for attempt in range(retries):
            try:
                response = self.session.request(method, url, data=data, headers=headers)
                result = response.json()
                
                if response.status_code == 200 and result.get("code") == "200000":
                    return result.get("data")
                elif response.status_code == 429:  # Rate limit
                    time.sleep(1)
                    continue
                else:
                    return None
            except Exception as e:
                if attempt == retries - 1:
                    print(f"❌ API call failed after {retries} attempts: {e}")
                    return None
                time.sleep(0.5)
        
        return None
    
    def _update_tradeable_symbols(self):
        """تحديث قائمة العملات القابلة للتداول"""
        current_time = time.time()
        
        # تحديث كل 5 دقائق
        if current_time - self.last_tradeable_check < 300:
            return
        
        print("🔄 Updating tradeable symbols...")
        
        # الحصول على جميع الرموز
        symbols_data = self._api_call("GET", "/api/v1/symbols")
        if not symbols_data:
            print("❌ Failed to get symbols data")
            return
        
        tradeable_count = 0
        for symbol_info in symbols_data:
            symbol = symbol_info.get("symbol", "")
            
            # فلترة العملات
            if (symbol.endswith("-USDT") and 
                symbol_info.get("enableTrading", False) and
                not any(stable in symbol for stable in ["USDT", "USDC", "BUSD", "DAI", "TUSD", "FDUSD"])):
                
                self.tradeable_symbols.add(symbol)
                tradeable_count += 1
        
        self.last_tradeable_check = current_time
        print(f"✅ Found {tradeable_count} tradeable symbols")
    
    def get_price(self, symbol):
        """الحصول على السعر مع معالجة أخطاء محسنة"""
        try:
            ticker = self._api_call("GET", f"/api/v1/market/stats?symbol={symbol}")
            if ticker and "last" in ticker:
                price = float(ticker["last"])
                if price > 0:
                    return price
        except (ValueError, TypeError):
            pass
        return None
    
    def get_balance(self, currency="USDT"):
        """الحصول على الرصيد"""
        accounts = self._api_call("GET", "/api/v1/accounts", signed=True)
        if accounts:
            for account in accounts:
                if account["currency"] == currency and account["type"] == "trade":
                    return float(account["available"])
        return 0.0
    
    def get_all_balances(self):
        """الحصول على جميع الأرصدة"""
        accounts = self._api_call("GET", "/api/v1/accounts", signed=True)
        balances = {}
        
        if accounts:
            for account in accounts:
                if account["type"] == "trade" and float(account["available"]) > 0:
                    balances[account["currency"]] = float(account["available"])
        
        return balances
    
    def auto_convert_to_usdt(self, target_amount=20, exclude_currencies=None):
        """تحويل العملات تلقائياً إلى USDT"""
        if exclude_currencies is None:
            exclude_currencies = ["USDT", "BTC", "ETH"]  # عملات لا نريد بيعها
        
        print(f"🔄 Converting assets to USDT (target: ${target_amount})")
        
        balances = self.get_all_balances()
        total_converted = 0
        
        for currency, amount in balances.items():
            if currency in exclude_currencies:
                continue
            
            if total_converted >= target_amount:
                break
            
            symbol = f"{currency}-USDT"
            price = self.get_price(symbol)
            
            if price and price > 0:
                value = amount * price
                
                if value > 1:  # فقط العملات التي تستحق أكثر من $1
                    print(f"💰 Converting {amount:.4f} {currency} (${value:.2f}) to USDT")
                    
                    sell_order = self.sell_market(symbol, amount * 0.99)  # بيع 99% لتجنب مشاكل الرصيد
                    
                    if sell_order:
                        total_converted += value
                        print(f"✅ Converted ${value:.2f} | Total: ${total_converted:.2f}")
                        time.sleep(2)  # انتظار بين الصفقات
        
        print(f"🎯 Total converted: ${total_converted:.2f}")
        return total_converted

    def buy_market(self, symbol, amount):
        """شراء بسعر السوق"""
        data = {
            "clientOid": f"enhanced_buy_{int(time.time())}",
            "symbol": symbol,
            "side": "buy",
            "type": "market",
            "funds": str(amount)
        }

        result = self._api_call("POST", "/api/v1/orders", data=data, signed=True)
        return result.get('orderId') if result else None

    def sell_market(self, symbol, amount):
        """بيع بسعر السوق"""
        data = {
            "clientOid": f"enhanced_sell_{int(time.time())}",
            "symbol": symbol,
            "side": "sell",
            "type": "market",
            "size": str(amount)
        }

        result = self._api_call("POST", "/api/v1/orders", data=data, signed=True)
        return result.get('orderId') if result else None

    def enhanced_analyze_coin(self, symbol, ticker_data):
        """تحليل محسن للعملة باستخدام محرك التحليل المتقدم"""
        try:
            # استخدام محرك التحليل المتقدم
            advanced_analysis = self.analysis_engine.comprehensive_analysis(symbol, ticker_data)

            # تحويل النتائج للتوافق مع النظام القديم
            return {
                'symbol': symbol,
                'signal_type': advanced_analysis.get('signal_type', 'HOLD'),
                'score': advanced_analysis.get('comprehensive_score', 50),
                'confidence': advanced_analysis.get('success_probability', 50),
                'current_price': advanced_analysis.get('current_price', 0),
                'change_24h': advanced_analysis.get('change_24h', 0),
                'volume_24h': advanced_analysis.get('volume_24h', 0),
                'risk_level': advanced_analysis.get('risk_classification', {}).get('level', 'MEDIUM'),
                'signals': [advanced_analysis.get('best_strategy', {}).get('description', 'No strategy')],
                'reasoning': f"{advanced_analysis.get('market_phase', 'Unknown')} | {advanced_analysis.get('trend_strength', 'Unknown')} | {advanced_analysis.get('momentum_direction', 'Neutral')}",
                'timestamp': advanced_analysis.get('timestamp', datetime.now().strftime("%H:%M:%S")),

                # معلومات متقدمة إضافية
                'advanced_analysis': advanced_analysis,
                'success_probability': advanced_analysis.get('success_probability', 50),
                'best_strategy': advanced_analysis.get('best_strategy', {}),
                'entry_points': advanced_analysis.get('entry_points', {}),
                'exit_points': advanced_analysis.get('exit_points', {}),
                'position_size': advanced_analysis.get('position_size', 1.0)
            }

        except Exception as e:
            print(f"❌ Advanced analysis error for {symbol}: {e}")
            return None

    def enhanced_market_scan(self):
        """مسح محسن للسوق مع فلترة العملات القابلة للتداول"""
        print("🔍 Enhanced market scanning...")

        # تحديث العملات القابلة للتداول
        self._update_tradeable_symbols()

        # الحصول على جميع البيانات
        all_tickers = self._api_call("GET", "/api/v1/market/allTickers")

        if not all_tickers or "ticker" not in all_tickers:
            print("❌ Failed to get market data")
            return []

        opportunities = []
        processed_count = 0

        for ticker in all_tickers["ticker"]:
            symbol = ticker["symbol"]

            # فلترة العملات القابلة للتداول فقط
            if symbol not in self.tradeable_symbols:
                continue

            processed_count += 1

            # تحليل العملة
            analysis = self.enhanced_analyze_coin(symbol, ticker)
            if analysis and analysis['score'] >= 25:  # حد أدنى للنقاط
                opportunities.append(analysis)

        # ترتيب حسب النقاط
        opportunities.sort(key=lambda x: x['score'], reverse=True)

        print(f"\n📊 ENHANCED MARKET SCAN ({processed_count} tradeable coins analyzed):")
        print("=" * 120)
        print(f"{'#':<3} {'Symbol':<15} {'Signal':<16} {'Score':<5} {'Price':<12} {'Change':<8} {'Volume':<10} {'Risk':<10} {'Reasoning'}")
        print("=" * 120)

        for i, opp in enumerate(opportunities[:25]):  # أفضل 25
            volume_k = opp['volume_24h'] / 1000
            print(f"{i+1:<3} {opp['symbol']:<15} {opp['signal_type']:<16} "
                  f"{opp['score']:<5} ${opp['current_price']:<11.6f} "
                  f"{opp['change_24h']:+7.2f}% {volume_k:>8.0f}K {opp['risk_level']:<10} {opp['reasoning'][:40]}")

        self.opportunities = opportunities
        return opportunities

    def multi_level_scalping(self, symbol, base_amount, analysis=None):
        """مضاربة متعددة المستويات مع إدارة وقت ذكية"""
        print(f"⚡ Smart multi-level scalping {symbol} with ${base_amount}")

        if analysis:
            print(f"📊 Analysis: {analysis['signal_type']} (Score: {analysis['score']}) - {analysis['reasoning']}")

        # الحصول على السعر الحالي
        current_price = self.get_price(symbol)
        if not current_price:
            print("❌ Cannot get current price")
            return False

        # التحقق من الرصيد
        usdt_balance = self.get_balance("USDT")
        if usdt_balance < base_amount:
            print(f"❌ Insufficient USDT: ${usdt_balance:.2f}")
            return False

        # تحديد استراتيجية المضاربة حسب التحليل (بدون timeout)
        if analysis:
            if analysis['score'] >= 80:
                # مضاربة عدوانية للفرص القوية جداً
                levels = [
                    {"amount": base_amount * 0.4, "target": 0.015, "stop": 0.006},   # 1.5% هدف
                    {"amount": base_amount * 0.3, "target": 0.010, "stop": 0.004},   # 1.0% هدف
                    {"amount": base_amount * 0.3, "target": 0.006, "stop": 0.003}    # 0.6% هدف
                ]
            elif analysis['score'] >= 60:
                # مضاربة متوسطة
                levels = [
                    {"amount": base_amount * 0.5, "target": 0.010, "stop": 0.004},
                    {"amount": base_amount * 0.5, "target": 0.006, "stop": 0.003}
                ]
            else:
                # مضاربة محافظة
                levels = [
                    {"amount": base_amount, "target": 0.005, "stop": 0.0025}
                ]
        else:
            # استراتيجية افتراضية
            levels = [
                {"amount": base_amount, "target": 0.005, "stop": 0.0025}
            ]

        successful_levels = 0
        total_profit = 0
        max_loss_threshold = base_amount * 0.02  # حد أقصى للخسارة 2%

        for i, level in enumerate(levels):
            print(f"\n🎯 Level {i+1}: ${level['amount']:.2f} | Target: {level['target']*100:.1f}% | Stop: {level['stop']*100:.1f}%")

            # تنفيذ المضاربة لهذا المستوى (بدون timeout)
            result = self._execute_smart_scalp_level(symbol, level['amount'], level['target'], level['stop'], analysis)

            if result['success']:
                successful_levels += 1
                total_profit += result['profit']
                print(f"✅ Level {i+1} successful! Profit: ${result['profit']:.3f}")
            else:
                total_profit += result['profit']  # إضافة الخسارة
                print(f"❌ Level {i+1} failed. Loss: ${abs(result['profit']):.3f}")

                # فحص حد الخسارة الأقصى
                if abs(total_profit) >= max_loss_threshold:
                    print(f"🛑 Maximum loss threshold reached (${abs(total_profit):.3f}). Stopping all levels.")
                    break

                # إيقاف المضاربة إذا كانت الخسارة كبيرة نسبياً
                if abs(result['profit']) >= level['amount'] * 0.015:  # خسارة أكثر من 1.5%
                    print("🛑 Significant loss detected. Stopping multi-level scalping.")
                    break

            # انتظار قصير بين المستويات
            if i < len(levels) - 1:
                print("⏳ Waiting before next level...")
                time.sleep(5)

        print(f"\n📊 Smart multi-level scalping completed:")
        print(f"   Successful levels: {successful_levels}/{len(levels)}")
        print(f"   Total profit: ${total_profit:.3f}")
        print(f"   Success rate: {(successful_levels/len(levels))*100:.1f}%")
        print(f"   ROI: {(total_profit/base_amount)*100:.2f}%")

        return successful_levels > 0

    def _execute_smart_scalp_level(self, symbol, amount, profit_target, stop_loss, analysis=None):
        """تنفيذ مضاربة ذكية بدون قيود زمنية"""
        start_time = time.time()

        # الحصول على السعر الحالي
        entry_price = self.get_price(symbol)
        if not entry_price:
            return {"success": False, "profit": 0, "reason": "Cannot get price"}

        # تنفيذ أمر الشراء
        buy_order = self.buy_market(symbol, amount)
        if not buy_order:
            return {"success": False, "profit": 0, "reason": "Buy order failed"}

        print(f"✅ Smart position opened at ${entry_price:.6f} | Order: {buy_order}")

        # انتظار تنفيذ الأمر
        time.sleep(3)

        # حساب كمية العملة المشتراة
        crypto_amount = amount / entry_price

        # تحديد الأهداف
        target_price = entry_price * (1 + profit_target)
        initial_stop = entry_price * (1 - stop_loss)
        max_loss_price = entry_price * (1 - 0.02)  # حد أقصى للخسارة 2%

        print(f"🎯 Target: ${target_price:.6f} | Initial Stop: ${initial_stop:.6f} | Max Loss: ${max_loss_price:.6f}")

        # متغيرات التتبع الذكية
        best_price = entry_price
        trailing_stop = initial_stop
        price_history = deque(maxlen=20)  # آخر 20 سعر
        volume_history = deque(maxlen=10)  # آخر 10 أحجام
        stagnation_count = 0  # عداد الركود
        api_error_count = 0  # عداد أخطاء API

        print("🧠 Smart scalping started - monitoring market conditions...")

        # حلقة المضاربة الذكية (بدون timeout)
        while True:
            current_price = self.get_price(symbol)

            # فحص اتصال API
            if not current_price:
                api_error_count += 1
                if api_error_count >= 5:
                    print(f"\n❌ API connection issues detected. Emergency exit.")
                    break
                time.sleep(2)
                continue
            else:
                api_error_count = 0  # إعادة تعيين العداد

            # تحديث التاريخ
            price_history.append(current_price)

            # الحصول على بيانات الحجم
            ticker = self._api_call("GET", f"/api/v1/market/stats?symbol={symbol}")
            if ticker:
                current_volume = float(ticker.get('vol', 0))
                volume_history.append(current_volume)

            # تحديث أفضل سعر و trailing stop الذكي
            if current_price > best_price:
                best_price = current_price
                # trailing stop ديناميكي يعتمد على التقلبات
                if len(price_history) >= 10:
                    recent_volatility = (max(price_history) - min(price_history)) / min(price_history)
                    dynamic_stop_distance = max(stop_loss, recent_volatility * 0.5)
                else:
                    dynamic_stop_distance = stop_loss

                new_trailing = best_price * (1 - dynamic_stop_distance)
                if new_trailing > trailing_stop:
                    trailing_stop = new_trailing

            # حساب المؤشرات الحية
            profit_pct = ((current_price - entry_price) / entry_price) * 100
            elapsed_minutes = (time.time() - start_time) / 60

            # فحص الركود (السعر لا يتحرك)
            if len(price_history) >= 10:
                recent_range = max(price_history[-10:]) - min(price_history[-10:])
                if recent_range / entry_price < 0.001:  # أقل من 0.1% حركة
                    stagnation_count += 1
                else:
                    stagnation_count = 0

            # عرض التقدم الذكي
            print(f"🧠 ${current_price:.6f} | P&L: {profit_pct:+.2f}% | Best: ${best_price:.6f} | Trail: ${trailing_stop:.6f} | {elapsed_minutes:.1f}m", end="\r")

            # شروط الخروج الذكية

            # 1. تحقيق الهدف
            if current_price >= target_price:
                print(f"\n🎉 TARGET ACHIEVED! Selling at ${current_price:.6f}")
                sell_order = self.sell_market(symbol, crypto_amount * 0.995)
                if sell_order:
                    profit = (current_price - entry_price) * crypto_amount
                    return {"success": True, "profit": profit, "reason": "Target achieved"}
                break

            # 2. Trailing stop
            elif current_price <= trailing_stop:
                print(f"\n🛑 SMART TRAILING STOP! Selling at ${current_price:.6f}")
                sell_order = self.sell_market(symbol, crypto_amount * 0.995)
                if sell_order:
                    profit = (current_price - entry_price) * crypto_amount
                    return {"success": profit > 0, "profit": profit, "reason": "Smart trailing stop"}
                break

            # 3. حد الخسارة الأقصى
            elif current_price <= max_loss_price:
                print(f"\n🚨 MAXIMUM LOSS LIMIT! Emergency exit at ${current_price:.6f}")
                sell_order = self.sell_market(symbol, crypto_amount * 0.995)
                if sell_order:
                    profit = (current_price - entry_price) * crypto_amount
                    return {"success": False, "profit": profit, "reason": "Maximum loss limit"}
                break

            # 4. ركود السوق المطول
            elif stagnation_count >= 20:  # 20 فحص متتالي بدون حركة
                print(f"\n😴 MARKET STAGNATION detected. Exiting at ${current_price:.6f}")
                sell_order = self.sell_market(symbol, crypto_amount * 0.995)
                if sell_order:
                    profit = (current_price - entry_price) * crypto_amount
                    return {"success": profit > 0, "profit": profit, "reason": "Market stagnation"}
                break

            # 5. انهيار الحجم المفاجئ
            elif len(volume_history) >= 5:
                avg_volume = sum(volume_history) / len(volume_history)
                current_volume = volume_history[-1] if volume_history else 0
                if current_volume < avg_volume * 0.3:  # انخفاض الحجم أكثر من 70%
                    print(f"\n📉 VOLUME COLLAPSE detected. Exiting at ${current_price:.6f}")
                    sell_order = self.sell_market(symbol, crypto_amount * 0.995)
                    if sell_order:
                        profit = (current_price - entry_price) * crypto_amount
                        return {"success": profit > 0, "profit": profit, "reason": "Volume collapse"}
                    break

            # انتظار ذكي (أقل في الأسواق المتقلبة)
            if len(price_history) >= 5:
                recent_volatility = (max(price_history[-5:]) - min(price_history[-5:])) / min(price_history[-5:])
                if recent_volatility > 0.005:  # سوق متقلب
                    time.sleep(1)  # فحص كل ثانية
                else:
                    time.sleep(3)  # فحص كل 3 ثوان
            else:
                time.sleep(2)

        # خروج اضطراري
        print(f"\n⚠️ Emergency exit triggered")
        final_price = self.get_price(symbol)
        sell_order = self.sell_market(symbol, crypto_amount * 0.995)

        if sell_order and final_price:
            profit = (final_price - entry_price) * crypto_amount
            return {"success": profit > 0, "profit": profit, "reason": "Emergency exit"}

        return {"success": False, "profit": 0, "reason": "Failed to sell"}

    def smart_auto_hunt(self, amount_per_trade=15, max_trades=5):
        """صيد ذكي تلقائي مع إدارة وقت ذكية"""
        print(f"🎯 Starting smart auto hunt with intelligent time management")
        print(f"💰 ${amount_per_trade} per trade | Max {max_trades} trades | No time limits per trade")

        self.hunting = True
        trades_executed = 0
        successful_trades = 0
        total_profit = 0
        start_hunt_time = time.time()

        while self.hunting and trades_executed < max_trades:
            try:
                # مسح السوق
                print(f"\n🔍 Scanning market for opportunities... (Trade {trades_executed + 1}/{max_trades})")
                opportunities = self.enhanced_market_scan()

                # فلترة الفرص عالية الجودة
                high_quality_opps = [opp for opp in opportunities if opp['score'] >= 70]

                if high_quality_opps:
                    best_opp = high_quality_opps[0]
                    print(f"\n🚀 Smart auto-trading: {best_opp['symbol']} (Score: {best_opp['score']})")
                    print(f"📊 Signal: {best_opp['signal_type']} | Risk: {best_opp['risk_level']} | Change: {best_opp['change_24h']:+.2f}%")

                    # تنفيذ المضاربة الذكية (بدون قيود زمنية)
                    trade_start = time.time()
                    success = self.multi_level_scalping(best_opp['symbol'], amount_per_trade, best_opp)
                    trade_duration = (time.time() - trade_start) / 60

                    trades_executed += 1

                    if success:
                        successful_trades += 1
                        print(f"✅ Smart auto-trade #{trades_executed} successful! Duration: {trade_duration:.1f} minutes")
                    else:
                        print(f"❌ Smart auto-trade #{trades_executed} failed. Duration: {trade_duration:.1f} minutes")

                    # إحصائيات مؤقتة
                    current_success_rate = (successful_trades / trades_executed * 100)
                    hunt_duration = (time.time() - start_hunt_time) / 60
                    print(f"📊 Current stats: {successful_trades}/{trades_executed} successful ({current_success_rate:.1f}%) | Hunt duration: {hunt_duration:.1f}m")

                    # انتظار قصير بين الصفقات (تقليل الانتظار)
                    if self.hunting and trades_executed < max_trades:
                        print("⏳ Brief pause before next opportunity scan...")
                        time.sleep(30)  # انتظار 30 ثانية فقط

                else:
                    print("⏳ No high-quality opportunities found. Continuing scan...")
                    time.sleep(15)  # انتظار أقل للمسح المستمر

            except KeyboardInterrupt:
                self.hunting = False
                print("\n🛑 Smart auto-hunt interrupted by user")
                break
            except Exception as e:
                print(f"❌ Smart auto-hunt error: {e}")
                print("🔄 Continuing hunt after brief pause...")
                time.sleep(20)

        # إحصائيات نهائية
        total_hunt_duration = (time.time() - start_hunt_time) / 60
        success_rate = (successful_trades / trades_executed * 100) if trades_executed > 0 else 0
        avg_trade_time = total_hunt_duration / trades_executed if trades_executed > 0 else 0

        print(f"\n📊 Smart auto-hunt completed:")
        print(f"   Total duration: {total_hunt_duration:.1f} minutes")
        print(f"   Trades executed: {trades_executed}")
        print(f"   Successful trades: {successful_trades}")
        print(f"   Success rate: {success_rate:.1f}%")
        print(f"   Average time per trade: {avg_trade_time:.1f} minutes")
        print(f"   Total profit: ${total_profit:.2f}")

        if trades_executed > 0:
            print(f"   Efficiency: {trades_executed/total_hunt_duration:.2f} trades per minute")

        self.hunting = False

    def stop_hunting(self):
        """إيقاف الصيد"""
        self.hunting = False
        print("🛑 Hunting stopped")

def main():
    """الواجهة الرئيسية المحسنة"""
    scalper = EnhancedUltimateScalper()

    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🚀 المضارب النهائي المحسن - تحليل فني متقدم          ║
    ║           Enhanced Ultimate Scalper v4.0 - Advanced TA      ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝

    🆕 التحسينات الجديدة v4.0 - نظام التحليل الفني المتقدم:
    ✅ مؤشرات فنية مخصصة (Smart Momentum, Market Sentiment)
    ✅ Volume-Weighted RSI و Adaptive MACD
    ✅ مؤشر Volatility Breakout لكشف الاختراقات
    ✅ نطاقات بولينجر ديناميكية متكيفة
    ✅ نظام دعم/مقاومة ذكي تلقائي
    ✅ تحليل متعدد الإطارات الزمنية
    ✅ نظام تقييم شامل (0-100 نقطة)
    ✅ تصنيف مخاطر ذكي (LOW/MEDIUM/HIGH/EXTREME)
    ✅ حساب احتمالية النجاح قبل التنفيذ
    ✅ تحديد أفضل استراتيجية تلقائياً

    🔧 الميزات السابقة v3.0:
    ✅ إدارة وقت ذكية بدون قيود زمنية
    ✅ مراقبة التقلبات والحجم الحية
    ✅ آليات أمان متقدمة
    ✅ Trailing stop ديناميكي ذكي

    الأوامر المتقدمة:
    1. scan                     - مسح متقدم مع تحليل فني شامل
    2. analyze <SYMBOL>         - تحليل فني متقدم لعملة محددة
    3. scalp <SYMBOL> <AMOUNT>  - مضاربة ذكية مع تحليل متقدم
    4. hunt <AMOUNT>            - صيد ذكي مع تحليل فني متقدم
    5. strategies               - عرض أداء الاستراتيجيات
    6. convert <AMOUNT>         - تحويل العملات إلى USDT
    7. balance <CURRENCY>       - فحص الرصيد
    8. balances                 - جميع الأرصدة
    9. price <SYMBOL>           - سعر العملة
    10. stop                    - إيقاف الصيد
    11. quit                    - خروج

    أمثلة متقدمة:
    analyze BTC-USDT     - تحليل فني شامل للبيتكوين
    scalp ETH-USDT 20    - مضاربة ذكية مع تحليل متقدم
    hunt 15              - صيد ذكي مع احتمالية نجاح >70%
    strategies           - عرض أداء جميع الاستراتيجيات

    🧠 نظام التحليل المتقدم يشمل:
    • 12+ مؤشر فني متقدم ومخصص
    • تحليل متعدد الطبقات والإطارات الزمنية
    • تقييم شامل من 0-100 نقطة
    • حساب احتمالية النجاح قبل التنفيذ
    • تحديد أفضل استراتيجية تلقائياً
    • نقاط دخول وخروج محسوبة بدقة
    """)

    while True:
        try:
            command = input("\n🚀 Enhanced Command: ").strip().split()

            if not command:
                continue

            cmd = command[0].lower()

            if cmd == "quit":
                scalper.stop_hunting()
                print("👋 Enhanced Ultimate Scalper stopped!")
                break

            elif cmd == "scan":
                scalper.enhanced_market_scan()

            elif cmd == "analyze":
                symbol = command[1].upper() if len(command) > 1 else "BTC-USDT"

                # الحصول على بيانات العملة
                ticker = scalper._api_call("GET", f"/api/v1/market/stats?symbol={symbol}")
                if ticker:
                    analysis = scalper.analysis_engine.comprehensive_analysis(symbol, ticker)

                    print(f"\n🔬 ADVANCED TECHNICAL ANALYSIS: {symbol}")
                    print("=" * 80)
                    print(f"💰 Price: ${analysis['current_price']:.6f} | Change: {analysis['change_24h']:+.2f}%")
                    print(f"📊 Comprehensive Score: {analysis['comprehensive_score']}/100")
                    print(f"🎯 Success Probability: {analysis['success_probability']:.1f}%")
                    print(f"⚠️  Risk Level: {analysis['risk_classification']['level']}")
                    print(f"📈 Market Phase: {analysis['market_phase']}")
                    print(f"💪 Trend Strength: {analysis['trend_strength']}")
                    print(f"🔄 Momentum: {analysis['momentum_direction']}")

                    # عرض الاستراتيجية المقترحة
                    strategy = analysis['best_strategy']
                    print(f"\n🎯 RECOMMENDED STRATEGY:")
                    print(f"   Strategy: {strategy['name']}")
                    print(f"   Confidence: {strategy['confidence']}%")
                    print(f"   Description: {strategy['description']}")

                    # عرض نقاط الدخول والخروج
                    entry_points = analysis['entry_points']
                    exit_points = analysis['exit_points']

                    print(f"\n📍 ENTRY POINTS:")
                    print(f"   Immediate: ${entry_points['immediate']:.6f}")
                    print(f"   Conservative: ${entry_points['conservative']:.6f}")

                    print(f"\n🎯 EXIT TARGETS:")
                    for i, target in enumerate(exit_points['targets'][:3], 1):
                        print(f"   Target {i}: ${target:.6f}")

                    print(f"\n📊 TECHNICAL INDICATORS:")
                    indicators = analysis['indicators']

                    # المؤشرات التقليدية
                    traditional = indicators.get('traditional', {})
                    print(f"   RSI: {traditional.get('rsi', 0):.1f}")
                    print(f"   Volume-Weighted RSI: {traditional.get('volume_weighted_rsi', 0):.1f}")
                    print(f"   Volatility: {traditional.get('volatility', 0)*100:.2f}%")

                    # المؤشرات المخصصة
                    custom = indicators.get('custom', {})
                    print(f"   Smart Momentum: {custom.get('smart_momentum', 0):.1f}")
                    print(f"   Market Sentiment: {custom.get('market_sentiment', 0):.1f}")
                    print(f"   Breakout Type: {custom.get('breakout_type', 'NONE')}")

                    # مستويات الدعم والمقاومة
                    levels = indicators.get('levels', {})
                    print(f"   Bollinger Position: {levels.get('bb_position', 0)*100:.1f}%")

                    resistance = levels.get('resistance', [])
                    support = levels.get('support', [])

                    if resistance:
                        print(f"   Resistance Levels: {', '.join([f'${r:.6f}' for r in resistance[:3]])}")
                    if support:
                        print(f"   Support Levels: {', '.join([f'${s:.6f}' for s in support[:3]])}")

                    # تحليل متعدد الإطارات
                    timeframes = indicators.get('timeframes', {})
                    if timeframes:
                        print(f"\n⏰ MULTI-TIMEFRAME ANALYSIS:")
                        for tf_name, tf_data in timeframes.items():
                            print(f"   {tf_name.capitalize()}: {tf_data['direction']} (Strength: {tf_data['strength']:.1f}%)")

                else:
                    print(f"❌ Cannot get data for {symbol}")

            elif cmd == "convert":
                amount = float(command[1]) if len(command) > 1 else 20
                scalper.auto_convert_to_usdt(amount)

            elif cmd == "scalp":
                symbol = command[1].upper() if len(command) > 1 else "BTC-USDT"
                amount = float(command[2]) if len(command) > 2 else 10

                # الحصول على التحليل أولاً
                ticker = scalper._api_call("GET", f"/api/v1/market/stats?symbol={symbol}")
                analysis = None
                if ticker:
                    analysis = scalper.enhanced_analyze_coin(symbol, ticker)

                scalper.multi_level_scalping(symbol, amount, analysis)

            elif cmd == "hunt":
                amount = float(command[1]) if len(command) > 1 else 15
                max_trades = int(command[2]) if len(command) > 2 else 5

                hunting_thread = threading.Thread(
                    target=scalper.smart_auto_hunt,
                    args=(amount, max_trades),
                    daemon=True
                )
                hunting_thread.start()
                print(f"🎯 Smart auto-hunting started: ${amount} per trade, max {max_trades} trades")

            elif cmd == "balance":
                currency = command[1].upper() if len(command) > 1 else "USDT"
                balance = scalper.get_balance(currency)
                print(f"💰 {currency}: {balance}")

            elif cmd == "balances":
                balances = scalper.get_all_balances()
                print("\n💰 All Balances:")
                total_value = 0

                for currency, amount in balances.items():
                    if currency == "USDT":
                        value = amount
                    else:
                        price = scalper.get_price(f"{currency}-USDT")
                        value = amount * price if price else 0

                    total_value += value
                    print(f"   {currency}: {amount:.6f} (${value:.2f})")

                print(f"   Total Value: ${total_value:.2f}")

            elif cmd == "price":
                symbol = command[1].upper() if len(command) > 1 else "BTC-USDT"
                price = scalper.get_price(symbol)
                if price:
                    print(f"💰 {symbol}: ${price}")
                else:
                    print(f"❌ Cannot get price for {symbol}")

            elif cmd == "strategies":
                print("\n📊 STRATEGY PERFORMANCE ANALYSIS:")
                print("=" * 80)

                performance_data = scalper.analysis_engine.strategy_performance

                if performance_data:
                    for symbol, perf in list(performance_data.items())[:10]:  # Top 10
                        if perf['total_trades'] > 0:
                            print(f"{symbol:<15} | Trades: {perf['total_trades']:<3} | "
                                  f"Success: {perf['success_rate']:<5.1f}% | "
                                  f"Avg Profit: ${perf['avg_profit']:<6.3f} | "
                                  f"Total: ${perf['total_profit']:<7.2f}")
                else:
                    print("No strategy performance data available yet.")
                    print("Start trading to collect performance statistics.")

            elif cmd == "stop":
                scalper.stop_hunting()

            else:
                print("❌ Invalid command. Type 'quit' to exit.")

        except KeyboardInterrupt:
            scalper.stop_hunting()
            print("\n👋 Enhanced Ultimate Scalper stopped!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
